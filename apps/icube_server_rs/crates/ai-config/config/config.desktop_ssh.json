{"default": {"api": {"host": "https://copilot-cn.bytedance.net", "hostDCDN": "https://copilot-cn.bytedance.net", "region": "cn", "appId": "6eefa01c-1036-4c7e-9ca5-d891f63bfcd8", "practiceAppId": "850edec7-b9d0-48aa-99b5-67c888e282cd", "intentDetectAPI": "api/ide/v1/intent_detect", "contextSelectionAPI": "api/ide/v1/context_select", "queryRewriteAPI": "api/ide/v1/query_rewrite", "chatAPI": "api/ide/v1/chat", "authHeader": "x-ide-token", "appVersion": "default", "agentRunAPI": "api/ide/v1/agents/runs", "agentRunResultAPI": "api/ide/v1/agents/runs/:id/tool_call_outputs", "agentRunDiff": "api/ide/unstable/tools/diff", "practiceTitleAPI": "api/ide/v1/practice/generate_conversation_title", "getResourceUploadToken": "api/ide/v1/get_resource_upload_token", "getResourceUrl": "api/ide/v1/get_resource_url", "featuresAPI": "api/ide/v1/features", "fastApplyAPI": "api/ide/v1/fast_apply", "modelListAPI": "api/ide/v1/model_list", "fusionModelListAPI": "api/ide/v1/get_model_list", "fusionModelDetailAPI": "api/ide/v1/get_detail_param", "fusionRawChatAPI": "api/ide/v2/llm_raw_chat", "feedbackAPI": "api/ide/v1/feedback", "llmRawChatAPI": "api/ide/v1/llm_raw_chat", "getResourceUploadURLAPI": "api/ide/v1/get_resource_upload_url", "commitResourceUploadResultAPI": "api/ide/v1/commit_resource_upload_result", "cancelQueueTaskAPI": "api/ide/v1/cancel_queue_task", "modelConnectAPI": "api/ide/v1/connect", "chatPromptAPI": "api/ide/v1/chat_prompt", "updateModelAPI": "api/ide/v1/update_custom_model", "getProvidersAPI": "api/ide/v1/providers", "addModelAPI": "api/ide/v1/add_custom_model", "builderPromptAPI": "api/ide/v1/llm_raw_chat_prompt", "docsetCheckShouldUpdateAPI": "api/ide/v1/documentrag/official/check_should_update", "docsetLatestOfficialListAPI": "api/ide/v1/documentrag/official/latest_document_sets", "docsetCreateCustomDocsetIndexAPI": "api/ide/v1/documentrag/custom/index_document_set", "docsetDeleteCustomDocsetIndexAPI": "api/ide/v1/documentrag/custom/delete_document_set", "docsetGetCustomDocsetStatusAPI": "api/ide/v1/documentrag/custom/document_sets_status", "docsetRetrieveRagAPI": "api/ide/v1/documentrag/retrieve", "webSearchAPI": "api/ide/v1/web_search", "jumpQueueTaskAPI": "api/ide/v1/jump_queue_task", "getChatModeAPI": "api/v1/commercial/get_mode_info", "chatModeAPI": "api/v1/commercial/chat_mode", "saveCommercialStatusAPI": "api/v1/commercial/save_status", "pingAPI": "api/ide/v1/ping"}, "ckgConfig": {"host": "https://copilot-cn.bytedance.net", "hostDCDN": "https://copilot-cn.bytedance.net", "appId": "6eefa01c-1036-4c7e-9ca5-d891f63bfcd8", "storagePath": "/cloudide/workspace/.cloudide/data", "binaryPath": "binary", "sourceProduct": "native_ide", "port": 50051, "versionCode": "2", "knowledgebaseIdPrefix": "ckg_v2", "ideVersion": "1.84.1", "region": "cn", "enableLocalEmbedding": true, "ckgVersion": "0.0.98", "embeddingStorageType": "sqlite_vec"}, "versionCode": 20250812, "channel": "desktop", "featureGates": {"enableKeywordSearch": true, "enableUnitTest": true}, "slardarConfig": {"host": "https://mon.zijieapi.com", "bid": "trae_cn", "pcHost": "https://pc-mon.zijieapi.com", "pcAid": "787976"}, "teaConfig": {"host": "https://mcs.zijieapi.com", "appId": "711126"}, "ttNetConfig": {"appId": "787976", "httpDNS": "dig.bdurl.net", "netLog": "crash.snssdk.com", "domainBOE": ".boe-gateway.byted.org", "tncHost": "tnc3-bjlgy.zijieapi.com"}}, "sgInternal": {"api": {"host": "https://copilot-sg.byteintl.net", "hostDCDN": "https://copilot-sg.byteintl.net", "region": "sg", "appId": "6eefa01c-1036-4c7e-9ca5-d891f63bfcd8", "practiceAppId": "850edec7-b9d0-48aa-99b5-67c888e282cd", "intentDetectAPI": "api/ide/v1/intent_detect", "contextSelectionAPI": "api/ide/v1/context_select", "queryRewriteAPI": "api/ide/v1/query_rewrite", "chatAPI": "api/ide/v1/chat", "authHeader": "x-ide-token", "appVersion": "default", "agentRunAPI": "api/ide/v1/agents/runs", "agentRunResultAPI": "api/ide/v1/agents/runs/:id/tool_call_outputs", "agentRunDiff": "api/ide/unstable/tools/diff", "practiceTitleAPI": "api/ide/v1/practice/generate_conversation_title", "getResourceUploadToken": "api/ide/v1/get_resource_upload_token", "getResourceUrl": "api/ide/v1/get_resource_url", "featuresAPI": "api/ide/v1/features", "fastApplyAPI": "api/ide/v1/fast_apply", "modelListAPI": "api/ide/v1/model_list", "fusionModelListAPI": "api/ide/v1/get_model_list", "fusionModelDetailAPI": "api/ide/v1/get_detail_param", "fusionRawChatAPI": "api/ide/v2/llm_raw_chat", "feedbackAPI": "api/ide/v1/feedback", "llmRawChatAPI": "api/ide/v1/llm_raw_chat", "getResourceUploadURLAPI": "api/ide/v1/get_resource_upload_url", "commitResourceUploadResultAPI": "api/ide/v1/commit_resource_upload_result", "cancelQueueTaskAPI": "api/ide/v1/cancel_queue_task", "modelConnectAPI": "api/ide/v1/connect", "chatPromptAPI": "api/ide/v1/chat_prompt", "updateModelAPI": "api/ide/v1/update_custom_model", "getProvidersAPI": "api/ide/v1/providers", "addModelAPI": "api/ide/v1/add_custom_model", "builderPromptAPI": "api/ide/v1/llm_raw_chat_prompt", "docsetCheckShouldUpdateAPI": "api/ide/v1/documentrag/official/check_should_update", "docsetLatestOfficialListAPI": "api/ide/v1/documentrag/official/latest_document_sets", "docsetCreateCustomDocsetIndexAPI": "api/ide/v1/documentrag/custom/index_document_set", "docsetDeleteCustomDocsetIndexAPI": "api/ide/v1/documentrag/custom/delete_document_set", "docsetGetCustomDocsetStatusAPI": "api/ide/v1/documentrag/custom/document_sets_status", "docsetRetrieveRagAPI": "api/ide/v1/documentrag/retrieve", "webSearchAPI": "api/ide/v1/web_search", "jumpQueueTaskAPI": "api/ide/v1/jump_queue_task", "getChatModeAPI": "api/v1/commercial/get_mode_info", "chatModeAPI": "api/v1/commercial/chat_mode", "saveCommercialStatusAPI": "api/v1/commercial/save_status", "pingAPI": "api/ide/v1/ping"}, "ckgConfig": {"host": "https://copilot-sg.byteintl.net", "hostDCDN": "https://copilot-sg.byteintl.net", "appId": "6eefa01c-1036-4c7e-9ca5-d891f63bfcd8", "storagePath": "/cloudide/workspace/.cloudide/data", "binaryPath": "binary", "sourceProduct": "native_ide", "port": 50051, "versionCode": "2", "knowledgebaseIdPrefix": "ckg_v2", "ideVersion": "1.84.1", "region": "sg", "enableLocalEmbedding": true, "ckgVersion": "0.0.98", "embeddingStorageType": "sqlite_vec"}, "versionCode": 20250812, "channel": "desktop", "featureGates": {"enableKeywordSearch": false, "enableUnitTest": true}, "slardarConfig": {"host": "https://mon-va.byteoversea.com", "bid": "marscode_nativeide_us", "pcHost": "https://pc-mon-sg.byteintlapi.com", "pcAid": "787976"}, "teaConfig": {"host": "https://maliva-mcs.byteoversea.com", "appId": "677332"}, "ttNetConfig": {"appId": "787976", "httpDNS": "*************", "netLog": "ttnet-sg.byteoversea.com", "domainBOE": ".boe-gateway.byted.org", "tncHost": "tnc16-alisg.isnssdk.com"}}, "usInternal": {"api": {"host": "https://copilot.byteintl.net", "hostDCDN": "https://copilot.byteintl.net", "region": "us", "appId": "6eefa01c-1036-4c7e-9ca5-d891f63bfcd8", "practiceAppId": "850edec7-b9d0-48aa-99b5-67c888e282cd", "intentDetectAPI": "api/ide/v1/intent_detect", "contextSelectionAPI": "api/ide/v1/context_select", "queryRewriteAPI": "api/ide/v1/query_rewrite", "chatAPI": "api/ide/v1/chat", "authHeader": "x-ide-token", "appVersion": "default", "agentRunAPI": "api/ide/v1/agents/runs", "agentRunResultAPI": "api/ide/v1/agents/runs/:id/tool_call_outputs", "agentRunDiff": "api/ide/unstable/tools/diff", "practiceTitleAPI": "api/ide/v1/practice/generate_conversation_title", "getResourceUploadToken": "api/ide/v1/get_resource_upload_token", "getResourceUrl": "api/ide/v1/get_resource_url", "featuresAPI": "api/ide/v1/features", "fastApplyAPI": "api/ide/v1/fast_apply", "modelListAPI": "api/ide/v1/model_list", "fusionModelListAPI": "api/ide/v1/get_model_list", "fusionModelDetailAPI": "api/ide/v1/get_detail_param", "fusionRawChatAPI": "api/ide/v2/llm_raw_chat", "feedbackAPI": "api/ide/v1/feedback", "llmRawChatAPI": "api/ide/v1/llm_raw_chat", "getResourceUploadURLAPI": "api/ide/v1/get_resource_upload_url", "commitResourceUploadResultAPI": "api/ide/v1/commit_resource_upload_result", "cancelQueueTaskAPI": "api/ide/v1/cancel_queue_task", "modelConnectAPI": "api/ide/v1/connect", "chatPromptAPI": "api/ide/v1/chat_prompt", "updateModelAPI": "api/ide/v1/update_custom_model", "getProvidersAPI": "api/ide/v1/providers", "addModelAPI": "api/ide/v1/add_custom_model", "builderPromptAPI": "api/ide/v1/llm_raw_chat_prompt", "docsetCheckShouldUpdateAPI": "api/ide/v1/documentrag/official/check_should_update", "docsetLatestOfficialListAPI": "api/ide/v1/documentrag/official/latest_document_sets", "docsetCreateCustomDocsetIndexAPI": "api/ide/v1/documentrag/custom/index_document_set", "docsetDeleteCustomDocsetIndexAPI": "api/ide/v1/documentrag/custom/delete_document_set", "docsetGetCustomDocsetStatusAPI": "api/ide/v1/documentrag/custom/document_sets_status", "docsetRetrieveRagAPI": "api/ide/v1/documentrag/retrieve", "webSearchAPI": "api/ide/v1/web_search", "jumpQueueTaskAPI": "api/ide/v1/jump_queue_task", "getChatModeAPI": "api/v1/commercial/get_mode_info", "chatModeAPI": "api/v1/commercial/chat_mode", "saveCommercialStatusAPI": "api/v1/commercial/save_status", "pingAPI": "api/ide/v1/ping"}, "ckgConfig": {"host": "https://copilot-sg.byteintl.net", "hostDCDN": "https://copilot-sg.byteintl.net", "appId": "6eefa01c-1036-4c7e-9ca5-d891f63bfcd8", "storagePath": "/cloudide/workspace/.cloudide/data", "binaryPath": "binary", "sourceProduct": "native_ide", "port": 50051, "versionCode": "2", "knowledgebaseIdPrefix": "ckg_v2", "ideVersion": "1.84.1", "region": "sg", "enableLocalEmbedding": true, "ckgVersion": "0.0.98", "embeddingStorageType": "sqlite_vec"}, "versionCode": 20250812, "channel": "desktop", "featureGates": {"enableKeywordSearch": false, "enableUnitTest": true}, "slardarConfig": {"host": "https://mon-va.byteoversea.com", "bid": "marscode_nativeide_us", "pcHost": "https://pc-mon-sg.byteintlapi.com", "pcAid": "787976"}, "teaConfig": {"host": "https://maliva-mcs.byteoversea.com", "appId": "677332"}, "ttNetConfig": {"appId": "787976", "httpDNS": "*************", "netLog": "ttnet-sg.byteoversea.com", "domainBOE": ".boe-gateway.byted.org", "tncHost": "tnc16-alisg.isnssdk.com"}}, "cn": {"api": {"host": "https://a0ai-api.zijieapi.com", "hostDCDN": "https://trae-api-cn.mchost.guru", "region": "cn", "appId": "6eefa01c-1036-4c7e-9ca5-d891f63bfcd8", "practiceAppId": "850edec7-b9d0-48aa-99b5-67c888e282cd", "intentDetectAPI": "api/ide/v1/intent_detect", "contextSelectionAPI": "api/ide/v1/context_select", "queryRewriteAPI": "api/ide/v1/query_rewrite", "chatAPI": "api/ide/v1/chat", "authHeader": "x-ide-token", "appVersion": "default", "agentRunAPI": "api/ide/v1/agents/runs", "agentRunResultAPI": "api/ide/v1/agents/runs/:id/tool_call_outputs", "agentRunDiff": "api/ide/unstable/tools/diff", "practiceTitleAPI": "api/ide/v1/practice/generate_conversation_title", "getResourceUploadToken": "api/ide/v1/get_resource_upload_token", "getResourceUrl": "api/ide/v1/get_resource_url", "featuresAPI": "api/ide/v1/features", "fastApplyAPI": "api/ide/v1/fast_apply", "modelListAPI": "api/ide/v1/model_list", "fusionModelListAPI": "api/ide/v1/get_model_list", "fusionModelDetailAPI": "api/ide/v1/get_detail_param", "fusionRawChatAPI": "api/ide/v2/llm_raw_chat", "feedbackAPI": "api/ide/v1/feedback", "llmRawChatAPI": "api/ide/v1/llm_raw_chat", "getResourceUploadURLAPI": "api/ide/v1/get_resource_upload_url", "commitResourceUploadResultAPI": "api/ide/v1/commit_resource_upload_result", "cancelQueueTaskAPI": "api/ide/v1/cancel_queue_task", "modelConnectAPI": "api/ide/v1/connect", "chatPromptAPI": "api/ide/v1/chat_prompt", "updateModelAPI": "api/ide/v1/update_custom_model", "getProvidersAPI": "api/ide/v1/providers", "addModelAPI": "api/ide/v1/add_custom_model", "builderPromptAPI": "api/ide/v1/llm_raw_chat_prompt", "docsetCheckShouldUpdateAPI": "api/ide/v1/documentrag/official/check_should_update", "docsetLatestOfficialListAPI": "api/ide/v1/documentrag/official/latest_document_sets", "docsetCreateCustomDocsetIndexAPI": "api/ide/v1/documentrag/custom/index_document_set", "docsetDeleteCustomDocsetIndexAPI": "api/ide/v1/documentrag/custom/delete_document_set", "docsetGetCustomDocsetStatusAPI": "api/ide/v1/documentrag/custom/document_sets_status", "docsetRetrieveRagAPI": "api/ide/v1/documentrag/retrieve", "webSearchAPI": "api/ide/v1/web_search", "jumpQueueTaskAPI": "api/ide/v1/jump_queue_task", "getChatModeAPI": "api/v1/commercial/get_mode_info", "chatModeAPI": "api/v1/commercial/chat_mode", "saveCommercialStatusAPI": "api/v1/commercial/save_status", "pingAPI": "api/ide/v1/ping"}, "ckgConfig": {"host": "https://a0ai-api.zijieapi.com", "hostDCDN": "https://trae-api-cn.mchost.guru", "appId": "6eefa01c-1036-4c7e-9ca5-d891f63bfcd8", "storagePath": "/cloudide/workspace/.cloudide/data", "binaryPath": "binary", "sourceProduct": "native_ide", "port": 50051, "versionCode": "2", "knowledgebaseIdPrefix": "ckg_v2", "ideVersion": "1.84.1", "region": "cn", "enableLocalEmbedding": true, "ckgVersion": "0.0.98", "embeddingStorageType": "sqlite_vec"}, "versionCode": 20250812, "channel": "desktop", "featureGates": {"enableKeywordSearch": true, "enableUnitTest": true}, "slardarConfig": {"host": "https://mon.zijieapi.com", "bid": "trae_cn", "pcHost": "https://pc-mon.zijieapi.com", "pcAid": "787976"}, "teaConfig": {"host": "https://mcs.zijieapi.com", "appId": "711126"}, "ttNetConfig": {"appId": "787976", "httpDNS": "dig.bdurl.net", "netLog": "crash.snssdk.com", "domainBOE": ".boe-gateway.byted.org", "tncHost": "tnc3-bjlgy.zijieapi.com"}}, "sg": {"api": {"host": "https://a0ai-api-sg.byteintlapi.com", "hostDCDN": "https://trae-api-sg.mchost.guru", "region": "sg", "appId": "6eefa01c-1036-4c7e-9ca5-d891f63bfcd8", "practiceAppId": "850edec7-b9d0-48aa-99b5-67c888e282cd", "intentDetectAPI": "api/ide/v1/intent_detect", "contextSelectionAPI": "api/ide/v1/context_select", "queryRewriteAPI": "api/ide/v1/query_rewrite", "chatAPI": "api/ide/v1/chat", "authHeader": "x-ide-token", "appVersion": "default", "agentRunAPI": "api/ide/v1/agents/runs", "agentRunResultAPI": "api/ide/v1/agents/runs/:id/tool_call_outputs", "agentRunDiff": "api/ide/unstable/tools/diff", "practiceTitleAPI": "api/ide/v1/practice/generate_conversation_title", "getResourceUploadToken": "api/ide/v1/get_resource_upload_token", "getResourceUrl": "api/ide/v1/get_resource_url", "featuresAPI": "api/ide/v1/features", "fastApplyAPI": "api/ide/v1/fast_apply", "modelListAPI": "api/ide/v1/model_list", "fusionModelListAPI": "api/ide/v1/get_model_list", "fusionModelDetailAPI": "api/ide/v1/get_detail_param", "fusionRawChatAPI": "api/ide/v2/llm_raw_chat", "feedbackAPI": "api/ide/v1/feedback", "llmRawChatAPI": "api/ide/v1/llm_raw_chat", "getResourceUploadURLAPI": "api/ide/v1/get_resource_upload_url", "commitResourceUploadResultAPI": "api/ide/v1/commit_resource_upload_result", "cancelQueueTaskAPI": "api/ide/v1/cancel_queue_task", "modelConnectAPI": "api/ide/v1/connect", "chatPromptAPI": "api/ide/v1/chat_prompt", "updateModelAPI": "api/ide/v1/update_custom_model", "getProvidersAPI": "api/ide/v1/providers", "addModelAPI": "api/ide/v1/add_custom_model", "builderPromptAPI": "api/ide/v1/llm_raw_chat_prompt", "docsetCheckShouldUpdateAPI": "api/ide/v1/documentrag/official/check_should_update", "docsetLatestOfficialListAPI": "api/ide/v1/documentrag/official/latest_document_sets", "docsetCreateCustomDocsetIndexAPI": "api/ide/v1/documentrag/custom/index_document_set", "docsetDeleteCustomDocsetIndexAPI": "api/ide/v1/documentrag/custom/delete_document_set", "docsetGetCustomDocsetStatusAPI": "api/ide/v1/documentrag/custom/document_sets_status", "docsetRetrieveRagAPI": "api/ide/v1/documentrag/retrieve", "webSearchAPI": "api/ide/v1/web_search", "jumpQueueTaskAPI": "api/ide/v1/jump_queue_task", "getChatModeAPI": "api/v1/commercial/get_mode_info", "chatModeAPI": "api/v1/commercial/chat_mode", "saveCommercialStatusAPI": "api/v1/commercial/save_status", "pingAPI": "api/ide/v1/ping"}, "ckgConfig": {"host": "https://a0ai-api-sg.byteintlapi.com", "hostDCDN": "https://trae-api-sg.mchost.guru", "appId": "6eefa01c-1036-4c7e-9ca5-d891f63bfcd8", "storagePath": "/cloudide/workspace/.cloudide/data", "binaryPath": "binary", "sourceProduct": "native_ide", "port": 50051, "versionCode": "2", "knowledgebaseIdPrefix": "ckg_v2", "ideVersion": "1.84.1", "region": "sg", "enableLocalEmbedding": true, "ckgVersion": "0.0.98", "embeddingStorageType": "sqlite_vec"}, "versionCode": 20250812, "channel": "desktop", "featureGates": {"enableKeywordSearch": true, "enableUnitTest": true}, "slardarConfig": {"host": "https://mon-va.byteoversea.com", "bid": "marscode_nativeide_us", "pcHost": "https://pc-mon-sg.byteintlapi.com", "pcAid": "682161"}, "teaConfig": {"host": "https://maliva-mcs.byteoversea.com", "appId": "677332"}, "ttNetConfig": {"appId": "682161", "httpDNS": "*************", "netLog": "ttnet-sg.byteoversea.com", "domainBOE": ".boe-gateway.byted.org", "tncHost": "tnc16-alisg.isnssdk.com"}}, "us": {"api": {"host": "https://a0ai-api-us.byteintlapi.com", "hostDCDN": "https://trae-api-us.mchost.guru", "region": "us", "appId": "6eefa01c-1036-4c7e-9ca5-d891f63bfcd8", "practiceAppId": "850edec7-b9d0-48aa-99b5-67c888e282cd", "intentDetectAPI": "api/ide/v1/intent_detect", "contextSelectionAPI": "api/ide/v1/context_select", "queryRewriteAPI": "api/ide/v1/query_rewrite", "chatAPI": "api/ide/v1/chat", "authHeader": "x-ide-token", "appVersion": "default", "agentRunAPI": "api/ide/v1/agents/runs", "agentRunResultAPI": "api/ide/v1/agents/runs/:id/tool_call_outputs", "agentRunDiff": "api/ide/unstable/tools/diff", "practiceTitleAPI": "api/ide/v1/practice/generate_conversation_title", "getResourceUploadToken": "api/ide/v1/get_resource_upload_token", "getResourceUrl": "api/ide/v1/get_resource_url", "featuresAPI": "api/ide/v1/features", "fastApplyAPI": "api/ide/v1/fast_apply", "modelListAPI": "api/ide/v1/model_list", "fusionModelListAPI": "api/ide/v1/get_model_list", "fusionModelDetailAPI": "api/ide/v1/get_detail_param", "fusionRawChatAPI": "api/ide/v2/llm_raw_chat", "feedbackAPI": "api/ide/v1/feedback", "llmRawChatAPI": "api/ide/v1/llm_raw_chat", "getResourceUploadURLAPI": "api/ide/v1/get_resource_upload_url", "commitResourceUploadResultAPI": "api/ide/v1/commit_resource_upload_result", "cancelQueueTaskAPI": "api/ide/v1/cancel_queue_task", "modelConnectAPI": "api/ide/v1/connect", "chatPromptAPI": "api/ide/v1/chat_prompt", "updateModelAPI": "api/ide/v1/update_custom_model", "getProvidersAPI": "api/ide/v1/providers", "addModelAPI": "api/ide/v1/add_custom_model", "builderPromptAPI": "api/ide/v1/llm_raw_chat_prompt", "docsetCheckShouldUpdateAPI": "api/ide/v1/documentrag/official/check_should_update", "docsetLatestOfficialListAPI": "api/ide/v1/documentrag/official/latest_document_sets", "docsetCreateCustomDocsetIndexAPI": "api/ide/v1/documentrag/custom/index_document_set", "docsetDeleteCustomDocsetIndexAPI": "api/ide/v1/documentrag/custom/delete_document_set", "docsetGetCustomDocsetStatusAPI": "api/ide/v1/documentrag/custom/document_sets_status", "docsetRetrieveRagAPI": "api/ide/v1/documentrag/retrieve", "webSearchAPI": "api/ide/v1/web_search", "jumpQueueTaskAPI": "api/ide/v1/jump_queue_task", "getChatModeAPI": "api/v1/commercial/get_mode_info", "chatModeAPI": "api/v1/commercial/chat_mode", "saveCommercialStatusAPI": "api/v1/commercial/save_status", "pingAPI": "api/ide/v1/ping"}, "ckgConfig": {"host": "https://a0ai-api-sg.byteintlapi.com", "hostDCDN": "https://trae-api-sg.mchost.guru", "appId": "6eefa01c-1036-4c7e-9ca5-d891f63bfcd8", "storagePath": "/cloudide/workspace/.cloudide/data", "binaryPath": "binary", "sourceProduct": "native_ide", "port": 50051, "versionCode": "2", "knowledgebaseIdPrefix": "ckg_v2", "ideVersion": "1.84.1", "region": "sg", "enableLocalEmbedding": true, "ckgVersion": "0.0.98", "embeddingStorageType": "sqlite_vec"}, "versionCode": 20250812, "channel": "desktop", "featureGates": {"enableKeywordSearch": true, "enableUnitTest": true}, "slardarConfig": {"host": "https://mon-va.byteoversea.com", "bid": "marscode_nativeide_us", "pcHost": "https://pc-mon-sg.byteintlapi.com", "pcAid": "682161"}, "teaConfig": {"host": "https://maliva-mcs.byteoversea.com", "appId": "677332"}, "ttNetConfig": {"appId": "682161", "httpDNS": "*************", "netLog": "ttnet-sg.byteoversea.com", "domainBOE": ".boe-gateway.byted.org", "tncHost": "tnc16-alisg.isnssdk.com"}}}