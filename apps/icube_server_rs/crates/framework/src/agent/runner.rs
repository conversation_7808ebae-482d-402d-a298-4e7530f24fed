use crate::agent::context::CallingContext;
use crate::agent::execution::{Execution, ExecutionStatus};
use crate::agent::state::RunState;

use crate::core::checkpoint::{CheckPointer, GraphState};
use crate::core::error::AgentError;
use crate::core::event::{Event, ProcessedOutput};
use crate::core::task::TaskControl;
use crate::graph::builtin_action::interrupt::Interrupt;
use crate::graph::{GraphNodeInvokeDecision, NodeRef, RunGraph};

use serde::de::DeserializeOwned;
use serde::Serialize;
use serde_json::Value;
use std::collections::VecDeque;
use std::fmt::Debug;
use std::sync::Arc;
use tokio::select;
use tokio_util::sync::CancellationToken;
use tracing::instrument;

pub struct TaskRunner<S, Config> {
    graph: Arc<dyn RunGraph<RunState<S>, CallingContext<Config>>>,
    queue: VecDeque<(NodeRef, Value)>,
    check_pointer: Arc<dyn CheckPointer<S, Config>>,
    error_node: NodeRef,
}

impl<S, Config> TaskRunner<S, Config> {
    pub fn new(
        graph: Arc<dyn RunGraph<RunState<S>, CallingContext<Config>>>,
        check_pointer: Arc<dyn CheckPointer<S, Config>>,
    ) -> Self {
        let error_node = graph.error_node();
        Self {
            error_node,
            graph,
            queue: VecDeque::new(),
            check_pointer,
        }
    }

    fn enqueue_entry(&mut self, entry: NodeRef, input: Value) {
        self.queue.push_back((entry, input));
    }

    fn has_finished(&self) -> bool {
        self.queue.is_empty()
    }

    fn update_graph_state(
        &self,
        task: &mut Execution<S, Config>,
        last_node_name: &str,
        next_node_name: &str,
        next_node_input: Value,
    ) {
        task.graph_state = GraphState {
            last_node_name: last_node_name.to_string(),
            next_node_name: next_node_name.to_string(),
            next_node_input,
        };
    }
}

impl<S, Config> TaskRunner<S, Config>
where
    S: Clone + Serialize + Debug + Sync + Send + 'static + Default,
    Config: Default + Clone + DeserializeOwned + Debug + Sync + Send + 'static,
{
    #[instrument(skip(self, ctx, cancel, task), fields(task_id = %task.id))]
    async fn step(
        &mut self,
        ctx: &CallingContext<Config>,
        cancel: &CancellationToken,
        task: &mut Execution<S, Config>,
    ) -> Result<(), AgentError> {
        // 如果队列为空的话，外部是进不来的
        let Some((current, input)) = self.queue.pop_front() else {
            return Ok(());
        };

        let current_node = self
            .graph
            .current_node(current.index)
            .ok_or_else(|| AgentError::TransitionError("failed to get current node".into()))?;

        let decision = {
            // 如果已经取消的话，就执行一下最后一个节点
            if matches!(task.status, ExecutionStatus::Canceled) {
                current_node
                    .invoke(ctx.clone(), &mut task.state, input)
                    .await
            } else {
                select! {
                    biased;
                    _ = cancel.cancelled() => {
                        tracing::warn!("task runner canceled");
                        task.to_cancel();
                        Err(AgentError::TaskCanceled)
                    },
                    res = current_node.invoke(
                        ctx.clone(),
                        &mut task.state,
                        input,
                    ) => res,
                }
            }
        };

        match decision {
            Ok(GraphNodeInvokeDecision::Continue(output)) => {
                if let Some(next) = self.graph.next_node(current.index) {
                    let next_name = self.graph.node_name(next.index);
                    self.update_graph_state(
                        task,
                        &self.graph.node_name(current.index), // last
                        &next_name,                           // next
                        output.clone(),                       // next_in
                    );
                    self.enqueue_entry(next, output);
                } else {
                    // 错误的时候不要发送事件
                    if current.index != self.error_node.index {
                        ctx.emit_message(Event::ProcessingComplete {
                            output: ProcessedOutput {
                                payload: output,
                                history: task.state.messages().to_vec(),
                                artifacts: task.state.artifacts().clone(),
                                summary: "".into(),
                                turns: task.state.turns,
                            },
                        });
                    }
                }
            }
            Ok(GraphNodeInvokeDecision::Goto(idx, output)) => {
                let next = self
                    .graph
                    .node_ref(&self.graph.node_name(idx))
                    .ok_or_else(|| AgentError::InternalError("can not find node".into()))?;
                let next_name = self.graph.node_name(next.index);

                self.update_graph_state(
                    task,
                    &self.graph.node_name(current.index), // last
                    &next_name,                           // next
                    output.clone(),                       // next_in
                );
                self.enqueue_entry(next, output);
            }
            Err(e) => {
                if current.index != self.error_node.index {
                    let curr_name = self.graph.node_name(current.index);
                    let err_val = serde_json::to_value(&e).unwrap_or_default();
                    task.state.error = Some(e.clone());
                    self.update_graph_state(
                        task,
                        &curr_name,
                        &self.graph.node_name(self.error_node.index),
                        err_val.clone(),
                    );
                    self.enqueue_entry(self.graph.error_node(), err_val);
                }
                ctx.emit_message(Event::ErrorOccurred {
                    error: e,
                    turns: task.state.turns,
                });
            }
        }
        Ok(())
    }

    #[instrument(skip(self, ctx, task, incoming), fields(task_id = %task.id, next_node_name))]
    async fn handle_interrupt(
        &self,
        ctx: &CallingContext<Config>,
        task: &mut Execution<S, Config>,
        next_node_name: &str,
        incoming: &Value,
    ) -> Result<(), AgentError> {
        let output_val = incoming.clone();
        let intr: Interrupt = serde_json::from_value(output_val)?;
        task.set_interrupt(&intr);
        task.set_history_pending(task.history_pendings.clone());
        let last_node_name = task.graph_state.last_node_name.clone();
        self.update_graph_state(task, &last_node_name, next_node_name, incoming.clone());
        let checkpoint = task.make_checkpoint();
        self.check_pointer.save(&checkpoint).await?;
        ctx.emit_message(Event::Interrupt(Box::new(intr)));
        Ok(())
    }

    #[instrument(skip(self, ctx, task, ctrl), fields(task_id = %task.id))]
    pub async fn run(
        &mut self,
        ctx: CallingContext<Config>,
        mut task: Execution<S, Config>,
        ctrl: TaskControl,
    ) -> Result<(), AgentError> {
        let entry_point = self
            .graph
            .node_ref(&task.graph_state.next_node_name)
            .ok_or_else(|| AgentError::InternalError("can not find start node".into()))?;

        let input = task.get_start_input()?;
        if task.graph_state.last_node_name.is_empty() {
            self.update_graph_state(
                &mut task,
                &self.graph.node_name(entry_point.index), // last
                &self.graph.node_name(entry_point.index), // next
                input.clone(),                            // next_in
            );
        }
        self.queue.push_back((entry_point, input));

        while !self.has_finished() {
            self.step(&ctx, &ctrl.token, &mut task).await?;

            if let Some((head, output)) = self.queue.front() {
                if let Some(next_node) = self.graph.current_node(head.index) {
                    if next_node.is_interrupt() {
                        let next_node_name = self.graph.node_name(head.index);
                        if let Err(e) = self
                            .handle_interrupt(&ctx, &mut task, &next_node_name, output)
                            .await
                        {
                            ctx.emit_message(Event::ErrorOccurred {
                                error: e,
                                turns: task.state.turns,
                            });
                        }
                        break;
                    }
                }
            }
        }
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::agent::context::ReActAgentContext;

    use crate::agent::react_agent::ReActAgentGraph;
    use crate::agent::state::RunState;
    use crate::core::artifact::Artifact;
    use crate::core::checkpoint::{CheckPoint, CheckPointError};
    use crate::core::handler::StageHandler;

    use crate::core::llm::{DynLLMWrapper, LLMRunConfig, PinBoxLLMStream, LLM};
    use crate::core::message::{Message, ToolCall};
    use crate::core::prompt::RenderResult;
    use crate::core::provider::model::{
        LLMProvider, LLMProviderError, LanguageModel, ModelProvider,
    };
    use crate::graph::builtin_action::interrupt::{InterruptReason, Question};
    use crate::graph::{InterruptHandler, StateGraph};
    use crate::ioc::ioc::IOCContainer;
    use crate::llm::error::LLMError;
    use crate::llm::GenerateResult;
    use crate::prompt::{PromptArgs, PromptError, PromptFormatter, PromptProvider};
    use async_trait::async_trait;
    use serde::{Deserialize, Serialize};
    use std::collections::HashMap;
    use std::sync::Mutex;
    use tokio::sync::mpsc::unbounded_channel;

    // Mock LLM
    #[derive(Clone)]
    struct MockLLM;

    #[async_trait]
    impl LLM for MockLLM {
        type LLMConfig = LLMRunConfig;
        fn name(&self) -> &'static str {
            "MockLLM"
        }

        async fn generate(&self, messages: &[Message]) -> Result<GenerateResult, LLMError> {
            Ok(GenerateResult {
                generation: "Mock response".to_string(),
                ..Default::default()
            })
        }

        async fn stream(&self, _messages: &[Message]) -> Result<PinBoxLLMStream, LLMError> {
            todo!()
        }

        // async fn stream(
        //     &self,
        //     _messages: &[Message],
        // ) -> Result<Pin<Box<dyn Stream<Item = Result<StreamData, LLMError>> + Send>>, LLMError>
        // {
        //     todo!()
        // }
    }

    #[derive(Default, Clone, Deserialize, Debug, Serialize)]
    struct TestConfig;

    struct TestHandler;
    #[async_trait]
    impl StageHandler<RunState<()>, CallingContext<()>> for TestHandler {
        type Incoming = ();
        type Outgoing = Interrupt;

        async fn handle(
            &self,
            ctx: CallingContext<()>,
            state: &mut RunState<()>,
            input: Self::Incoming,
        ) -> Result<Self::Outgoing, AgentError> {
            Ok(Interrupt::new(
                "test_task",
                InterruptReason::AskUser(Question {
                    task_id: "test_task".to_string(),
                    tool_call: ToolCall::default(),
                    message: None,
                    payload: None,
                    agent_name: "test_agent".to_string(),
                }),
                vec![],
            ))
        }
    }

    #[derive(Debug)]
    pub struct ResumeHandler;

    #[async_trait]
    impl InterruptHandler<RunState<()>, CallingContext<()>> for ResumeHandler {
        type Outgoing = ();

        async fn on_resume(
            &self,
            ctx: CallingContext<()>,
            state: &mut RunState<()>,
            interrupt: Interrupt,
        ) -> Result<Self::Outgoing, AgentError> {
            Ok(())
        }
    }

    fn create_simple_graph() -> ReActAgentGraph<(), ()> {
        let mut graph = StateGraph::new();
        let node1 = graph.add_node(TestHandler).expect("Failed to add node1");
        let node2 = graph
            .add_interrupt_node(ResumeHandler)
            .expect("Failed to add resume");
        graph.connect(&node1, &node2).expect("Failed to add node2");
        graph.compile().expect("Failed to compile graph")
    }

    struct TestCheckPointer {
        map: Arc<Mutex<HashMap<String, CheckPoint<(), ()>>>>,
    }

    impl TestCheckPointer {
        fn new() -> Self {
            Self {
                map: Arc::new(Mutex::new(HashMap::new())),
            }
        }
    }

    #[async_trait]
    impl CheckPointer<(), ()> for TestCheckPointer {
        async fn get(&self, session_id: &str) -> Result<CheckPoint<(), ()>, CheckPointError> {
            Ok(CheckPoint::default())
        }

        async fn save(&self, checkpoint: &CheckPoint<(), ()>) -> Result<(), CheckPointError> {
            let mut map = self.map.lock().unwrap();
            map.insert(checkpoint.task_id.to_string(), checkpoint.clone());
            Ok(())
        }
    }
    struct TestModelProvider;

    impl ModelProvider for TestModelProvider {
        fn get_llm_model(&self, provider: &LLMProvider) -> Result<LanguageModel, LLMProviderError> {
            Ok(Arc::new(DynLLMWrapper(MockLLM)))
        }
    }

    struct TestPromptProvider;
    struct TestPromptFormatter;

    #[async_trait]
    impl PromptFormatter for TestPromptFormatter {
        async fn format(&self, _input_variables: PromptArgs) -> Result<RenderResult, PromptError> {
            Ok(RenderResult {
                system_prompt: "Test template".to_string(),
                user_input: None,
                final_input: None,
                ..Default::default()
            })
        }

        fn as_any(&self) -> &dyn std::any::Any {
            self
        }
    }

    #[async_trait]
    impl PromptProvider for TestPromptProvider {
        async fn get_default_prompt_template(
            &self,
        ) -> Result<Arc<dyn PromptFormatter>, PromptError> {
            todo!()
        }

        async fn get_prompt_template(
            &self,
            _name: &str,
        ) -> Result<Arc<dyn PromptFormatter>, PromptError> {
            Ok(Arc::new(TestPromptFormatter))
        }

        fn as_any(&self) -> &dyn std::any::Any {
            self
        }
    }

    #[tokio::test]
    async fn test_simple_run_completes() {
        let graph = create_simple_graph();
        let start_node = graph.start_node();
        let node_name = graph.node_name(start_node.index);
        let checkpointer = Arc::new(TestCheckPointer::new());
        let mut runner = TaskRunner::new(Arc::new(graph), checkpointer.clone());

        let ctx = ReActAgentContext {
            name: "test_agent".into(),
            llm: Arc::new(MockLLM),
            llm_provider: Arc::new(TestModelProvider),
            prompt_provider: Arc::new(TestPromptProvider),
            service_registry: Arc::new(IOCContainer::default()),
            ..Default::default()
        };

        let task = Execution::builder()
            .state(RunState::new(()))
            .graph_state(GraphState::new(node_name.as_ref()))
            .session_id("")
            .user_message_id("")
            .message_id("")
            .config(())
            .build()
            .expect("Failed to build task runner");
        // let task = Execution::new(RunState::new(()), GraphState::new(node_name.as_ref()));
        let (output_tx, mut output_rx) = unbounded_channel();
        let task_id = task.id.clone();
        let ctrl = TaskControl::new(&task_id, CancellationToken::new());
        let ctx = CallingContext::new(
            task.get_session_ref(),
            Arc::new(ctx),
            Some(output_tx),
            Arc::new(IOCContainer::default()),
            Arc::new(()),
            None,
        );

        runner.run(ctx, task, ctrl).await.unwrap();

        let mut interrupted = false;
        while let Some(event) = output_rx.recv().await {
            if let Event::Interrupt { .. } = event {
                interrupted = true;
                break;
            }
        }
        assert!(interrupted);
        let map = checkpointer.map.lock().unwrap();
        let checkpoint = map.get(&task_id).unwrap();
        assert!(checkpoint.interrupt.is_some());
        assert_eq!(
            checkpoint.graph_state.last_node_name,
            "framework::agent::runner::tests::TestHandler"
        );
        assert_eq!(checkpoint.graph_state.next_node_name, "__INTERRUPT__");
    }

    #[tokio::test]
    async fn test_conditional_run() {
        #[derive(Serialize, Deserialize)]
        enum DecisionB {
            C,
            D,
        }

        struct HandlerA;
        struct HandlerB;
        struct HandlerC;
        struct HandlerD;

        #[async_trait]
        impl StageHandler<RunState<()>, CallingContext<()>> for HandlerA {
            type Incoming = ();
            type Outgoing = String;

            async fn handle(
                &self,
                _ctx: CallingContext<()>,
                _state: &mut RunState<()>,
                _input: Self::Incoming,
            ) -> Result<Self::Outgoing, AgentError> {
                Ok("Ok".to_string())
            }
        }

        #[async_trait]
        impl StageHandler<RunState<()>, CallingContext<()>> for HandlerB {
            type Incoming = String;
            type Outgoing = DecisionB;

            async fn handle(
                &self,
                _ctx: CallingContext<()>,
                _state: &mut RunState<()>,
                input: Self::Incoming,
            ) -> Result<Self::Outgoing, AgentError> {
                if input == "Ok" {
                    Ok(DecisionB::C)
                } else {
                    Ok(DecisionB::D)
                }
            }
        }

        #[async_trait]
        impl StageHandler<RunState<()>, CallingContext<()>> for HandlerC {
            type Incoming = u32;
            type Outgoing = ();

            async fn handle(
                &self,
                _ctx: CallingContext<()>,
                state: &mut RunState<()>,
                _input: Self::Incoming,
            ) -> Result<Self::Outgoing, AgentError> {
                state.add_message(Message::new_human_message("calling from C"));
                state.add_artifact(Artifact::new(
                    "artifact_from_C",
                    "This is an artifact from C",
                ));
                Ok(())
            }
        }

        #[async_trait]
        impl StageHandler<RunState<()>, CallingContext<()>> for HandlerD {
            type Incoming = ();
            type Outgoing = ();

            async fn handle(
                &self,
                _ctx: CallingContext<()>,
                _state: &mut RunState<()>,
                _input: Self::Incoming,
            ) -> Result<Self::Outgoing, AgentError> {
                Ok(())
            }
        }

        fn create_simple_graph() -> ReActAgentGraph<(), ()> {
            let mut graph = StateGraph::new();
            let node1 = graph.add_node(HandlerA).expect("Failed to add node1");
            let node2 = graph.add_node(HandlerB).expect("Failed to add node1");
            let node3 = graph.add_node(HandlerC).expect("Failed to add node3");
            let node4 = graph.add_node(HandlerD).expect("Failed to add node3");
            graph
                .connect(&node1, &node2)
                .expect("Failed to connect node1 to node2");
            graph
                .switch("router")
                .from(&node2)
                .case(
                    |_, out| matches!(out, DecisionB::C).then_some(123_u32),
                    &node3,
                )
                .case(|_, out| matches!(out, DecisionB::D).then_some(()), &node4)
                .default(&node4)
                .build()
                .unwrap();
            graph.compile().unwrap()
        }

        let graph = create_simple_graph();
        let start_node = graph.start_node();
        let node_name = graph.node_name(start_node.index);
        let checkpointer = Arc::new(TestCheckPointer::new());
        let mut runner = TaskRunner::new(Arc::new(graph), checkpointer.clone());

        let ctx = ReActAgentContext {
            name: "test_agent".into(),
            llm: Arc::new(MockLLM),
            llm_provider: Arc::new(TestModelProvider),
            prompt_provider: Arc::new(TestPromptProvider),
            service_registry: Arc::new(IOCContainer::default()),
            ..Default::default()
        };

        let task = Execution::builder()
            .state(RunState::new(()))
            .graph_state(GraphState::new(node_name.as_ref()))
            .session_id("")
            .user_message_id("")
            .message_id("")
            .config(())
            .build()
            .expect("Failed to build task runner");
        let (output_tx, mut output_rx) = unbounded_channel();
        let task_id = task.id.clone();
        let ctrl = TaskControl::new(&task_id, CancellationToken::new());
        let ctx = CallingContext::new(
            task.get_session_ref(),
            Arc::new(ctx),
            Some(output_tx),
            Arc::new(IOCContainer::default()),
            Arc::new(()),
            None,
        );

        runner.run(ctx, task, ctrl).await.unwrap();

        let mut completed = false;
        let mut res = None;
        while let Some(event) = output_rx.recv().await {
            if let Event::ProcessingComplete { output } = event {
                completed = true;
                res = Some(output);
                break;
            }
        }
        assert!(completed);
        assert!(res.is_some());
        let res = res.unwrap();
        assert_eq!(res.artifacts.len(), 1);
        assert_eq!(res.history.len(), 1);
    }

    #[tokio::test]
    async fn test_task_cancellation() {
        use std::time::Duration;
        use tokio::time::sleep;

        // Create a handler that sleeps for a long time
        struct LongRunningHandler;

        #[async_trait]
        impl StageHandler<RunState<()>, CallingContext<()>> for LongRunningHandler {
            type Incoming = ();
            type Outgoing = ();

            async fn handle(
                &self,
                _ctx: CallingContext<()>,
                _state: &mut RunState<()>,
                _input: Self::Incoming,
            ) -> Result<Self::Outgoing, AgentError> {
                // Sleep for a long time to simulate long-running task
                sleep(Duration::from_secs(10)).await;
                Ok(())
            }
        }

        fn create_long_running_graph() -> ReActAgentGraph<(), ()> {
            let mut graph = StateGraph::new();
            let node1 = graph
                .add_node(LongRunningHandler)
                .expect("Failed to add node1");
            graph.compile().expect("Failed to compile graph")
        }

        let graph = create_long_running_graph();
        let start_node = graph.start_node();
        let node_name = graph.node_name(start_node.index);
        let checkpointer = Arc::new(TestCheckPointer::new());
        let runner = TaskRunner::new(Arc::new(graph), checkpointer.clone());

        let ctx = ReActAgentContext {
            name: "test_agent".into(),
            llm: Arc::new(MockLLM),
            llm_provider: Arc::new(TestModelProvider),
            prompt_provider: Arc::new(TestPromptProvider),
            service_registry: Arc::new(IOCContainer::default()),
            ..Default::default()
        };

        let task = Execution::builder()
            .state(RunState::new(()))
            .graph_state(GraphState::new(node_name.as_ref()))
            .session_id("")
            .user_message_id("")
            .message_id("")
            .config(())
            .build()
            .expect("Failed to build task runner");
        let (output_tx, mut output_rx) = unbounded_channel();
        let task_id = task.id.clone();
        let cancel_token = CancellationToken::new();
        let ctrl = TaskControl::new(&task_id, cancel_token.clone());
        let ctx = CallingContext::new(
            task.get_session_ref(),
            Arc::new(ctx),
            Some(output_tx),
            Arc::new(IOCContainer::default()),
            Arc::new(()),
            None,
        );

        // Start the task in a separate task
        let runner_handle = {
            let mut runner = runner;
            tokio::spawn(async move { runner.run(ctx, task, ctrl).await })
        };

        // Wait a short time to ensure the task starts
        sleep(Duration::from_millis(100)).await;

        // Cancel the task
        cancel_token.cancel();

        // The task should complete quickly after cancellation
        let start_time = std::time::Instant::now();
        let result = runner_handle.await.unwrap();
        let elapsed = start_time.elapsed();

        // Verify the task completed successfully (cancellation is handled gracefully)
        assert!(result.is_ok());

        // Verify the task stopped quickly after cancellation (within 1 second)
        assert!(
            elapsed < Duration::from_secs(1),
            "Task took too long to stop after cancellation: {:?}",
            elapsed
        );

        // Verify no completion event was emitted (task was cancelled before completion)
        let mut completed = false;
        while let Ok(event) = output_rx.try_recv() {
            if let Event::ProcessingComplete { .. } = event {
                completed = true;
                break;
            }
        }
        assert!(
            !completed,
            "Task should not have completed normally after cancellation"
        );
    }

    #[tokio::test]
    async fn test_cancellation_stops_immediately() {
        use std::time::Duration;

        // Create a simple handler that just returns
        struct SimpleHandler;

        #[async_trait]
        impl StageHandler<RunState<()>, CallingContext<()>> for SimpleHandler {
            type Incoming = ();
            type Outgoing = ();

            async fn handle(
                &self,
                _ctx: CallingContext<()>,
                _state: &mut RunState<()>,
                _input: Self::Incoming,
            ) -> Result<Self::Outgoing, AgentError> {
                Ok(())
            }
        }

        fn create_simple_graph() -> ReActAgentGraph<(), ()> {
            let mut graph = StateGraph::new();
            let node1 = graph.add_node(SimpleHandler).expect("Failed to add node1");
            graph.compile().expect("Failed to compile graph")
        }

        let graph = create_simple_graph();
        let start_node = graph.start_node();
        let node_name = graph.node_name(start_node.index);
        let checkpointer = Arc::new(TestCheckPointer::new());
        let mut runner = TaskRunner::new(Arc::new(graph), checkpointer.clone());

        let ctx = ReActAgentContext {
            name: "test_agent".into(),
            llm: Arc::new(MockLLM),
            llm_provider: Arc::new(TestModelProvider),
            prompt_provider: Arc::new(TestPromptProvider),
            service_registry: Arc::new(IOCContainer::default()),
            ..Default::default()
        };

        let task = Execution::builder()
            .state(RunState::new(()))
            .graph_state(GraphState::new(node_name.as_ref()))
            .session_id("")
            .message_id("")
            .user_message_id("")
            .config(())
            .build()
            .expect("Failed to build task runner");
        let (output_tx, mut output_rx) = unbounded_channel();
        let task_id = task.id.clone();
        let cancel_token = CancellationToken::new();
        let ctrl = TaskControl::new(&task_id, cancel_token.clone());
        let ctx = CallingContext::new(
            task.get_session_ref(),
            Arc::new(ctx),
            Some(output_tx),
            Arc::new(IOCContainer::default()),
            Arc::new(()),
            None,
        );

        // Cancel the task immediately before running
        cancel_token.cancel();

        // Run the task - it should stop immediately after processing the error_node
        let start_time = std::time::Instant::now();
        let result = runner.run(ctx, task, ctrl).await;
        let elapsed = start_time.elapsed();

        // Verify the task completed successfully
        assert!(result.is_ok());

        // Verify the task stopped quickly (within 100ms)
        assert!(
            elapsed < Duration::from_millis(100),
            "Task took too long to stop after immediate cancellation: {:?}",
            elapsed
        );

        // Verify an error event was emitted
        let mut completed = false;
        while let Ok(event) = output_rx.try_recv() {
            if let Event::ProcessingComplete { .. } = event {
                completed = true;
                break;
            }
        }
        assert!(
            !completed,
            "Task should not have completed normally after cancellation"
        );
    }

    #[tokio::test]
    async fn test_error_handler_executed_on_cancel() {
        use std::sync::atomic::{AtomicBool, Ordering};
        use std::time::Duration;
        use tokio::time::sleep;

        // 用于跟踪 error handler 是否被调用的标志
        static ERROR_HANDLER_CALLED: AtomicBool = AtomicBool::new(false);

        // 创建一个长时间运行的 handler
        struct LongRunningHandler;

        #[async_trait]
        impl StageHandler<RunState<()>, CallingContext<()>> for LongRunningHandler {
            type Incoming = ();
            type Outgoing = ();

            async fn handle(
                &self,
                _ctx: CallingContext<()>,
                _state: &mut RunState<()>,
                _input: Self::Incoming,
            ) -> Result<Self::Outgoing, AgentError> {
                // 模拟长时间运行的任务
                sleep(Duration::from_secs(10)).await;
                Ok(())
            }
        }

        fn create_graph_with_custom_error_handler() -> ReActAgentGraph<(), ()> {
            let mut graph = StateGraph::new();
            let node1 = graph
                .add_node(LongRunningHandler)
                .expect("Failed to add node1");

            // 设置自定义的 error handler
            graph.on_error(
                |ctx: CallingContext<()>, state: &mut RunState<()>, input: AgentError| async move {
                    // 设置标志表示 error handler 被调用了
                    ERROR_HANDLER_CALLED.store(true, Ordering::SeqCst);

                    // 验证输入是 TaskCanceled 错误
                    match input {
                        AgentError::TaskCanceled => {
                            // 这是我们期望的取消错误
                        }
                        _ => {
                            panic!("Expected TaskCanceled error, got: {:?}", input);
                        }
                    }
                    Ok(())
                },
            );

            graph.compile().expect("Failed to compile graph")
        }

        // 重置标志
        ERROR_HANDLER_CALLED.store(false, Ordering::SeqCst);

        let graph = create_graph_with_custom_error_handler();
        let start_node = graph.start_node();
        let node_name = graph.node_name(start_node.index);
        let checkpointer = Arc::new(TestCheckPointer::new());
        let mut runner = TaskRunner::new(Arc::new(graph), checkpointer.clone());

        let ctx = ReActAgentContext {
            name: "test_agent".into(),
            llm: Arc::new(MockLLM),
            llm_provider: Arc::new(TestModelProvider),
            prompt_provider: Arc::new(TestPromptProvider),
            service_registry: Arc::new(IOCContainer::default()),
            ..Default::default()
        };

        let task = Execution::builder()
            .state(RunState::new(()))
            .graph_state(GraphState::new(node_name.as_ref()))
            .session_id("")
            .message_id("")
            .user_message_id("")
            .config(())
            .build()
            .expect("Failed to build task runner");
        let (output_tx, output_rx) = unbounded_channel();
        let task_id = task.id.clone();
        let cancel_token = CancellationToken::new();
        let ctrl = TaskControl::new(&task_id, cancel_token.clone());
        let ctx = CallingContext::new(
            task.get_session_ref(),
            Arc::new(ctx),
            Some(output_tx),
            Arc::new(IOCContainer::default()),
            Arc::new(()),
            None,
        );

        // 在单独的任务中启动 runner
        let runner_handle = { tokio::spawn(async move { runner.run(ctx, task, ctrl).await }) };

        // 等待一小段时间确保任务开始运行
        sleep(Duration::from_millis(50)).await;

        // 取消任务
        cancel_token.cancel();

        // 再等待一小段时间确保取消被处理
        sleep(Duration::from_millis(50)).await;

        // 等待任务完成
        let start_time = std::time::Instant::now();
        let result = runner_handle.await.unwrap();
        let elapsed = start_time.elapsed();

        // 验证任务成功完成（取消被优雅处理）
        assert!(result.is_ok());

        // 验证任务在取消后快速停止（1秒内）
        assert!(
            elapsed < Duration::from_secs(1),
            "Task took too long to stop after cancellation: {:?}",
            elapsed
        );

        // 验证 error handler 被调用了
        assert!(
            ERROR_HANDLER_CALLED.load(Ordering::SeqCst),
            "Error handler should have been called when task was cancelled"
        );

        // // 验证收到了错误事件
        // let mut error_occurred = false;
        // while let Ok(event) = output_rx.try_recv() {
        //     if let Event::ErrorOccurred(AgentError::TaskCanceled) = event {
        //         error_occurred = true;
        //         break;
        //     }
        // }
        // assert!(
        //     error_occurred,
        //     "Should have received TaskCanceled error event"
        // );
    }
}
