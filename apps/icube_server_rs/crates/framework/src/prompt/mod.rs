mod args;

use crate::core::prompt::RenderResult;
use serde::{Deserialize, Serialize};
use std::any::{type_name, Any, TypeId};
use std::collections::HashMap;
use std::sync::Arc;
use thiserror::Error;

#[derive(<PERSON><PERSON><PERSON>, Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum PromptError {
    #[error("Missing variable: {0}")]
    MissingVariable(String),
    #[error("Render fail: {0}")]
    RenderFail(String),
}

// pub type PromptArgs = HashMap<TypeId, Box<dyn Any>>;

pub struct PromptArgs {
    map: HashMap<TypeId, Box<dyn Any + Send + 'static>>,
}

impl Default for PromptArgs {
    fn default() -> Self {
        Self::new()
    }
}

impl PromptArgs {
    pub fn new() -> Self {
        Self {
            map: HashMap::new(),
        }
    }

    pub fn insert<T: Any + Send + 'static>(&mut self, value: T) {
        self.map.insert(TypeId::of::<T>(), Box::new(value));
    }

    pub fn get<T: Any>(&self) -> Option<&T> {
        self.map
            .get(&TypeId::of::<T>())
            .and_then(|v| v.downcast_ref())
    }

    pub fn require<T: Any>(&self) -> Result<&T, PromptError> {
        self.get::<T>()
            .ok_or_else(|| PromptError::MissingVariable(type_name::<T>().into()))
    }

    pub fn get_or_default<T: Any + Clone + Default>(&self) -> T {
        self.get::<T>().cloned().unwrap_or_default()
    }

    pub fn contains(&self, type_id: TypeId) -> bool {
        self.map.contains_key(&type_id)
    }
}


#[async_trait::async_trait]
pub trait PromptFormatter: Send + Sync + 'static {
    async fn format(&self, input_variables: PromptArgs) -> Result<RenderResult, PromptError>;

    fn variables(&self) -> Option<Vec<(TypeId, &'static str)>> {
        None
    }

    async fn render(&self, input_variables: PromptArgs) -> Result<RenderResult, PromptError> {
        if let Some(variable) = self.variables() {
            for (id, name) in variable {
                if !input_variables.contains(id) {
                    return Err(PromptError::MissingVariable(name.into()));
                }
            }
        }
        self.format(input_variables).await
    }

    fn as_any(&self) -> &dyn std::any::Any;
}

impl<PA> From<PA> for Box<dyn PromptFormatter>
where
    PA: PromptFormatter + 'static,
{
    fn from(prompt: PA) -> Self {
        Box::new(prompt)
    }
}

#[allow(dead_code)]
pub struct PromptTemplate {
    template: String,
    variables: Vec<String>,
}

impl PromptTemplate {
    pub fn new(template: String, variables: Vec<String>) -> Self {
        Self {
            template,
            variables,
        }
    }
}

#[async_trait::async_trait]
pub trait PromptProvider: Send + Sync + 'static {
    async fn get_default_prompt_template(&self) -> Result<Arc<dyn PromptFormatter>, PromptError>;

    async fn get_prompt_template(
        &self,
        agent_name: &str,
    ) -> Result<Arc<dyn PromptFormatter>, PromptError>;

    fn as_any(&self) -> &dyn std::any::Any;
}

#[async_trait::async_trait]
impl PromptProvider for Arc<dyn PromptProvider> {
    async fn get_default_prompt_template(&self) -> Result<Arc<dyn PromptFormatter>, PromptError> {
        (**self).get_default_prompt_template().await
    }

    async fn get_prompt_template(
        &self,
        agent_name: &str,
    ) -> Result<Arc<dyn PromptFormatter>, PromptError> {
        (**self).get_prompt_template(agent_name).await
    }

    fn as_any(&self) -> &dyn std::any::Any {
        (**self).as_any()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::any::TypeId;

    #[test]
    fn insert_and_get() {
        let mut args = PromptArgs::new();
        args.insert(42u32);
        assert_eq!(args.get::<u32>(), Some(&42u32));
    }

    #[test]
    fn require_success_and_missing() {
        let mut args = PromptArgs::new();
        args.insert("hello".to_string());

        // Successfully retrieve an existing value.
        let s = args.require::<String>().unwrap();
        assert_eq!(s, "hello");

        // Attempt to require a missing variable.
        let err = args.require::<u32>().unwrap_err();
        match err {
            PromptError::MissingVariable(name) => assert_eq!(name, "u32"),
            other => panic!("Unexpected error variant: {:?}", other),
        }
    }

    #[test]
    fn get_or_default_returns_default_when_absent() {
        let args = PromptArgs::new();
        let val: String = args.get_or_default();
        assert_eq!(val, String::default());
    }

    #[test]
    fn get_or_default_returns_existing_when_present() {
        let mut args = PromptArgs::new();
        args.insert::<i32>(7);
        let val: i32 = args.get_or_default();
        assert_eq!(val, 7);
    }

    #[test]
    fn contains_reports_presence_correctly() {
        let mut args = PromptArgs::new();
        args.insert(1usize);
        assert!(args.contains(TypeId::of::<usize>()));
        assert!(!args.contains(TypeId::of::<u8>()));
    }

    #[test]
    fn can_insert_wrapped_string() {
        struct WrappedString(String);
        let mut args = PromptArgs::new();
        args.insert(WrappedString("test".to_string()));
        let res = args.require::<WrappedString>().unwrap();
        assert_eq!(res.0, "test");
    }
}
