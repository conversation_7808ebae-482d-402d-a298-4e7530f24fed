#[cfg(test)]
mod tests {

    use crate::agent::context::{
        CallingContext, CreateContextParams, ReActAgentContext, SessionRef,
    };
    use crate::core::task::TaskControl;
    use crate::core::tool::ToolCallingContext;
    use crate::core::{
        error::AgentError,
        llm::{LLMStreamData, PinBoxLLMStream, LLM},
        message::{FunctionCall, Message, ToolCall},
        provider::model::ModelProvider,
    };
    use crate::ioc::ioc::IOCContainer;
    use crate::language_models::openrouter::OpenRouter;
    use crate::llm::llm_stream_parser::{LLMStreamParseTaskHandle, LLMStreamParser};
    use crate::llm::options::CallOptions;
    use crate::llm::{error::LLMError, GenerateResult};
    use crate::prompt::{PromptError, PromptFormatter, PromptProvider};
    use crate::schemas::FunctionDefinition;
    use crate::tool::tool::{PinBoxToolStreamParam, ToolCallStreamFrame};
    use crate::tool::tool_box::Toolbox;
    use async_trait::async_trait;
    use futures::{Stream, StreamExt};
    use schemars::JsonSchema;
    use serde::{Deserialize, Serialize};
    use serde_json::json;

    use crate::core::llm::{DynLLMWrapper, LLMRunConfig};
    use crate::core::provider::model::{LLMProvider, LLMProviderError, LanguageModel};
    use std::sync::Mutex;
    use std::{borrow::Cow, pin::Pin, sync::Arc};
    use tokio::sync::mpsc::unbounded_channel;
    use tokio_stream::wrappers::UnboundedReceiverStream;

    // 通用的测试工具计数器trait
    pub trait TestToolCounter {
        fn get_call_count(&self) -> usize;
        fn get_stream_call_count(&self) -> usize;
    }

    // Mock Tool implementation for testing
    #[derive(Debug, Clone)]
    pub struct MockTool {
        pub name: String,
        pub call_count: Arc<std::sync::Mutex<usize>>,
        pub stream_call_count: Arc<std::sync::Mutex<usize>>,
        pub processed_params: Arc<Mutex<Vec<MockToolParam>>>,
    }

    #[derive(Debug, Clone, PartialEq, Deserialize, JsonSchema, Default)]
    pub struct MockToolParam {
        pub input: String,
    }

    #[derive(Debug, Clone, PartialEq, Serialize)]
    pub struct MockToolResult {
        pub output: String,
    }

    impl MockTool {
        pub fn new(name: &str) -> Self {
            Self {
                name: name.to_string(),
                call_count: Arc::new(std::sync::Mutex::new(0)),
                stream_call_count: Arc::new(std::sync::Mutex::new(0)),
                processed_params: Arc::new(Mutex::new(Vec::new())),
            }
        }

        fn get_processed_params(&self) -> Vec<MockToolParam> {
            self.processed_params.lock().unwrap().clone()
        }
    }

    impl TestToolCounter for MockTool {
        fn get_call_count(&self) -> usize {
            *self.call_count.lock().unwrap()
        }

        fn get_stream_call_count(&self) -> usize {
            *self.stream_call_count.lock().unwrap()
        }
    }

    #[async_trait]
    impl crate::core::tool::Tool for MockTool {
        const NAME: &'static str = "mock_tool";
        type Params = MockToolParam;
        type Result = MockToolResult;

        fn name(&self) -> Cow<str> {
            Cow::Owned(self.name.clone())
        }

        fn description(&self) -> Cow<str> {
            Cow::Borrowed("A mock tool for testing")
        }

        async fn call(
            &self,
            _ctx: ToolCallingContext,
            param: Option<Self::Params>,
        ) -> crate::core::tool::ToolResult<Self::Result> {
            *self.call_count.lock().unwrap() += 1;
            let input = param
                .map(|p| p.input)
                .unwrap_or_else(|| "default".to_string());
            Ok(MockToolResult {
                output: format!("processed: {}", input),
            })
        }

        async fn call_stream(
            &self,
            _ctx: ToolCallingContext,
            mut stream: Pin<Box<dyn Stream<Item = Self::Params> + Send>>,
        ) -> crate::core::tool::ToolResult<()> {
            *self.stream_call_count.lock().unwrap() += 1;

            // Process the stream
            while let Some(param) = stream.next().await {
                // Simulate processing each parameter
                self.processed_params.lock().unwrap().push(param);
            }

            Ok(())
        }
    }

    // Mock LLM implementation for testing
    #[derive(Clone)]
    struct MockLLM {
        tool_calls: Vec<ToolCall>,
    }

    impl MockLLM {
        fn new_with_tool_calls(tool_calls: Vec<ToolCall>) -> Self {
            Self { tool_calls }
        }

        fn new_empty() -> Self {
            Self {
                tool_calls: Vec::new(),
            }
        }
    }

    // Mock LLM with content streaming capability
    #[derive(Clone)]
    struct MockLLMWithContent {
        content_chunks: Vec<String>,
        tool_calls: Vec<ToolCall>,
    }

    impl MockLLMWithContent {
        fn new_with_content_and_tool_calls(
            content_chunks: Vec<String>,
            tool_calls: Vec<ToolCall>,
        ) -> Self {
            Self {
                content_chunks,
                tool_calls,
            }
        }
    }

    #[async_trait]
    impl LLM for MockLLMWithContent {
        type LLMConfig = LLMRunConfig;
        fn name(&self) -> &'static str {
            "mock_llm_with_content"
        }

        async fn generate(&self, _messages: &[Message]) -> Result<GenerateResult, LLMError> {
            let full_content = self.content_chunks.join("");
            Ok(GenerateResult {
                generation: full_content,
                tool_calls: Some(self.tool_calls.clone()),
                ..Default::default()
            })
        }

        async fn stream(&self, _messages: &[Message]) -> Result<PinBoxLLMStream, LLMError> {
            let (tx, rx) = unbounded_channel();

            let chunks = self.content_chunks.clone();
            let toolcalls = self.tool_calls.clone();
            let tx = tx.clone();
            // Send content chunks as stream data
            for content in &chunks {
                let _ = tx.send(Ok(LLMStreamData::new_content(content.clone())));
            }

            // Send tool calls as stream data
            for tool_call in &toolcalls {
                let _ = tx.send(Ok(LLMStreamData::new_tool_call(tool_call.clone())));
            }

            // Send finished signal
            let _ = tx.send(Ok(LLMStreamData::new_finished()));

            // Close the sender to signal end of stream

            Ok(Box::pin(UnboundedReceiverStream::new(rx)))
        }
    }

    #[async_trait]
    impl LLM for MockLLM {
        type LLMConfig = LLMRunConfig;
        fn name(&self) -> &'static str {
            "mock_llm"
        }

        async fn generate(&self, _messages: &[Message]) -> Result<GenerateResult, LLMError> {
            Ok(GenerateResult {
                generation: "test response".to_string(),
                tool_calls: Some(self.tool_calls.clone()),
                ..Default::default()
            })
        }

        async fn stream(&self, _messages: &[Message]) -> Result<PinBoxLLMStream, LLMError> {
            let (tx, rx) = unbounded_channel();

            // Send tool calls as stream data
            for tool_call in &self.tool_calls {
                let _ = tx.send(Ok(LLMStreamData::new_tool_call(tool_call.clone())));
            }

            // Send finished signal
            let _ = tx.send(Ok(LLMStreamData::new_finished()));

            Ok(Box::pin(UnboundedReceiverStream::new(rx)))
        }
    }

    // Mock ModelProvider
    struct MockModelProvider;

    impl ModelProvider for MockModelProvider {
        fn get_llm_model(
            &self,
            _provider: &LLMProvider,
        ) -> Result<LanguageModel, LLMProviderError> {
            // Return a mock LLM for testing
            let mock_llm = MockLLM::new_empty();
            Ok(Arc::new(DynLLMWrapper(mock_llm)))
        }
    }

    struct MockPromptProvider;
    #[async_trait]
    impl PromptProvider for MockPromptProvider {
        async fn get_default_prompt_template(
            &self,
        ) -> Result<Arc<dyn PromptFormatter>, PromptError> {
            todo!()
        }

        async fn get_prompt_template(
            &self,
            _agent_name: &str,
        ) -> Result<Arc<dyn PromptFormatter>, PromptError> {
            todo!()
        }

        fn as_any(&self) -> &dyn std::any::Any {
            self
        }
    }

    // Test configuration
    #[derive(Clone, Debug)]
    struct TestConfig {
        test_value: String,
    }

    impl Default for TestConfig {
        fn default() -> Self {
            Self {
                test_value: "test".to_string(),
            }
        }
    }

    // 通用测试辅助函数

    /// 创建包含指定工具的工具箱
    fn create_toolbox_with_tools<T: crate::core::tool::Tool + Send + Sync + 'static>(
        tools: Vec<T>,
    ) -> Arc<Toolbox> {
        let mut toolbox = Toolbox::new();
        for tool in tools {
            toolbox.add_tool(tool).expect("Failed to add tool");
        }
        Arc::new(toolbox)
    }

    /// 创建基本的测试消息
    fn create_test_messages(system_prompt: &str, user_message: &str) -> Vec<Message> {
        vec![
            Message::new_system_message(system_prompt.to_string()),
            Message::new_human_message(user_message.to_string()),
        ]
    }

    /// 创建带工具调用处理器的流解析器
    fn create_stream_parser_with_handler(
        ctx: CallingContext<TestConfig>,
        stream: PinBoxLLMStream,
    ) -> LLMStreamParseTaskHandle {
        LLMStreamParser::new()
            .with_toolcall_handler(move |toolcall: ToolCall, stream| {
                let ctx = ctx.clone();
                async move { ctx.call_tool_stream(toolcall.function.name, stream).await }
            })
            .process_stream(stream)
    }

    /// 创建完整的测试上下文（包含LLM、工具箱等）
    async fn create_full_test_context(
        llm: Arc<dyn LLM<LLMConfig = LLMRunConfig>>,
        toolbox: Option<Arc<Toolbox>>,
        agent_name: String,
        task_id: String,
    ) -> CallingContext<TestConfig> {
        let agent_context = ReActAgentContext {
            llm,
            llm_provider: Arc::new(MockModelProvider),
            name: agent_name.into(),
            prompt_provider: Arc::new(MockPromptProvider),
            toolbox: toolbox.clone(),
            service_registry: Arc::new(IOCContainer::new()),
            ..Default::default()
        };

        let container = Arc::new(IOCContainer::new());
        let config = Arc::new(TestConfig::default());

        CallingContext::create(CreateContextParams {
            session_ref: SessionRef::default(),
            agent_ref: Arc::new(agent_context),
            event_bus: None,
            container,
            config,
            toolbox,
            ctrl: TaskControl::default(),
            initial_message: None,
        })
        .await
        .unwrap()
    }

    // Helper function to create CallingContext for testing
    async fn create_test_context(llm: MockLLM) -> CallingContext<TestConfig> {
        create_test_context_with_toolbox(llm, None).await
    }

    // Helper function to create CallingContext with optional toolbox for testing
    async fn create_test_context_with_toolbox(
        llm: MockLLM,
        toolbox: Option<Arc<Toolbox>>,
    ) -> CallingContext<TestConfig> {
        let agent_context = ReActAgentContext {
            llm: Arc::new(llm),
            llm_provider: Arc::new(MockModelProvider),
            name: "test_agent".into(),
            prompt_provider: Arc::new(MockPromptProvider),
            toolbox: toolbox.clone(),
            service_registry: Arc::new(IOCContainer::new()),
            ..Default::default()
        };

        let container = Arc::new(IOCContainer::new());
        let config = Arc::new(TestConfig::default());

        CallingContext::create(CreateContextParams {
            session_ref: SessionRef::default(),
            agent_ref: Arc::new(agent_context),
            event_bus: None,
            container,
            config,
            toolbox: toolbox.clone(),
            ctrl: TaskControl::default(),
            initial_message: None,
        })
        .await
        .unwrap()
    }

    #[tokio::test]
    async fn test_tool_call_stream_functionality() {
        // Arrange: Create a mock tool and toolbox
        let mock_tool = MockTool::new("stream_test_tool");
        let tool_clone = mock_tool.clone();
        let toolbox = create_toolbox_with_tools(vec![mock_tool]);

        // Get the tool from toolbox to test call_tool_stream
        let dyn_tool = toolbox.get_tool("stream_test_tool").unwrap();

        // Create a stream of tool call frames
        let (tx, rx) = unbounded_channel();
        let test_tool_call = ToolCall {
            id: "stream_test_1".to_string(),
            index: 0,
            function: FunctionCall {
                name: "stream_test_tool".to_string(),
                arguments: json!({"input": "stream_test_value"}),
            },
        };
        tx.send(ToolCallStreamFrame(test_tool_call)).unwrap();
        drop(tx);

        let tool_stream: PinBoxToolStreamParam = Box::pin(UnboundedReceiverStream::new(rx));

        // Act: Call the tool stream method
        let result = dyn_tool
            .call_tool_stream(
                ToolCallingContext::new(Arc::new(IOCContainer::new())),
                tool_stream,
            )
            .await;

        // Assert: Should succeed
        assert!(
            result.is_ok(),
            "call_tool_stream should succeed: {:?}",
            result
        );
        assert_eq!(
            tool_clone.get_stream_call_count(),
            1,
            "Mock tool should have processed the stream once"
        );
        assert_eq!(
            tool_clone.get_call_count(),
            0,
            "Regular call should not have been made during stream processing"
        );
    }

    // Calculator Tool implementation for testing
    #[derive(Debug, Clone)]
    pub struct CalculatorTool {
        pub call_count: Arc<std::sync::Mutex<usize>>,
        pub stream_call_count: Arc<std::sync::Mutex<usize>>,
        pub processed_params: Arc<Mutex<Vec<CalculatorParam>>>,
    }

    #[derive(Debug, Clone, PartialEq, Deserialize, JsonSchema, Default)]
    pub struct CalculatorParam {
        pub operation: String,
        pub a: f64,
        pub b: f64,
    }

    #[derive(Debug, Clone, PartialEq, Serialize)]
    pub struct CalculatorResult {
        pub result: f64,
    }

    impl CalculatorTool {
        pub fn new() -> Self {
            Self {
                call_count: Arc::new(std::sync::Mutex::new(0)),
                stream_call_count: Arc::new(std::sync::Mutex::new(0)),
                processed_params: Arc::new(Mutex::new(Vec::new())),
            }
        }

        fn get_processed_params(&self) -> Vec<CalculatorParam> {
            self.processed_params.lock().unwrap().clone()
        }
    }

    impl TestToolCounter for CalculatorTool {
        fn get_call_count(&self) -> usize {
            *self.call_count.lock().unwrap()
        }

        fn get_stream_call_count(&self) -> usize {
            *self.stream_call_count.lock().unwrap()
        }
    }

    #[async_trait]
    impl crate::core::tool::Tool for CalculatorTool {
        const NAME: &'static str = "calculator";
        type Params = CalculatorParam;
        type Result = CalculatorResult;

        fn name(&self) -> Cow<str> {
            Cow::Borrowed("calculator")
        }

        fn description(&self) -> Cow<str> {
            Cow::Borrowed("A calculator tool that can perform basic arithmetic operations")
        }

        async fn call(
            &self,
            _ctx: ToolCallingContext,
            param: Option<Self::Params>,
        ) -> crate::core::tool::ToolResult<Self::Result> {
            *self.call_count.lock().unwrap() += 1;
            let param = param.ok_or_else(|| {
                crate::core::error::ToolError::ValidateError("Missing parameters".to_string())
            })?;

            let result = match param.operation.as_str() {
                "add" => param.a + param.b,
                "subtract" => param.a - param.b,
                "multiply" => param.a * param.b,
                "divide" => {
                    if param.b == 0.0 {
                        return Err(crate::core::error::ToolError::ValidateError(
                            "Division by zero".to_string(),
                        ));
                    }
                    param.a / param.b
                }
                _ => {
                    return Err(crate::core::error::ToolError::ValidateError(
                        "Unsupported operation".to_string(),
                    ));
                }
            };

            Ok(CalculatorResult { result })
        }

        async fn call_stream(
            &self,
            _ctx: ToolCallingContext,
            mut stream: Pin<Box<dyn Stream<Item = Self::Params> + Send>>,
        ) -> crate::core::tool::ToolResult<()> {
            *self.stream_call_count.lock().unwrap() += 1;

            // Process the stream
            while let Some(param) = stream.next().await {
                self.processed_params.lock().unwrap().push(param);
            }

            Ok(())
        }
    }

    #[tokio::test]
    #[ignore]
    async fn test_openrouter_calculator_with_stream() {
        dotenv::dotenv().ok();

        // Create calculator tool and toolbox
        let calculator = CalculatorTool::new();
        let toolbox = create_toolbox_with_tools(vec![calculator]);

        // Create function definitions for OpenRouter
        let func_def = toolbox
            .list_tools()
            .iter()
            .map(|t| FunctionDefinition {
                name: t.name().to_string(),
                description: t.description().to_string(),
                parameters: t.schema(),
            })
            .collect::<Vec<_>>();

        let llm =
            Arc::new(OpenRouter::new().with_options(CallOptions::new().with_functions(func_def)));
        let ctx = create_full_test_context(
            llm,
            Some(toolbox),
            "calculator_agent".to_string(),
            "calculator_task_id".to_string(),
        )
        .await;

        // Create messages and get LLM stream
        let messages = create_test_messages(
            "You are a helpful calculator assistant. Use the calculator tool to perform mathematical operations.",
            "Please calculate 15 + 27"
        );

        let llm = ctx.llm().unwrap();
        let resp = llm
            .stream(&messages)
            .await
            .map_err(|e| AgentError::InternalError(e.to_string()))
            .unwrap();

        // Process stream with parser
        let task = create_stream_parser_with_handler(ctx, resp);
        let generate_result = task.await_completion().await;

        // Verify results
        assert!(
            generate_result.is_ok(),
            "Should successfully extract generate result: {:?}",
            generate_result
        );
        let result = generate_result.unwrap();

        if let Some(tool_calls) = &result.tool_calls {
            assert!(!tool_calls.is_empty(), "Should have at least one tool call");
            let calculator_calls: Vec<_> = tool_calls
                .iter()
                .filter(|tc| tc.function.name == "calculator")
                .collect();
            if !calculator_calls.is_empty() {
                println!("Calculator tool was called with: {:?}", calculator_calls);
            }
        }
        println!(
            "Test completed successfully with generate_result: {:?}",
            result
        );
    }

    #[tokio::test]
    async fn test_stream_parser_with_finished_signal() {
        // Create mock LLM with content and tool calls
        let mock_llm = MockLLMWithContent::new_with_content_and_tool_calls(
            vec![
                "Processing request".to_string(),
                "Calling tool".to_string(),
                "Task completed".to_string(),
            ],
            vec![ToolCall {
                id: "test_call_1".to_string(),
                index: 0,
                function: FunctionCall {
                    name: "mock_tool".to_string(),
                    arguments: json!({"input": "test_input"}),
                },
            }],
        );

        // Create mock tool and toolbox
        let mock_tool = MockTool::new("mock_tool");
        let tool_clone = mock_tool.clone();
        let toolbox = create_toolbox_with_tools(vec![mock_tool]);

        // Create context and get stream
        let ctx = create_test_context_with_toolbox(MockLLM::new_empty(), Some(toolbox)).await;
        let stream = mock_llm.stream(&[]).await.unwrap();

        // Process stream with parser
        let task = create_stream_parser_with_handler(ctx, stream);
        let result = task.await_completion().await;

        // Verify results
        assert!(
            result.is_ok(),
            "Stream processing should complete successfully"
        );
        let generate_result = result.unwrap();

        assert_eq!(generate_result.generation, "Task completed");
        assert!(generate_result.tool_calls.is_some());

        let tool_calls = generate_result.tool_calls.unwrap();
        assert_eq!(tool_calls.len(), 1);
        assert_eq!(tool_calls[0].function.name, "mock_tool");
        assert_eq!(tool_clone.get_stream_call_count(), 1);
    }

    #[tokio::test]
    async fn test_stream_parser_without_finished_signal() {
        use tokio::time::Duration;

        // Create mock LLM with content and tool calls but no finished signal
        let mock_llm = MockLLMWithContent::new_with_content_and_tool_calls(
            vec![
                "Processing request".to_string(),
                "Calling tool".to_string(),
                "Task completed".to_string(),
            ],
            vec![ToolCall {
                id: "test_call_1".to_string(),
                index: 0,
                function: FunctionCall {
                    name: "mock_tool".to_string(),
                    arguments: json!({"input": "test_input"}),
                },
            }],
        );

        // Create mock tool and toolbox
        let mock_tool = MockTool::new("mock_tool");
        let tool_clone = mock_tool.clone();
        let toolbox = create_toolbox_with_tools(vec![mock_tool]);

        // Create context and get stream
        let ctx = create_test_context_with_toolbox(MockLLM::new_empty(), Some(toolbox)).await;
        let stream = mock_llm.stream(&[]).await.unwrap();

        // Process stream with parser and timeout
        let task = create_stream_parser_with_handler(ctx, stream);
        let result = tokio::time::timeout(Duration::from_secs(5), task.await_completion()).await;

        // Verify results
        assert!(
            result.is_ok(),
            "Stream processing should complete within timeout"
        );
        let generate_result = result.unwrap().unwrap();

        assert_eq!(generate_result.generation, "Task completed");
        assert!(generate_result.tool_calls.is_some());

        let tool_calls = generate_result.tool_calls.unwrap();
        assert_eq!(tool_calls.len(), 1);
        assert_eq!(tool_calls[0].function.name, "mock_tool");
        assert_eq!(tool_clone.get_stream_call_count(), 1);
    }

    #[tokio::test]
    async fn test_stream_parser_early_finished_signal() {
        // Create a mock LLM that sends finished signal early
        let (tx, rx) = unbounded_channel();

        // Send content and then finished signal
        let _ = tx.send(Ok(LLMStreamData::new_content("Content 1".to_string())));
        let _ = tx.send(Ok(LLMStreamData::new_content("Content 2".to_string())));
        let _ = tx.send(Ok(LLMStreamData::new_finished()));
        // Send more content after finished (should be ignored)
        let _ = tx.send(Ok(LLMStreamData::new_content("Content 3".to_string())));
        drop(tx);

        let stream: PinBoxLLMStream = Box::pin(UnboundedReceiverStream::new(rx));

        // Process stream with parser
        let task = LLMStreamParser::new().process_stream(stream);
        let result = task.await_completion().await;

        // Verify results
        assert!(
            result.is_ok(),
            "Stream processing should complete when Finished signal is received"
        );
        let generate_result = result.unwrap();
        assert_eq!(generate_result.generation, "Content 2");
    }

    #[tokio::test]
    async fn test_mock_calculator_with_stream() {
        // Create mock LLM with calculator tool calls
        let mock_llm = MockLLMWithContent::new_with_content_and_tool_calls(
            vec![
                "I'll calculate 15".to_string(),
                "I'll calculate 15 + 27 ".to_string(),
                "I'll calculate 15 + 27 for you.".to_string(),
            ],
            vec![
                ToolCall {
                    id: "calc_call_1".to_string(),
                    index: 0,
                    function: FunctionCall {
                        name: "calculator".to_string(),
                        arguments: json!({
                            "operation": "add",
                        }),
                    },
                },
                ToolCall {
                    id: "calc_call_2".to_string(),
                    index: 0,
                    function: FunctionCall {
                        name: "calculator".to_string(),
                        arguments: json!({
                            "operation": "add",
                            "a": 15.0,
                        }),
                    },
                },
                ToolCall {
                    id: "calc_call_1".to_string(),
                    index: 0,
                    function: FunctionCall {
                        name: "calculator".to_string(),
                        arguments: json!({
                            "operation": "add",
                            "a": 15.0,
                            "b": 27.0
                        }),
                    },
                },
            ],
        );

        // Create calculator tool and toolbox
        let calculator = CalculatorTool::new();
        let calculator_clone = calculator.clone();
        let toolbox = create_toolbox_with_tools(vec![calculator]);

        // Create context and get stream
        let ctx = create_full_test_context(
            Arc::new(mock_llm),
            Some(toolbox),
            "calculator_agent".to_string(),
            "calculator_task_id".to_string(),
        )
        .await;

        // Create messages and get LLM stream
        let messages = create_test_messages(
            "You are a helpful calculator assistant. Use the calculator tool to perform mathematical operations.",
            "Please calculate 15 + 27"
        );

        let llm = ctx.llm().unwrap();
        let resp = llm
            .stream(&messages)
            .await
            .map_err(|e| AgentError::InternalError(e.to_string()))
            .unwrap();

        // Process stream with parser
        let task = create_stream_parser_with_handler(ctx, resp);
        let generate_result = task.await_completion().await;

        // Verify results
        assert!(
            generate_result.is_ok(),
            "Should successfully extract generate result: {:?}",
            generate_result
        );

        let result = generate_result.unwrap();

        // Verify content was properly accumulated
        assert_eq!(result.generation, "I'll calculate 15 + 27 for you.");
        assert_eq!(
            result.tool_calls.iter().len(),
            1,
            "Should have exactly one tool call"
        );
        assert_eq!(
            result.tool_calls,
            Some(vec![ToolCall {
                id: "calc_call_1".to_string(),
                index: 0,
                function: FunctionCall {
                    name: "calculator".to_string(),
                    arguments: json!({
                        "operation": "add",
                        "a": 15.0,
                        "b": 27.0
                    }),
                },
            }])
        );

        // Verify calculator was called via stream
        assert_eq!(
            calculator_clone.get_processed_params(),
            vec![CalculatorParam {
                operation: "add".into(),
                a: 15.0,
                b: 27.0,
            }],
        );
    }

    // Timing Tool implementation for concurrent testing
    #[derive(Debug, Clone)]
    pub struct TimingTool {
        pub name: String,
        pub delay: tokio::time::Duration,
        pub call_count: Arc<std::sync::Mutex<usize>>,
        pub stream_call_count: Arc<std::sync::Mutex<usize>>,
        pub processed_params: Arc<Mutex<Vec<TimingParam>>>,
    }

    #[derive(Debug, Clone, PartialEq, Deserialize, JsonSchema, Default)]
    pub struct TimingParam {
        pub input: String,
    }

    #[derive(Debug, Clone, PartialEq, Serialize)]
    pub struct TimingResult {
        pub output: String,
    }

    impl TimingTool {
        pub fn new(name: &str, delay: tokio::time::Duration) -> Self {
            Self {
                name: name.to_string(),
                delay,
                call_count: Arc::new(std::sync::Mutex::new(0)),
                stream_call_count: Arc::new(std::sync::Mutex::new(0)),
                processed_params: Arc::new(Mutex::new(Vec::new())),
            }
        }

        fn get_processed_params(&self) -> Vec<TimingParam> {
            self.processed_params.lock().unwrap().clone()
        }
    }

    impl TestToolCounter for TimingTool {
        fn get_call_count(&self) -> usize {
            *self.call_count.lock().unwrap()
        }

        fn get_stream_call_count(&self) -> usize {
            *self.stream_call_count.lock().unwrap()
        }
    }

    #[async_trait]
    impl crate::core::tool::Tool for TimingTool {
        const NAME: &'static str = "timing_tool";
        type Params = TimingParam;
        type Result = TimingResult;

        fn name(&self) -> Cow<str> {
            Cow::Owned(self.name.clone())
        }

        fn description(&self) -> Cow<str> {
            Cow::Borrowed("A timing tool for testing concurrent execution")
        }

        async fn call(
            &self,
            ctx: ToolCallingContext,
            param: Option<Self::Params>,
        ) -> crate::core::tool::ToolResult<Self::Result> {
            *self.call_count.lock().unwrap() += 1;
            tokio::time::sleep(self.delay).await;
            let input = param
                .map(|p| p.input)
                .unwrap_or_else(|| "default".to_string());
            Ok(TimingResult {
                output: format!("processed: {}", input),
            })
        }

        async fn call_stream(
            &self,
            ctx: ToolCallingContext,
            mut stream: Pin<Box<dyn Stream<Item = Self::Params> + Send>>,
        ) -> crate::core::tool::ToolResult<()> {
            *self.stream_call_count.lock().unwrap() += 1;
            tokio::time::sleep(self.delay).await;

            while let Some(param) = stream.next().await {
                self.processed_params.lock().unwrap().push(param);
            }

            Ok(())
        }
    }

    #[tokio::test]
    async fn test_concurrent_tool_execution_during_stream() {
        use std::time::Instant;
        use tokio::time::Duration;

        // Create timing tools with different delays
        let timing_tool_1 = TimingTool::new("timing_tool_1", Duration::from_millis(100));
        let timing_tool_2 = TimingTool::new("timing_tool_2", Duration::from_millis(200));
        let timing_tool_3 = TimingTool::new("timing_tool_3", Duration::from_millis(50));

        let tool_1_clone = timing_tool_1.clone();
        let tool_2_clone = timing_tool_2.clone();
        let tool_3_clone = timing_tool_3.clone();

        // Create toolbox with timing tools
        let toolbox = create_toolbox_with_tools(vec![timing_tool_1, timing_tool_2, timing_tool_3]);

        // Create mock LLM with concurrent tool calls
        let mock_llm = MockLLMWithContent::new_with_content_and_tool_calls(
            vec![
                "Starting concurrent operations...".to_string(),
                "All operations completed".to_string(),
            ],
            vec![
                ToolCall {
                    id: "call_1".to_string(),
                    index: 0,
                    function: FunctionCall {
                        name: "timing_tool_1".to_string(),
                        arguments: json!({"input": "task_1"}),
                    },
                },
                ToolCall {
                    id: "call_2".to_string(),
                    index: 1,
                    function: FunctionCall {
                        name: "timing_tool_2".to_string(),
                        arguments: json!({"input": "task_2"}),
                    },
                },
                ToolCall {
                    id: "call_3".to_string(),
                    index: 2,
                    function: FunctionCall {
                        name: "timing_tool_3".to_string(),
                        arguments: json!({"input": "task_3"}),
                    },
                },
            ],
        );

        // Create context and get stream
        let ctx = create_test_context_with_toolbox(MockLLM::new_empty(), Some(toolbox)).await;
        let stream = mock_llm.stream(&[]).await.unwrap();

        // Record start time and process stream
        let start_time = Instant::now();
        let task = create_stream_parser_with_handler(ctx, stream);
        let result = task.await_completion().await;
        let elapsed = start_time.elapsed();

        // Verify results
        assert!(
            result.is_ok(),
            "Stream processing should complete successfully"
        );
        let generate_result = result.unwrap();

        assert_eq!(generate_result.generation, "All operations completed");
        assert!(generate_result.tool_calls.is_some());

        let tool_calls = generate_result.tool_calls.unwrap();
        assert_eq!(tool_calls.len(), 3);

        // Verify all tools were called
        assert_eq!(tool_1_clone.get_stream_call_count(), 1);
        assert_eq!(tool_2_clone.get_stream_call_count(), 1);
        assert_eq!(tool_3_clone.get_stream_call_count(), 1);

        // Verify concurrent execution (should be faster than sequential)
        // Sequential would take 100 + 200 + 50 = 350ms
        // Concurrent should take max(100, 200, 50) = 200ms + some overhead
        assert!(
            elapsed < Duration::from_millis(300),
            "Concurrent execution should be faster than sequential. Elapsed: {:?}",
            elapsed
        );

        println!(
            "Concurrent tool execution completed in {:?} (expected < 300ms)",
            elapsed
        );
    }

    #[tokio::test]
    async fn test_params_keys_check() {
        // Create mock LLM with tool calls containing extra parameters
        let mock_llm = MockLLMWithContent::new_with_content_and_tool_calls(
            vec!["Testing parameter validation".to_string()],
            vec![ToolCall {
                id: "test_call_1".to_string(),
                index: 0,
                function: FunctionCall {
                    name: "mock_tool".to_string(),
                    arguments: json!({
                        "extra_param": "should_be_ignored",
                        "input": "test_value",
                    }),
                },
            }],
        );

        // Create mock tool and toolbox
        let mock_tool = MockTool::new("mock_tool");
        let tool_clone = mock_tool.clone();
        let toolbox = create_toolbox_with_tools(vec![mock_tool]);

        // Create context and get stream
        let ctx = create_test_context_with_toolbox(MockLLM::new_empty(), Some(toolbox)).await;
        let stream = mock_llm.stream(&[]).await.unwrap();

        // Process stream with parser
        let task = create_stream_parser_with_handler(ctx, stream);
        let result = task.await_completion().await;

        // Verify results
        assert!(
            result.is_ok(),
            "Stream processing should complete successfully"
        );
        let generate_result = result.unwrap();

        assert_eq!(generate_result.generation, "Testing parameter validation");
        assert!(generate_result.tool_calls.is_some());

        let tool_calls = generate_result.tool_calls.unwrap();
        assert_eq!(tool_calls.len(), 1);
        assert_eq!(tool_calls[0].function.name, "mock_tool");
        assert_eq!(tool_clone.get_stream_call_count(), 1);

        // Verify the processed parameters contain only expected keys
        let processed_params = tool_clone.get_processed_params();
        assert_eq!(processed_params.len(), 1);
        // The extra_param should be ignored during deserialization
    }
}
