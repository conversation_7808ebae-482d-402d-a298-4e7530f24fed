use super::entity::RuleEntity;
use anyhow::Result;
use async_trait::async_trait;
use futures::stream::{self, StreamExt};
use serde::{Deserialize, Serialize};
use shaku::{Component, Interface};
use std::env;
use std::path::{Path, PathBuf};
use std::result;
use std::sync::Mutex;
use tokio::fs;
use tokio::fs::File;
use tokio::io::{self, AsyncReadExt};

// 定义规则内容的最大字节数
const MAX_RULE_SIZE: i32 = 20000_i32;

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct RuleParams {
    pub workspace_rule: String,
    pub global_rule: String,
}

#[derive(Debug)]
pub enum RuleError {
    GlobalRuleError(anyhow::Error),
    GlobalRuleLockError(String),
    WorkspaceRuleError(anyhow::Error),
    WorkspaceRuleLockError(String),
}

impl RuleError {
    pub fn error_type(&self) -> &'static str {
        match self {
            RuleError::GlobalRuleError(_) => "global_rule_error",
            RuleError::GlobalRuleLockError(_) => "global_rule_lock_error",
            RuleError::WorkspaceRuleError(_) => "workspace_rule_error",
            RuleError::WorkspaceRuleLockError(_) => "workspace_rule_lock_error",
        }
    }
}

impl std::fmt::Display for RuleError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            RuleError::GlobalRuleError(e) => write!(f, "Failed to read global rule file: {}", e),
            RuleError::GlobalRuleLockError(e) => write!(f, "Failed to lock global rule: {}", e),
            RuleError::WorkspaceRuleError(e) => {
                write!(f, "Failed to read workspace rule file: {}", e)
            }
            RuleError::WorkspaceRuleLockError(e) => {
                write!(f, "Failed to lock workspace rules: {}", e)
            }
        }
    }
}

impl std::error::Error for RuleError {}

#[async_trait]
pub trait RuleService: Interface {
    fn get_rule_context(&self) -> RuleParams;
    async fn load_rules(&self, folder: &str) -> Result<Vec<RuleError>, ()>;
    async fn parse_rule_file(&self, file_path: &Path) -> Result<RuleEntity>;
    fn get_all_rules(&self) -> Vec<RuleEntity>;
    async fn get_global_rule(&self) -> Result<String>;
}

#[derive(Debug, Component)]
#[shaku(interface = RuleService)]
pub struct RuleServiceImpl {
    #[shaku(default)]
    rules: Mutex<Vec<RuleEntity>>,
    #[shaku(default)]
    global_rule: Mutex<String>,
}

impl Default for RuleServiceImpl {
    fn default() -> Self {
        Self {
            rules: Mutex::new(Vec::new()),
            global_rule: Mutex::new(String::new()),
        }
    }
}

// 新增的通用按需读取函数
async fn read_file_with_limit(file_path: &Path, max_size: usize) -> Result<String> {
    use tokio::io::BufReader;

    let file = fs::File::open(file_path).await?;
    let reader = BufReader::new(file);
    let mut bytes = Vec::new();

    // 使用 take 限制读取的字节数
    let mut stream = reader.take(max_size as u64);
    let mut buffer = [0; 8192];

    loop {
        let bytes_read = stream.read(&mut buffer).await?;
        if bytes_read == 0 {
            break;
        }
        bytes.extend_from_slice(&buffer[..bytes_read]);
    }

    // 确保在有效的 UTF-8 字符边界处截断
    while !bytes.is_empty() && !String::from_utf8(bytes.clone()).is_ok() {
        bytes.pop();
    }

    Ok(String::from_utf8(bytes.clone())
        .unwrap_or_else(|_| String::from_utf8_lossy(&bytes).into_owned()))
}

#[async_trait]
impl RuleService for RuleServiceImpl {
    fn get_all_rules(&self) -> Vec<RuleEntity> {
        self.rules
            .lock()
            .unwrap_or_else(|poisoned| poisoned.into_inner())
            .clone()
    }

    fn get_rule_context(&self) -> RuleParams {
        let rules = self.get_all_rules();
        let workspace_rule = rules
            .iter()
            .map(|rule| rule.content.clone())
            .collect::<Vec<_>>()
            .join("\n");

        RuleParams {
            workspace_rule,
            global_rule: self.global_rule.lock().unwrap().clone(),
        }
    }

    async fn load_rules(&self, folder: &str) -> Result<Vec<RuleError>, ()> {
        let mut errors = Vec::new();

        // 先读取全局规则
        let global_rule_content = match self.get_global_rule().await {
            Ok(content) => content,
            Err(e) => {
                let err = RuleError::GlobalRuleError(e);
                errors.push(err);
                String::new()
            }
        };

        // 更新全局规则
        if let Ok(mut guard) = self.global_rule.lock() {
            *guard = global_rule_content;
        } else {
            let err =
                RuleError::GlobalRuleLockError("Failed to lock global rule for update".to_string());
            tracing::warn!("{}", err);
            errors.push(err);
        }

        // 再读取工作区规则
        let workspace_rules = if folder.is_empty() {
            tracing::info!("Workspace folder is empty: {:?}", folder);
            Vec::new()
        } else {
            let workspace_rule_path = Path::new(folder)
                .join(".trae")
                .join("rules")
                .join("project_rules.md");

            if !fs::try_exists(&workspace_rule_path).await.unwrap_or(false) {
                tracing::info!(
                    "Workspace rule file does not exist: {:?}",
                    workspace_rule_path
                );
                Vec::new()
            } else {
                match self.parse_rule_file(&workspace_rule_path).await {
                    Ok(rule) => {
                        if !rule.content.is_empty() {
                            vec![rule]
                        } else {
                            Vec::new()
                        }
                    }
                    Err(e) => {
                        let err = RuleError::WorkspaceRuleError(e);
                        tracing::warn!("Workspace rule parse failed: {}", err);
                        errors.push(err);
                        Vec::new()
                    }
                }
            }
        };
        // 更新工作区规则
        if let Ok(mut rules_lock) = self.rules.lock() {
            rules_lock.clear();
            rules_lock.extend(workspace_rules);
        } else {
            let err = RuleError::WorkspaceRuleLockError(
                "Failed to lock workspace rules for update".to_string(),
            );
            tracing::warn!("{}", err);
            errors.push(err);
        }

        Ok(errors)
    }

    async fn parse_rule_file(&self, file_path: &Path) -> Result<RuleEntity> {
        let global_rule_size = self.global_rule.lock().unwrap().len() as i32;
        let remaining_space = MAX_RULE_SIZE.saturating_sub(global_rule_size);

        if remaining_space == 0 {
            return Ok(RuleEntity::new(
                None,
                String::new(),
                file_path.to_path_buf(),
            ));
        }

        let content = read_file_with_limit(file_path, remaining_space as usize).await?;
        Ok(RuleEntity::new(None, content, file_path.to_path_buf()))
    }

    async fn get_global_rule(&self) -> Result<String> {
        let user_data_path = match env::var("ICUBE_USER_DATA_DIR") {
            Ok(path) => path,
            Err(e) => {
                tracing::info!("ICUBE_USER_DATA_DIR environment variable not found: {}", e);
                return Ok(String::new());
            }
        };

        let global_rule_path = Path::new(&user_data_path).join("user_rules.md");
        if !fs::try_exists(&global_rule_path).await.unwrap_or(false) {
            tracing::info!("Global rule file does not exist: {:?}", global_rule_path);
            return Ok(String::new());
        }

        // 限制为20000字节
        read_file_with_limit(&global_rule_path, MAX_RULE_SIZE as usize).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::os::unix::fs::PermissionsExt;
    use tempfile::tempdir;
    use tokio::fs::File;

    #[tokio::test]
    async fn test_get_all_rules_empty() {
        let service = RuleServiceImpl::default();
        let rules = service.get_all_rules();
        assert!(rules.is_empty());
    }

    #[tokio::test]
    async fn test_get_rule_context_empty() {
        let service = RuleServiceImpl::default();
        let context = service.get_rule_context();
        assert_eq!(context.workspace_rule, "");
    }

    #[tokio::test]
    async fn test_load_rules_empty_folder() {
        let service = RuleServiceImpl::default();
        let result = service.load_rules("").await;
        assert!(result.is_ok());
        assert!(service.get_all_rules().is_empty());
    }

    #[tokio::test]
    async fn test_load_rules_nonexistent_folder() {
        let service = RuleServiceImpl::default();
        let result = service
            .load_rules(&String::from("/nonexistent/folder"))
            .await;
        assert!(result.is_ok());
        assert!(service.get_all_rules().is_empty());
    }

    #[tokio::test]
    async fn test_parse_rule_file_success() {
        let dir = tempdir().unwrap();
        let file_path = dir.path().join("test.md");
        let content = "Test rule content";
        tokio::fs::write(&file_path, content).await.unwrap();

        let service = RuleServiceImpl::default();
        let result = service.parse_rule_file(&file_path).await;

        assert!(result.is_ok());
        let rule = result.unwrap();
        assert_eq!(rule.content, content);
        assert_eq!(rule.file_path, file_path);
    }

    #[tokio::test]
    async fn test_parse_rule_file_nonexistent() {
        let service = RuleServiceImpl::default();
        let result = service
            .parse_rule_file(Path::new("/nonexistent/file.md"))
            .await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_get_rule_context_with_rules() {
        let service = RuleServiceImpl::default();
        let mut rules = service.rules.lock().unwrap();
        rules.push(RuleEntity::new(
            None,
            "Rule 1".to_string(),
            PathBuf::from("rule1.md"),
        ));
        rules.push(RuleEntity::new(
            None,
            "Rule 2".to_string(),
            PathBuf::from("rule2.md"),
        ));
        drop(rules);

        let context = service.get_rule_context();
        assert_eq!(context.workspace_rule, "Rule 1\nRule 2");
    }

    #[tokio::test]
    async fn test_read_file_with_limit() {
        // 创建临时目录和测试文件
        let temp_dir = tempdir().unwrap();
        let file_path = temp_dir.path().join("test.txt");

        // 测试正常读取（文件大小小于限制）
        let content = "Hello, World!";
        fs::write(&file_path, content).await.unwrap();
        let result = read_file_with_limit(&file_path, 100).await;
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), content);

        // 测试限制读取（文件大小大于限制）
        let large_content = "A".repeat(1000);
        fs::write(&file_path, &large_content).await.unwrap();
        let result = read_file_with_limit(&file_path, 500).await;
        assert!(result.is_ok());
        assert_eq!(result.unwrap().len(), 500);

        // 测试读取空文件
        fs::write(&file_path, "").await.unwrap();
        let result = read_file_with_limit(&file_path, 100).await;
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "");

        // 测试读取不存在的文件
        let nonexistent_path = temp_dir.path().join("nonexistent.txt");
        let result = read_file_with_limit(&nonexistent_path, 100).await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_load_rules_with_global_rule_error() {
        let service = RuleServiceImpl::default();

        // 设置环境变量指向一个有权限问题的目录
        let dir = tempdir().unwrap();
        let user_data_dir = dir.path().join("user_data");
        fs::create_dir_all(&user_data_dir).await.unwrap();
        let global_rule_file = user_data_dir.join("user_rules.md");

        // 创建文件并写入内容
        File::create(&global_rule_file).await.unwrap();

        // 设置文件权限为不可读（仅在 Unix 系统上）
        #[cfg(unix)]
        std::fs::set_permissions(&global_rule_file, std::fs::Permissions::from_mode(0o000))
            .unwrap();

        std::env::set_var("ICUBE_USER_DATA_DIR", user_data_dir);

        let result = service.load_rules("").await;

        assert!(result.is_ok());
        let errors = result.unwrap();
        assert!(!errors.is_empty());
        assert!(matches!(errors[0], RuleError::GlobalRuleError(_)));

        // 清理：恢复文件权限以便删除
        #[cfg(unix)]
        std::fs::set_permissions(&global_rule_file, std::fs::Permissions::from_mode(0o644))
            .unwrap();
    }

    #[tokio::test]
    async fn test_load_rules_with_workspace_rule_error() {
        let dir = tempdir().unwrap();
        let trae_dir = dir.path().join(".trae").join("rules");
        fs::create_dir_all(&trae_dir).await.unwrap();
        let rule_file = trae_dir.join("project_rules.md");

        // 创建一个无法读取的文件（权限设置为0）
        File::create(&rule_file).await.unwrap();
        #[cfg(unix)]
        std::fs::set_permissions(&rule_file, std::fs::Permissions::from_mode(0o000)).unwrap();

        let service = RuleServiceImpl::default();
        let result = service
            .load_rules(dir.path().to_string_lossy().as_ref())
            .await;

        assert!(result.is_ok());
        let errors = result.unwrap();
        assert!(!errors.is_empty());
        assert!(matches!(errors[0], RuleError::WorkspaceRuleError(_)));

        // 清理：恢复文件权限以便删除
        #[cfg(unix)]
        std::fs::set_permissions(&rule_file, std::fs::Permissions::from_mode(0o644)).unwrap();
    }

    #[tokio::test]
    async fn test_load_rules_with_lock_errors() {
        let service = RuleServiceImpl::default();

        // 创建一个有效的工作区规则文件
        let dir = tempdir().unwrap();
        let trae_dir = dir.path().join(".trae").join("rules");
        fs::create_dir_all(&trae_dir).await.unwrap();
        let rule_file = trae_dir.join("project_rules.md");
        fs::write(&rule_file, "# Test Rule\nThis is a test rule")
            .await
            .unwrap();

        // 创建一个有效的全局规则文件
        let user_data_dir = tempdir().unwrap();
        fs::write(
            user_data_dir.path().join("user_rules.md"),
            "# Global Rule\nThis is a global rule",
        )
        .await
        .unwrap();
        std::env::set_var("ICUBE_USER_DATA_DIR", user_data_dir.path());

        // 获取并持有锁，以模拟锁失败的情况
        let _global_rule_lock = service.global_rule.lock().unwrap();
        let _workspace_rules_lock = service.rules.lock().unwrap();

        // 在另一个线程中尝试加载规则
        let result = service
            .load_rules(&dir.path().to_string_lossy().to_string())
            .await;

        assert!(result.is_ok());
        let errors = result.unwrap();

        // 验证是否包含锁错误
        assert_eq!(errors.len(), 2);
        assert!(errors
            .iter()
            .any(|e| matches!(e, RuleError::GlobalRuleLockError(_))));
        assert!(errors
            .iter()
            .any(|e| matches!(e, RuleError::WorkspaceRuleLockError(_))));
    }

    #[tokio::test]
    async fn test_load_rules_with_valid_rules() {
        let dir = tempdir().unwrap();
        let trae_dir = dir.path().join(".trae").join("rules");
        fs::create_dir_all(&trae_dir).await.unwrap();

        // 创建有效的工作区规则文件
        let rule_file = trae_dir.join("project_rules.md");
        fs::write(&rule_file, "# Test Rule\nThis is a test rule")
            .await
            .unwrap();

        // 设置有效的全局规则环境
        let user_data_dir = tempdir().unwrap();
        fs::write(
            user_data_dir.path().join("user_rules.md"),
            "# Global Rule\nThis is a global rule",
        )
        .await
        .unwrap();
        std::env::set_var("ICUBE_USER_DATA_DIR", user_data_dir.path());

        let service = RuleServiceImpl::default();
        let result = service
            .load_rules(&dir.path().to_string_lossy().to_string())
            .await;

        assert!(result.is_ok());
        let errors = result.unwrap();
        assert!(errors.is_empty());

        // 验证规则是否被正确加载
        let rules = service.get_all_rules();
        assert_eq!(rules.len(), 1);
        assert!(rules[0].content.contains("Test Rule"));

        let context = service.get_rule_context();
        assert!(context.global_rule.contains("Global Rule"));
    }
}
