use super::artifact::Artifacts;
use super::llm::LLM;
use super::message::{Message, MessageContent, MessageContents};
use super::{error::AgentError, task::TaskHandle};

use crate::core::provider::model::ModelProvider;
use crate::graph::builtin_action::interrupt::UserAction;
use crate::prompt::PromptFormatter;
use async_trait::async_trait;
use serde::Serialize;
use std::any::Any;
use std::sync::Arc;
use tokio_util::sync::CancellationToken;

type SerializerFn =
    Box<dyn Fn(&dyn Any) -> Result<serde_json::Value, serde_json::Error> + Send + Sync>;

pub struct DynData {
    data: Box<dyn Any + Send + Sync>,
    serializer: Option<SerializerFn>,
}

impl DynData {
    pub fn new<T: Any + Send + Sync + Serialize>(data: T) -> Self {
        let serializer = Box::new(|any: &dyn Any| {
            if let Some(typed_data) = any.downcast_ref::<T>() {
                serde_json::to_value(typed_data)
            } else {
                Err(serde_json::Error::io(std::io::Error::new(
                    std::io::ErrorKind::InvalidData,
                    "Type mismatch in DynData serializer",
                )))
            }
        });

        Self {
            data: Box::new(data),
            serializer: Some(serializer),
        }
    }

    pub fn downcast_ref<T: Any>(&self) -> Option<&T> {
        self.data.downcast_ref::<T>()
    }

    pub fn downcast<T: Any>(self) -> Result<T, String> {
        match self.data.downcast::<T>() {
            Ok(data) => Ok(*data),
            Err(_) => Err(format!(
                "Failed to downcast to type {}",
                std::any::type_name::<T>()
            )),
        }
    }

    // 仅在需要序列化时才调用，避免不必要的性能开销
    pub fn to_json_value(&self) -> Result<serde_json::Value, serde_json::Error> {
        if let Some(ref serializer) = self.serializer {
            serializer(self.data.as_ref())
        } else {
            Err(serde_json::Error::io(std::io::Error::new(
                std::io::ErrorKind::InvalidData,
                "No serializer available for DynData",
            )))
        }
    }
}

impl std::fmt::Debug for DynData {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("DynData")
            .field("type", &(*self.data).type_id())
            .finish()
    }
}

#[derive(Debug, Default)]
pub struct DynRunTaskRequest {
    pub message: MessageContents,
    pub(crate) initial_state: Option<DynData>,
    pub(crate) config: Option<DynData>,
    pub(crate) history: Vec<Message>,
    pub(crate) artifacts: Artifacts,
    pub(crate) cancellation_token: Option<CancellationToken>,
    pub session_context: SessionContext,
    pub agent_run_ids: Vec<String>,
    pub turns: u32,
}

#[derive(Debug, Clone, Default)]
pub struct SessionContext {
    pub session_id: String,
    pub message_id: String,
    pub user_message_id: String,
}

impl DynRunTaskRequest {
    pub fn new(message: impl Into<MessageContents>) -> Self {
        Self {
            message: message.into(),
            ..Default::default()
        }
    }
    pub fn with_initial_state<T: Any + Send + Sync + Serialize>(
        mut self,
        initial_state: T,
    ) -> Self {
        self.initial_state = Some(DynData::new(initial_state));
        self
    }

    pub fn with_config<T: Any + Send + Sync + Serialize>(mut self, config: T) -> Self {
        self.config = Some(DynData::new(config));
        self
    }

    pub fn with_history(mut self, history: Vec<Message>) -> Self {
        self.history = history;
        self
    }
    pub fn with_artifacts(mut self, artifacts: Artifacts) -> Self {
        self.artifacts = artifacts;
        self
    }
    pub fn with_cancellation_token(mut self, token: CancellationToken) -> Self {
        self.cancellation_token = Some(token);
        self
    }
    pub fn with_session_context(mut self, session_context: SessionContext) -> Self {
        self.session_context = session_context;
        self
    }
    pub fn with_initial_turns(mut self, turns: u32) -> Self {
        self.turns = turns;
        self
    }
    pub fn with_agent_run_ids(mut self, agent_run_ids: Vec<String>) -> Self {
        self.agent_run_ids = agent_run_ids;
        self
    }
}

impl From<String> for DynRunTaskRequest {
    fn from(value: String) -> Self {
        Self::new(value)
    }
}
impl From<&String> for DynRunTaskRequest {
    fn from(value: &String) -> Self {
        Self::new(value)
    }
}
impl From<&str> for DynRunTaskRequest {
    fn from(value: &str) -> Self {
        Self::new(value)
    }
}
impl From<MessageContent> for DynRunTaskRequest {
    fn from(value: MessageContent) -> Self {
        Self::new(value)
    }
}
impl From<MessageContents> for DynRunTaskRequest {
    fn from(value: MessageContents) -> Self {
        Self::new(value)
    }
}
#[async_trait]
pub trait AgentContext: Send + Sync + 'static {
    type LLMType: LLM;

    fn name(&self) -> String;
    fn llm(&self) -> Result<Arc<Self::LLMType>, AgentError>;
    fn llm_provider(&self) -> Arc<dyn ModelProvider>;
    async fn system_prompt(&self) -> Result<Box<dyn PromptFormatter>, AgentError>;
    fn get_agent_by_name(&self, name: &str) -> Result<Arc<dyn Agent>, AgentError>;
}

#[async_trait]
#[cfg_attr(any(test, feature = "testing"), mockall::automock)]
pub trait Agent: Send + Sync + 'static {
    fn name(&self) -> String;

    fn description(&self) -> String;

    fn ext_params(&self) -> Option<serde_json::Value> {
        None
    }

    async fn run_task(self: Arc<Self>, message: String) -> Result<TaskHandle, AgentError> {
        self.run_task_with_request(DynRunTaskRequest::new(message))
            .await
    }

    async fn run_task_with_request(
        self: Arc<Self>,
        request: DynRunTaskRequest,
    ) -> Result<TaskHandle, AgentError>;

    async fn resume_task(
        self: Arc<Self>,
        task_id: &str,
        user_action: UserAction,
    ) -> Result<TaskHandle, AgentError>;
    fn shutdown(self: Arc<Self>);
}
