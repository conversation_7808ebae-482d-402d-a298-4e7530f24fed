use crate::graph::node::{NodeHandle, NodeRef};
use crate::graph::{GraphError, StateGraph};
use serde::de::DeserializeOwned;
use serde::Serialize;
use serde_json::Value;
use std::marker::PhantomData;

pub trait InputOf {
    type In;
    fn get_ref(&self) -> NodeRef;
}
impl<I, O> InputOf for NodeHandle<I, O> {
    type In = I;

    fn get_ref(&self) -> NodeRef {
        self.get_ref()
    }
}

pub trait OutputOf {
    type Out;

    fn get_ref(&self) -> NodeRef;
}
impl<I, O> OutputOf for NodeHandle<I, O> {
    type Out = O;

    fn get_ref(&self) -> NodeRef {
        self.get_ref()
    }
}

pub struct NoFrom;
pub struct NoDefault;
pub struct HasDefault;

type PredictFn<GS, Out> = Box<dyn Fn(&GS, &Out) -> Option<Value> + Send + Sync>;

pub(crate) struct RouteRule<GS, Out> {
    pub(crate) predict: PredictFn<GS, Out>,
    pub(crate) destination: NodeRef,
}

pub struct RouterBuilder<'g, GS, C, Output, D = NoFrom>
where
    GS: Send + Sync + 'static,
    C: Send + Sync + 'static,
    Output: OutputOf,
{
    graph: &'g mut StateGraph<GS, C>,
    name: String,
    from: Option<&'g Output>,
    cases: Vec<RouteRule<GS, Output::Out>>,
    default: Option<NodeRef>,
    builder_state: PhantomData<D>,
}

// pub trait CaseFn<GS, C, In, Out>: Send + Sync + 'static {
//     fn call(&self, c: C, state: &GS, input: &Out) -> Option<In>;
// }
//
// impl<GS, C, In, Out, F> CaseFn<GS, C, In, Out> for F
// where
//     F: Fn(&Out) -> Option<In> + Send + Sync + 'static,
// {
//     #[inline]
//     fn call(&self, _c: C, _state: &GS, input: &Out) -> Option<In> {
//         (self)(input)
//     }
// }

impl<'g, GS, C, Output> RouterBuilder<'g, GS, C, Output>
where
    GS: Send + Sync + 'static,
    C: Send + Sync + 'static,
    Output: OutputOf,
    Output::Out: DeserializeOwned + Send + Sync + 'static,
{
    pub fn new(
        g: &'g mut StateGraph<GS, C>,
        name: String,
    ) -> RouterBuilder<'g, GS, C, Output, NoFrom> {
        RouterBuilder {
            graph: g,
            name,
            from: None,
            cases: Vec::new(),
            default: None,
            builder_state: PhantomData,
        }
    }

    pub fn from(mut self, from: &'g Output) -> RouterBuilder<'g, GS, C, Output, NoDefault> {
        self.from = Some(from);
        RouterBuilder {
            graph: self.graph,
            name: self.name,
            from: self.from,
            cases: self.cases,
            default: self.default,
            builder_state: PhantomData,
        }
    }
}

impl<'g, GS, C, Output> RouterBuilder<'g, GS, C, Output, NoDefault>
where
    GS: Send + Sync + 'static,
    C: Send + Sync + 'static,
    Output: OutputOf,
    Output::Out: DeserializeOwned + Send + Sync + 'static,
{
    pub fn case<F, Input>(mut self, f: F, dest: &Input) -> Self
    where
        Input: InputOf,
        F: Fn(&GS, &Output::Out) -> Option<Input::In> + Send + Sync + 'static,
        // F: for<'a> Fn(&'a GS, &'a Output::Out) -> Option<&'a Input::In> + Send + Sync + 'static, // use ref to avoid cloning
        // F: CaseFn<C, GS, Output::Out, Input::In> + Send + Sync + 'static,
        Input::In: Serialize + Sync + Send + 'static,
    {
        let wrap = move |st: &GS, input: &Output::Out| {
            f(st, input).map(|p| serde_json::to_value(p).unwrap())
        };
        self.cases.push(RouteRule {
            predict: Box::new(wrap),
            destination: dest.get_ref(),
        });
        self
    }

    // TODO: what default input should be?
    pub fn default<Input>(mut self, dest: &Input) -> RouterBuilder<'g, GS, C, Output, HasDefault>
    where
        Input: InputOf<In = ()>,
        // Input::In: Default + Serialize + Sync + Send + 'static,
    {
        self.default = Some(dest.get_ref());
        RouterBuilder {
            graph: self.graph,
            name: self.name,
            from: self.from,
            cases: self.cases,
            default: self.default,
            builder_state: PhantomData,
        }
    }
}

impl<'g, GS, C, Output> RouterBuilder<'g, GS, C, Output, HasDefault>
where
    GS: Send + Sync + 'static,
    C: Send + Sync + 'static,
    Output: OutputOf,
    Output::Out: DeserializeOwned + Send + Sync + 'static,
{
    pub fn build(self) -> Result<(), GraphError> {
        let default = self
            .default
            .ok_or_else(|| GraphError::NodeNotFound("default branch not found".to_string()))?;
        let from = self
            .from
            .ok_or_else(|| GraphError::NodeNotFound("from Node not found".to_string()))?;

        self.graph
            .add_router(self.name.into(), from.get_ref(), self.cases, default)?;
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use crate::core::error::AgentError;
    use crate::core::handler::StageHandler;
    use crate::graph::{RunGraph, GraphNodeInvokeDecision, StateGraph};
    use async_trait::async_trait;
    use serde_json::Value::Null;

    #[derive(Default)]
    struct TestState {
        v: Vec<String>,
    }

    struct TestContext;

    struct FirstHandler;
    struct SecondHandler;
    struct ThirdHandler;
    struct FinishHandler;

    #[async_trait]
    impl StageHandler<TestState, TestContext> for FirstHandler {
        type Incoming = ();
        type Outgoing = String;

        async fn handle(
            &self,
            _: TestContext,
            _state: &mut TestState,
            _input: Self::Incoming,
        ) -> Result<Self::Outgoing, AgentError> {
            Ok("second".into())
        }
    }

    #[async_trait]
    impl StageHandler<TestState, TestContext> for SecondHandler {
        type Incoming = String;
        type Outgoing = ();

        async fn handle(
            &self,
            _ctx: TestContext,
            state: &mut TestState,
            input: Self::Incoming,
        ) -> Result<Self::Outgoing, AgentError> {
            state.v.push(input);
            Ok(())
        }
    }

    #[async_trait]
    impl StageHandler<TestState, TestContext> for ThirdHandler {
        type Incoming = ();
        type Outgoing = ();

        async fn handle(
            &self,
            _ctx: TestContext,
            _state: &mut TestState,
            _input: Self::Incoming,
        ) -> Result<Self::Outgoing, AgentError> {
            Ok(())
        }
    }

    #[async_trait]
    impl StageHandler<TestState, TestContext> for FinishHandler {
        type Incoming = ();
        type Outgoing = ();

        async fn handle(
            &self,
            _ctx: TestContext,
            state: &mut TestState,
            _input: Self::Incoming,
        ) -> Result<Self::Outgoing, AgentError> {
            state.v.push("finish".into());
            Ok(())
        }
    }

    #[tokio::test]
    async fn test_router_builder() {
        let mut graph = StateGraph::<TestState, TestContext>::new();
        let first = graph.add_node(FirstHandler).unwrap();
        let second = graph.add_node(SecondHandler).unwrap();
        let third = graph.add_node(ThirdHandler).unwrap();
        let finish = graph.add_node(FinishHandler).unwrap();

        graph.connect(&second, &finish).unwrap();
        graph.connect(&third, &finish).unwrap();

        graph
            .switch("switch")
            .from(&first)
            .case(
                |_, input| (input == "second").then(|| "hello from first".to_string()),
                &second,
            )
            .case(|_, input| (input == "third").then_some(()), &third)
            .default(&finish)
            .build()
            .unwrap();

        let g = graph.compile().unwrap();
        let start = g.start_node();
        let start = g.next_node(start.index).unwrap();
        assert_eq!(start.index, first.get_ref().index);

        let mut current = g.start_node().index;
        let mut output = Null;
        let mut state = TestState::default();
        loop {
            let node = g.current_node(current).unwrap();
            let result = node.invoke(TestContext, &mut state, output).await;
            let (next, out) = match result {
                Ok(GraphNodeInvokeDecision::Continue(out)) => {
                    let next = g.next_node(current);
                    if let Some(next) = next {
                        (next.index, out)
                    } else {
                        break;
                    }
                }
                Ok(GraphNodeInvokeDecision::Goto(node_index, output)) => (node_index, output),
                Err(e) => {
                    // let box_any: AnyType = Box::new(e);
                    let res = serde_json::to_value(e)
                        .map_err(|e| AgentError::InternalError(e.to_string()))
                        .unwrap();
                    (g.error_node().index, res)
                }
                _ => {
                    break;
                }
            };
            current = next;
            output = out;
        }
        assert_eq!(
            state.v,
            vec!["hello from first".to_string(), "finish".to_string()]
        );
    }
}
