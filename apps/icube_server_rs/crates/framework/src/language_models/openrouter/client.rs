use async_openai::types::{
    ChatCompletionMessageToolCall, ChatCompletionRequestAssistantMessageArgs,
    ChatCompletionRequestMessage, ChatCompletionRequestSystemMessageArgs,
    ChatCompletionRequestToolMessageArgs, ChatCompletionRequestUserMessageArgs, ResponseFormat,
};
use async_trait::async_trait;
use futures::{Stream, StreamExt};
use jr::repair_json_object_v2;
use reqwest::Client;
use serde_json::Value;
use std::{collections::HashMap, pin::Pin, str};

use super::models::ApiResponse;
use super::OpenRouterError;
use crate::core::llm::{LLMRunConfig, LLMStreamData, LLM};
use crate::core::message::{FunctionCall, ToolCall};
use crate::llm::error::LLMError;
use crate::llm::options::CallOptions;
use crate::llm::{GenerateResult, TokenUsage};
use crate::schemas::{Message, MessageType};

use super::models::{OpenRouterMessage, Payload, Tool, ToolFunction};

#[derive(Debug, Clone)]
struct ToolCallAccumulator {
    id: Option<String>,
    name: Option<String>,
    arguments: String,
}

fn convert_message(val: crate::core::message::Message) -> Message {
    match val {
        crate::core::message::Message::HumanMessage(msg) => Message::new_human_message(msg),
        crate::core::message::Message::AIAssistant {
            content,
            tool_calls,
        } => {
            if let Some(calls) = tool_calls {
                if !calls.is_empty() {
                    let tools = calls
                        .iter()
                        .map(|tc| ChatCompletionMessageToolCall {
                            id: tc.id.clone(),
                            r#type: async_openai::types::ChatCompletionToolType::Function,
                            function: async_openai::types::FunctionCall {
                                name: tc.function.name.clone(),
                                arguments: serde_json::to_string(&tc.function.arguments)
                                    .expect("failed to serialize arguments"),
                            },
                        })
                        .collect::<Vec<_>>();

                    crate::schemas::Message::new_ai_message(content).with_tool_calls(
                        serde_json::to_value(tools).expect("failed to serialize tool calls"),
                    )
                } else {
                    crate::schemas::Message::new_ai_message(content)
                }
            } else {
                crate::schemas::Message::new_ai_message(content)
            }
        }
        crate::core::message::Message::SystemMessage(msg) => {
            crate::schemas::Message::new_system_message(msg)
        }
        crate::core::message::Message::ToolMessage { toolcall, result } => {
            let result_content = result.to_string();
            crate::schemas::Message::new_tool_message(result_content, toolcall.id)
        }
    }
}

pub enum OpenRouterModel {
    Claude4Sonnet,
    Claude4Opus,
    Claude35Sonnet,
    Claude35Haiku,
    Claude3Haiku,
    Claude3Opus,
    GPT4o,
    GPT4oMini,
    DeepseekV3,
}

impl std::fmt::Display for OpenRouterModel {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            OpenRouterModel::Claude4Sonnet => write!(f, "anthropic/claude-sonnet-4"),
            OpenRouterModel::Claude4Opus => write!(f, "anthropic/claude-opus-4"),
            OpenRouterModel::Claude35Sonnet => write!(f, "anthropic/claude-3.5-sonnet"),
            OpenRouterModel::Claude35Haiku => write!(f, "anthropic/claude-3.5-haiku"),
            OpenRouterModel::Claude3Haiku => write!(f, "anthropic/claude-3-haiku"),
            OpenRouterModel::Claude3Opus => write!(f, "anthropic/claude-3-opus"),
            OpenRouterModel::GPT4o => write!(f, "openai/gpt-4o"),
            OpenRouterModel::GPT4oMini => write!(f, "openai/gpt-4o-mini"),
            OpenRouterModel::DeepseekV3 => write!(f, "deepseek/deepseek-chat-v3-0324"),
        }
    }
}

#[derive(Clone)]
pub struct OpenRouter {
    model: String,
    options: CallOptions,
    api_key: String,
    base_url: String,
    json_mode: bool,
    app_name: String,
}

impl Default for OpenRouter {
    fn default() -> Self {
        Self::new()
    }
}

impl OpenRouter {
    pub fn new() -> Self {
        Self {
            model: OpenRouterModel::Claude4Sonnet.to_string(),
            options: CallOptions::default(),
            api_key: std::env::var("OPENROUTER_API_KEY").unwrap_or_default(),
            base_url: "https://openrouter.ai/api".to_string(),
            json_mode: false,
            app_name: "icube-framework".to_string(),
        }
    }

    pub fn with_model<S: Into<String>>(mut self, model: S) -> Self {
        self.model = model.into();
        self
    }

    pub fn with_options(mut self, options: CallOptions) -> Self {
        self.options = options;
        self
    }

    pub fn with_api_key<S: Into<String>>(mut self, api_key: S) -> Self {
        self.api_key = api_key.into();
        self
    }

    pub fn with_base_url<S: Into<String>>(mut self, base_url: S) -> Self {
        self.base_url = base_url.into();
        self
    }

    pub fn with_json_mode(mut self, json_mode: bool) -> Self {
        self.json_mode = json_mode;
        self
    }

    pub fn with_app_name<S: Into<String>>(mut self, app_name: S) -> Self {
        self.app_name = app_name.into();
        self
    }

    fn to_openai_messages(
        &self,
        messages: &[Message],
    ) -> Result<Vec<ChatCompletionRequestMessage>, LLMError> {
        let mut openai_messages: Vec<ChatCompletionRequestMessage> = Vec::new();
        for m in messages {
            match m.message_type {
                MessageType::AIMessage => openai_messages.push(match &m.tool_calls {
                    Some(value) => {
                        let function: Vec<ChatCompletionMessageToolCall> =
                            serde_json::from_value(value.clone()).unwrap();
                        ChatCompletionRequestAssistantMessageArgs::default()
                            .tool_calls(function)
                            .content(m.content.clone())
                            .build()
                            .unwrap()
                            .into()
                    }
                    None => ChatCompletionRequestAssistantMessageArgs::default()
                        .content(m.content.clone())
                        .build()?
                        .into(),
                }),
                MessageType::HumanMessage => {
                    openai_messages.push(
                        ChatCompletionRequestUserMessageArgs::default()
                            .content(m.content.clone())
                            .build()?
                            .into(),
                    );
                }
                MessageType::SystemMessage => {
                    openai_messages.push(
                        ChatCompletionRequestSystemMessageArgs::default()
                            .content(m.content.clone())
                            .build()?
                            .into(),
                    );
                }
                MessageType::ToolMessage => {
                    openai_messages.push(
                        ChatCompletionRequestToolMessageArgs::default()
                            .content(m.content.clone())
                            .tool_call_id(m.id.clone().unwrap_or_default())
                            .build()?
                            .into(),
                    );
                }
            }
        }
        Ok(openai_messages)
    }

    fn generate_request(&self, messages: &[Message], stream: bool) -> Result<Payload, LLMError> {
        let payload = self.build_payload(messages, stream, &LLMRunConfig::default());
        Ok(payload)
    }

    async fn generate(&self, messages: &[Message]) -> Result<GenerateResult, LLMError> {
        let client = Client::new();
        let is_stream = self.options.streaming_func.is_some();

        let payload = self.generate_request(messages, is_stream)?;

        let res = client
            .post(format!("{}/v1/chat/completions", self.base_url))
            .header("Authorization", format!("Bearer {}", self.api_key))
            .header("Content-Type", "application/json")
            .header("HTTP-Referer", "https://github.com/icube-ai/framework")
            .header("X-Title", &self.app_name)
            .json(&payload)
            .send()
            .await?;

        let status = res.status().as_u16();

        let res = match status {
            400 => {
                let error_text = res.text().await.unwrap_or_default();
                Err(LLMError::OpenRouterError(
                    OpenRouterError::InvalidFormatError(format!(
                        "Invalid request format: {}",
                        error_text
                    )),
                ))
            }
            401 => Err(LLMError::OpenRouterError(
                OpenRouterError::AuthenticationError("Invalid API Key".to_string()),
            )),
            402 => Err(LLMError::OpenRouterError(
                OpenRouterError::InsufficientCreditsError("Insufficient credits".to_string()),
            )),
            422 => Err(LLMError::OpenRouterError(
                OpenRouterError::InvalidParametersError("Invalid parameters".to_string()),
            )),
            429 => Err(LLMError::OpenRouterError(OpenRouterError::RateLimitError(
                "Rate limit reached".to_string(),
            ))),
            500 => Err(LLMError::OpenRouterError(OpenRouterError::ServerError(
                "Server error".to_string(),
            ))),
            503 => Err(LLMError::OpenRouterError(
                OpenRouterError::ServiceUnavailableError("Service unavailable".to_string()),
            )),
            _ => Ok(res.json::<ApiResponse>().await?),
        }?;

        let choice = res.choices.first();
        let result = match choice {
            Some(choice) => {
                let message = &choice.message;
                let content = message.content.clone();

                let tool_calls = message.tool_calls.as_ref().map(|calls| {
                    calls
                        .iter()
                        .enumerate()
                        .map(|(index, call)| ToolCall {
                            id: call.id.clone(),
                            index,
                            function: FunctionCall {
                                name: call.function.name.clone(),
                                arguments: serde_json::from_str(&call.function.arguments)
                                    .unwrap_or_default(),
                            },
                        })
                        .collect()
                });

                let usage = Some(TokenUsage {
                    prompt_tokens: res.usage.prompt_tokens,
                    completion_tokens: res.usage.completion_tokens,
                    total_tokens: res.usage.total_tokens,
                });

                GenerateResult {
                    generation: content,
                    tool_calls,
                    tokens: usage,
                    ..Default::default()
                }
            }
            None => GenerateResult::default(),
        };

        Ok(result)
    }

    fn build_payload(&self, messages: &[Message], stream: bool, config: &LLMRunConfig) -> Payload {
        let mut response_format = None;

        let processed_messages = messages
            .iter()
            .map(OpenRouterMessage::from_message)
            .collect::<Vec<_>>();

        // Convert function definitions to tools format first
        let tools = {
            if let Some(functions) = &config.tool_definitions {
                let f = functions
                    .values()
                    .map(|td| Tool {
                        tool_type: "function".to_string(),
                        function: ToolFunction {
                            name: td.name.clone(),
                            description: td.description.clone(),
                            parameters: td.parameters.clone(),
                        },
                    })
                    .collect::<Vec<_>>();
                Some(f)
            } else {
                self.options.functions.as_ref().map(|functions| {
                    functions
                        .iter()
                        .map(|f| Tool {
                            tool_type: "function".to_string(),
                            function: ToolFunction {
                                name: f.name.clone(),
                                description: f.description.clone(),
                                parameters: f.parameters.clone(),
                            },
                        })
                        .collect::<Vec<_>>()
                })
            }
        };

        // Handle JSON mode
        if self.json_mode {
            response_format = Some(ResponseFormat::JsonObject);
        }

        let mut payload = Payload {
            model: self.model.clone(),
            messages: processed_messages,
            max_tokens: self.options.max_tokens,
            stream: None,
            temperature: self.options.temperature,
            top_p: self.options.top_p,
            frequency_penalty: None,
            presence_penalty: None,
            stop: self.options.stop_words.clone(),
            response_format,
            route: None,
            anthropic_version: None,
            tools,
            tool_choice: None,
        };

        if stream {
            payload.stream = Some(true);
        }

        payload.frequency_penalty = self.options.frequency_penalty;
        payload.presence_penalty = self.options.presence_penalty;

        payload
    }

    fn parse_sse_chunk(chunk: &[u8]) -> Result<Vec<Value>, LLMError> {
        let chunk_str = str::from_utf8(chunk).map_err(|e| LLMError::ParsingError(e.to_string()))?;
        let mut results = Vec::new();

        for line in chunk_str.lines() {
            if let Some(data) = line.strip_prefix("data: ") {
                if data == "[DONE]" {
                    break;
                }
                match serde_json::from_str::<Value>(data) {
                    Ok(json) => results.push(json),
                    Err(_) => continue,
                }
            }
        }

        Ok(results)
    }

    fn extract_error_from_json(json_value: &Value) -> Option<LLMError> {
        json_value.get("error").map(|error| {
            let error_message = error
                .get("message")
                .and_then(|m| m.as_str())
                .unwrap_or("Unknown API error")
                .to_string();
            let error_code = error.get("code").and_then(|c| c.as_u64()).unwrap_or(0);
            LLMError::OtherError(format!("API error ({}): {}", error_code, error_message))
        })
    }

    fn extract_usage_from_chunk(chunk: &Value) -> Option<TokenUsage> {
        chunk.get("usage").map(|usage| TokenUsage {
            prompt_tokens: usage
                .get("prompt_tokens")
                .and_then(|t| t.as_u64())
                .unwrap_or(0) as u32,
            completion_tokens: usage
                .get("completion_tokens")
                .and_then(|t| t.as_u64())
                .unwrap_or(0) as u32,
            total_tokens: usage
                .get("total_tokens")
                .and_then(|t| t.as_u64())
                .unwrap_or(0) as u32,
        })
    }

    fn process_content_delta(
        delta: &Value,
        usage: &Option<TokenUsage>,
        generation: &mut String,
    ) -> Option<LLMStreamData> {
        delta
            .get("content")
            .and_then(|c| c.as_str())
            .filter(|content| !content.is_empty())
            .map(|content| {
                generation.push_str(content);
                let mut data = LLMStreamData::new_content(generation.clone());
                data.with_tokens(usage.clone());
                data
            })
    }

    fn process_tool_call_delta(
        delta: &Value,
        tool_call_accumulators: &mut HashMap<usize, ToolCallAccumulator>,
        usage: &Option<TokenUsage>,
    ) -> Vec<LLMStreamData> {
        let mut results = Vec::new();

        let Some(tool_calls) = delta.get("tool_calls").and_then(|tc| tc.as_array()) else {
            return results;
        };

        for tool_call in tool_calls {
            let Some(index) = tool_call.get("index").and_then(|i| i.as_u64()) else {
                continue;
            };

            let index = index as usize;
            let accumulator =
                tool_call_accumulators
                    .entry(index)
                    .or_insert_with(|| ToolCallAccumulator {
                        id: None,
                        name: None,
                        arguments: String::new(),
                    });

            // Accumulate tool call ID
            if let Some(id) = tool_call
                .get("id")
                .and_then(|i| i.as_str())
                .filter(|s| !s.is_empty())
            {
                accumulator.id = Some(id.to_string());
            }

            // Accumulate function information
            if let Some(function) = tool_call.get("function") {
                if let Some(name) = function
                    .get("name")
                    .and_then(|n| n.as_str())
                    .filter(|s| !s.is_empty())
                {
                    accumulator.name = Some(name.to_string());
                }
                if let Some(arguments) = function.get("arguments").and_then(|a| a.as_str()) {
                    accumulator.arguments.push_str(arguments);
                }
            }

            // Check if we have a complete tool call
            if let Some(name) = &accumulator.name {
                // FIXME: 这里在真实场景上需要一个 repairjson 的逻辑。目前就先从简了
                // toolcall 参数拼接和 content 参数拼接感觉可以统一放里面，或者放外面做。都行可以讨论下
                let json = {
                    if accumulator.arguments.is_empty() {
                        None
                    } else {
                        match repair_json_object_v2(&accumulator.arguments) {
                            Ok(json) => {
                                if serde_json::from_str::<Value>(&json).is_ok() {
                                    Some(json)
                                } else {
                                    None
                                }
                            }
                            Err(_) => None,
                        }
                    }
                };
                tracing::trace!("accumulator.arguments: {:?}", &accumulator.arguments);
                if let Some(json) = json {
                    tracing::trace!("json: {:?}", &json);
                    let tool_call = ToolCall {
                        index,
                        id: accumulator.id.clone().unwrap_or_default(),
                        function: FunctionCall {
                            name: name.clone(),
                            arguments: serde_json::from_str(&json).unwrap_or_default(),
                        },
                    };
                    let mut data = LLMStreamData::new_tool_call(tool_call);
                    data.with_tokens(usage.clone());
                    results.push(data);

                    // 不要在这里清除累积器，让它在 finish_reason 处理时清除
                    // 这样可以确保多个 chunk 中的 arguments 能够正确累积
                }
            }
        }

        results
    }

    fn process_finish_reason(choice: &Value, usage: &Option<TokenUsage>) -> Option<LLMStreamData> {
        choice
            .get("finish_reason")
            .and_then(|f| f.as_str())
            .filter(|reason| *reason == "tool_calls" || *reason == "stop")
            .map(|_| {
                let mut data = LLMStreamData::new_finished();
                data.with_tokens(usage.clone());
                data
            })
    }

    fn process_chunk_data(
        chunk: &Value,
        tool_call_accumulators: &mut HashMap<usize, ToolCallAccumulator>,
        generation: &mut String,
    ) -> Result<Vec<LLMStreamData>, LLMError> {
        // Check for errors first
        if let Some(error) = Self::extract_error_from_json(chunk) {
            return Err(error);
        }

        let usage = Self::extract_usage_from_chunk(chunk);
        let mut results = Vec::new();

        if let Some(choices) = chunk.get("choices").and_then(|c| c.as_array()) {
            if let Some(choice) = choices.first() {
                if let Some(delta) = choice.get("delta") {
                    // Process content
                    if let Some(content_data) =
                        Self::process_content_delta(delta, &usage, generation)
                    {
                        results.push(content_data);
                    }

                    // Process tool calls
                    let tool_call_data =
                        Self::process_tool_call_delta(delta, tool_call_accumulators, &usage);
                    results.extend(tool_call_data);

                    // 当收到 finish_reason 时，清除所有工具调用累积器
                    // 这确保了工具调用完成后状态被正确清理
                    if choice.get("finish_reason").and_then(|f| f.as_str()) == Some("tool_calls") {
                        tool_call_accumulators.clear();
                    }
                    // Process finish reason
                    if let Some(finish_data) = Self::process_finish_reason(choice, &usage) {
                        results.push(finish_data);
                    }
                }
            }
        }

        Ok(results)
    }

    async fn stream_internal(
        &self,
        messages: &[Message],
        stream_config: &LLMRunConfig,
    ) -> Result<Pin<Box<dyn Stream<Item = Result<LLMStreamData, LLMError>> + Send>>, LLMError> {
        let client = Client::new();
        let payload = self.build_payload(messages, true, stream_config);
        let request = client
            .post(format!("{}/v1/chat/completions", self.base_url))
            .header("Authorization", format!("Bearer {}", self.api_key))
            .header("Content-Type", "application/json")
            .header("HTTP-Referer", "https://github.com/icube-ai/framework")
            .header("X-Title", &self.app_name)
            .json(&payload)
            .build()?;

        let response = client.execute(request).await?;
        let mut stream = response.bytes_stream();

        use async_stream::stream;
        let processed_stream = stream! {
            let mut buffer = Vec::new();
            let mut tool_call_accumulators: HashMap<usize, ToolCallAccumulator> = HashMap::new();
            let mut generation = String::new();

            while let Some(chunk_result) = stream.next().await {
                match chunk_result {
                    Ok(chunk) => {
                        buffer.extend_from_slice(&chunk);

                        // Try to parse as direct JSON first (for error responses)
                        if let Ok(text) = str::from_utf8(&buffer) {
                            if let Ok(json_value) = serde_json::from_str::<Value>(text) {
                                if let Some(error) = Self::extract_error_from_json(&json_value) {
                                    yield Err(error);
                                    return;
                                }
                            }
                        }

                        // Try to parse SSE data
                        if let Ok(chunks) = Self::parse_sse_chunk(&buffer) {
                            buffer.clear();

                            for chunk in chunks {
                                match Self::process_chunk_data(&chunk, &mut tool_call_accumulators, &mut generation) {
                                    Ok(stream_data_list) => {
                                        for data in stream_data_list {
                                            yield Ok(data);
                                        }
                                    }
                                    Err(e) => {
                                        yield Err(e);
                                        return;
                                    }
                                }
                            }
                        }
                    }
                    Err(e) => {
                        yield Err(LLMError::OtherError(format!("Stream chunk error: {}", e)));
                        break;
                    }
                }
            }
        };

        Ok(Box::pin(processed_stream))
    }
}

#[async_trait]
impl LLM for OpenRouter {
    type LLMConfig = LLMRunConfig;
    fn name(&self) -> &'static str {
        "open-router"
    }

    async fn generate_with_config(
        &self,
        messages: &[crate::core::message::Message],
        config: &Self::LLMConfig,
    ) -> Result<GenerateResult, LLMError> {
        match &self.options.streaming_func {
            Some(func) => {
                let mut complete_response = String::new();
                let mut tool_calls: Option<Vec<ToolCall>> = None;
                let mut stream = self.stream(messages).await?;
                while let Some(data) = stream.next().await {
                    match data {
                        Ok(LLMStreamData::Content { content, .. }) => {
                            let mut func = func.lock().await;
                            let _ = func(content.clone()).await;
                            complete_response = content;
                        }
                        Ok(LLMStreamData::ToolCall { toolcall, .. }) => {
                            tool_calls = Some(vec![toolcall.clone()]);
                        }
                        Ok(LLMStreamData::Finished { .. }) => {
                            break;
                        }
                        Ok(LLMStreamData::Queuing(_)) => {}
                        Ok(LLMStreamData::FeeUsage(_)) => {}
                        Err(e) => return Err(e),
                    }
                }

                // For streaming, we need to parse the complete response for tool calls
                // This is a simplified implementation - you might want to enhance this
                let generate_result = GenerateResult {
                    generation: complete_response,
                    tool_calls,
                    ..Default::default()
                };
                Ok(generate_result)
            }
            None => {
                let client = Client::new();

                let final_messages: Vec<Message> = messages
                    .iter()
                    .map(|m| convert_message(m.clone()))
                    .collect();
                let payload = self.build_payload(&final_messages, false, config);

                let response = client
                    .post(format!("{}/v1/chat/completions", self.base_url))
                    .header("Authorization", format!("Bearer {}", self.api_key))
                    .header("Content-Type", "application/json")
                    .header("HTTP-Referer", "https://github.com/icube-ai/framework")
                    .header("X-Title", &self.app_name)
                    .json(&payload)
                    .send()
                    .await?;

                let status = response.status().as_u16();
                if status != 200 {
                    return Err(LLMError::OtherError(format!(
                        "OpenRouter API returned status code {}, detail: {}",
                        status,
                        response.text().await?
                    )));
                }
                let response_text = response.text().await?;
                let api_response: ApiResponse = serde_json::from_str(&response_text)
                    .map_err(|e| LLMError::ParsingError(e.to_string()))?;

                if let Some(choice) = api_response.choices.first() {
                    let content = &choice.message.content;
                    let usage = Some(TokenUsage {
                        prompt_tokens: api_response.usage.prompt_tokens,
                        completion_tokens: api_response.usage.completion_tokens,
                        total_tokens: api_response.usage.total_tokens,
                    });

                    // Enhanced tool call handling
                    let tool_calls = choice.message.tool_calls.as_ref().map(|calls| {
                        calls
                            .iter()
                            .enumerate()
                            .map(|(index, call)| ToolCall {
                                id: call.id.clone(),
                                index,
                                function: FunctionCall {
                                    name: call.function.name.clone(),
                                    arguments: serde_json::from_str(&call.function.arguments)
                                        .unwrap_or_default(),
                                },
                            })
                            .collect()
                    });

                    // Return the result even if content is empty

                    return Ok(GenerateResult {
                        generation: content.clone(),
                        tool_calls,
                        tokens: usage,
                        ..Default::default()
                    });
                }

                // If we get here, there were no choices
                Err(LLMError::OtherError(
                    "No choices in OpenRouter response".to_string(),
                ))
            }
        }
    }

    async fn generate(
        &self,
        messages: &[crate::core::message::Message],
    ) -> Result<GenerateResult, LLMError> {
        self.generate_with_config(messages, &LLMRunConfig::default())
            .await
    }
    async fn stream(
        &self,
        messages: &[crate::core::message::Message],
    ) -> Result<Pin<Box<dyn Stream<Item = Result<LLMStreamData, LLMError>> + Send>>, LLMError> {
        self.stream_with_config(messages, &LLMRunConfig::default())
            .await
    }

    async fn stream_with_config(
        &self,
        messages: &[crate::core::message::Message],
        config: &Self::LLMConfig,
    ) -> Result<Pin<Box<dyn Stream<Item = Result<LLMStreamData, LLMError>> + Send>>, LLMError> {
        let processed_messages: Vec<Message> = messages
            .iter()
            .map(|m| convert_message(m.clone()))
            .collect();
        self.stream_internal(&processed_messages, config).await
    }

    fn add_options(&mut self, options: CallOptions) {
        self.options = options;
    }

    fn with_options(&mut self, options: CallOptions) {
        self.options = options;
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::schemas::{Message, MessageType};

    #[tokio::test]
    #[ignore]
    async fn test_openrouter_generate() {
        let messages = vec![Message {
            content: "Hello".to_string(),
            message_type: MessageType::HumanMessage,
            id: Some("test_id".to_string()),
            images: None,
            tool_calls: None,
        }];

        let client = OpenRouter::new();
        let res = client.generate(&messages).await;
        assert!(res.is_ok());
    }

    #[tokio::test]
    #[ignore]
    async fn test_openrouter_stream() {
        let messages = vec![crate::core::message::Message::new_human_message(
            "Hello".to_string(),
        )];

        let client = OpenRouter::new();
        let res = client.stream(&messages).await;
        assert!(res.is_ok());
    }
}
