use std::{borrow::Cow, future::Future, pin::Pin};

use crate::core::{error::AgentError, handler::StageHand<PERSON>};
use serde_json::Value;

pub type FinishHandlerFuncResult = Pin<Box<dyn Future<Output = Result<(), AgentError>> + Send>>;
pub type FinishHandlerFunc<GS, C> =
    Box<dyn Fn(C, &mut GS, Value) -> FinishHandlerFuncResult + Sync + Send + 'static>;

pub struct GraphEndHandler<GS, C> {
    func: Option<FinishHandlerFunc<GS, C>>,
}

impl<GS, C> GraphEndHandler<GS, C> {
    pub fn new(func: Option<FinishHandlerFunc<GS, C>>) -> Self {
        Self { func }
    }
}

#[async_trait::async_trait]
impl<GS, C> StageHandler<GS, C> for GraphEndHandler<GS, C>
where
    GS: Send + Sync,
    C: Send + Sync + 'static,
{
    type Incoming = Value;
    type Outgoing = Value;

    fn name(&self) -> Cow<'static, str> {
        "__END__".into()
    }

    async fn handle(
        &self,
        ctx: C,
        state: &mut GS,
        input: Self::Incoming,
    ) -> Result<Self::Outgoing, AgentError> {
        let clone = input.clone();
        if let Some(ref func) = self.func {
            func(ctx, state, input).await?;
        }
        Ok(clone)
    }
}
