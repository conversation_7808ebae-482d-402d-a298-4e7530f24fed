use crate::agent::{<PERSON><PERSON><PERSON>, WorkComplete};
use crate::supervisor::util::ToolDefinitionUtil;
use crate::tools::filesystem::CreateRequirement;
use crate::utils::formatter::format_tool_call;
use async_trait::async_trait;
use framework::core::tool::Tool;
use framework::core::{
    error::AgentError,
    event::Event,
    handler::StageHandler,
    llm::{LLMGenerateResult, LLMRunConfig},
    message::Message,
};
use framework::graph::builtin_action::interrupt::{Interrupt, InterruptReason, Question};
use framework::tool::tool_box::ToolDefinition;
use framework::FunctionDefinition;
use serde_json::json;

use super::CallToolDecision;
use crate::{Context, SubAgentState};
use framework::prompt_args;

#[derive(Debug)]
pub struct PlanningStateHandler {}

#[async_trait]
impl StageHandler<SubAgentState, Context> for PlanningStateHandler {
    type Incoming = ();
    type Outgoing = CallToolDecision;

    async fn handle(
        &self,
        ctx: Context,
        state: &mut SubAgentState,
        _input: Self::Incoming,
    ) -> Result<Self::Outgoing, AgentError> {
        let llm = ctx.llm()?;

        let prompt = ctx.system_prompt().await?;
        let render_result = prompt.render(prompt_args! {}).await?;

        let mut messages = Vec::new();
        if !render_result.system_prompt.is_empty() {
            messages.push(Message::new_system_message(render_result.system_prompt));
        }

        if let Some(initial_message) = ctx.get_initial_message() {
            state.add_message(Message::new_human_message(initial_message.clone()));
        }

        messages.extend_from_slice(state.messages());
        state.turns += 1;

        let mut tool_defs = ctx.get_tool_definitions().clone();
        let finish_work_def: ToolDefinition = FunctionDefinition {
            name: "finish_work".into(),
            description: "when you finish work, you should call this method".to_string(),
            parameters: json!({
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "name of the agent",
                    }
                },
                "required": ["location"]
            }),
        }
        .into();
        tool_defs.insert("finish_work".to_string(), finish_work_def);

        ToolDefinitionUtil::append_thought_to_tool_definitions(&mut tool_defs);
        let llm_run_config = LLMRunConfig::default().with_tool_definitions(tool_defs);

        let resp = llm
            .generate_with_config(&messages, &llm_run_config)
            .await
            .map_err(|e| AgentError::InternalError(e.to_string()))?;

        // 创建 AI 消息
        let msg = Message::new_ai_message(resp.generation().into())
            .with_tool_calls(resp.tool_calls().map(|calls| calls.to_vec()));

        // 处理工具调用
        if let Some(tool_calls) = resp.tool_calls() {
            if let Some(call) = tool_calls.iter().next() {
                ctx.emit_message(Event::ResponseGenerated {
                    agent_name: ctx.name(),
                    agent_run_id: ctx.agent_run_id(),
                    response_text: format_tool_call(call),
                });

                if call.function.name.eq(CreateRequirement::NAME) {
                    return Ok(CallToolDecision::Interrupt(Interrupt::new(
                        ctx.task_id(),
                        InterruptReason::AskUser(Question {
                            task_id: ctx.task_id().to_string(),
                            agent_name: ctx.name().to_string(),
                            tool_call: call.clone(),
                            message: Some("confirm the requirement creation".to_string()),
                            payload: Some(serde_json::to_value(msg)?),
                        }),
                        vec![call.clone()],
                    )));
                }

                state.add_message(msg);

                if call.function.name.eq("finish_work") {
                    return Ok(CallToolDecision::WorkComplete(WorkComplete {
                        message: serde_json::to_string(&call.function.arguments)
                            .unwrap_or_default(),
                    }));
                }

                return Ok(CallToolDecision::CallTool(CallTool {
                    tool_call: call.clone(),
                }));
            }
        }

        state.add_message(msg);
        Ok(CallToolDecision::RePlan)
    }
}
