use crate::ioc::{IOCContainer, Injectable};
use crate::llm::error::LLMError;
use crate::schemas::FunctionDefinition;
use crate::tool::tool::PinBoxToolStreamParam;
use crate::tool::tool_box::ToolDefinitionTag;

use super::artifact::Artifact;
use super::error::ToolError;
use async_trait::async_trait;
use futures::Stream;
use schemars::{schema_for, JsonSchema};
use serde::de::DeserializeOwned;
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::borrow::Cow;
use std::collections::HashSet;
use std::pin::Pin;
use std::sync::Arc;

pub type ToolResult<T> = std::result::Result<T, ToolError>;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub enum CallToolResponse {
    Fail { code: i32, error_message: String },
    Success(Box<SuccessResponse>),
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SuccessResponse {
    pub code: i32,
    pub artifact: Option<Artifact>,
    pub toolmessage_result: serde_json::Value,
    pub original_result: serde_json::Value,
}

impl CallToolResponse {
    pub fn success(
        toolmessage_result: serde_json::Value,
        original_result: serde_json::Value,
    ) -> Self {
        Self::Success(Box::new(SuccessResponse {
            code: 0,
            artifact: None,
            toolmessage_result,
            original_result,
        }))
    }

    pub fn fail(e: impl Into<String>) -> Self {
        Self::Fail {
            code: 1,
            error_message: e.into(),
        }
    }
    pub fn with_artifacts(&mut self, artifact: Artifact) -> &mut Self {
        if let Self::Success(success) = self {
            success.artifact = Some(artifact);
        }
        self
    }

    pub fn artifact(&self) -> Option<Artifact> {
        match self {
            Self::Success(success) => success.artifact.clone(),
            _ => None,
        }
    }

    pub fn toolmessage_result(&self) -> Value {
        match self {
            Self::Success(success) => success.toolmessage_result.clone(),
            _ => Value::Null,
        }
    }

    pub fn original_result(&self) -> Value {
        match self {
            Self::Success(success) => success.original_result.clone(),
            _ => Value::Null,
        }
    }
}

#[async_trait]
pub trait Tool {
    /// The name of the tool. This name should be unique.
    const NAME: &'static str;

    type Params: DeserializeOwned + Send + Sync + JsonSchema + Clone + Default;
    type Result: Serialize + Sync + Send;

    fn name(&self) -> Cow<str> {
        Cow::Borrowed(Self::NAME)
    }
    fn description(&self) -> Cow<str>;
    fn alias(&self) -> Option<HashSet<Cow<str>>> {
        None
    }
    fn tag(&self) -> ToolDefinitionTag {
        ToolDefinitionTag::Normal
    }
    async fn call(
        &self,
        ctx: ToolCallingContext,
        param: Option<Self::Params>,
    ) -> ToolResult<Self::Result>;

    async fn handle_error(
        &self,
        _ctx: ToolCallingContext,
        _param: Option<Self::Params>,
        err: LLMError,
    ) -> ToolResult<Self::Result> {
        // 默认不处理 LLM 异常，直接返回异常
        Err(ToolError::LLMError(err))
    }

    fn schema(&self) -> Value {
        let mut schema = json!(schema_for!(Self::Params));
        // 移除 $schema 字段
        if let Some(obj) = schema.as_object_mut() {
            obj.remove("$schema");
        }
        schema
    }

    /// 将结果转换为 tool_message 中可见的 result，这里可以做一些裁剪
    async fn to_toolmessage_result(
        &self,
        // 可能需要拿 config
        _ctx: ToolCallingContext,
        _param: Option<&Self::Params>,
        result: &Self::Result,
    ) -> Result<Value, serde_json::Error> {
        serde_json::to_value(result)
    }

    /// 将结果转换为 artifact
    fn to_artifact(
        &self,
        // 可能需要拿 config
        _ctx: ToolCallingContext,
        _param: Option<&Self::Params>,
        _result: &Self::Result,
    ) -> Option<Artifact> {
        None
    }

    // 流式调用
    async fn call_stream(
        &self,
        _ctx: ToolCallingContext,
        _stream: Pin<Box<dyn Stream<Item = Self::Params> + Send>>,
    ) -> ToolResult<()> {
        Ok(())
    }

    async fn definition(&self, _ctx: ToolCallingContext) -> Vec<FunctionDefinition> {
        let def = FunctionDefinition {
            name: self.name().to_string(),
            description: self.description().to_string(),
            parameters: self.schema(),
        };
        vec![def]
    }
}

#[async_trait]
pub trait DynTool: Sync + Send + 'static {
    fn name(&self) -> Cow<str>;
    fn tag(&self) -> ToolDefinitionTag;
    fn description(&self) -> Cow<str>;
    async fn call_tool(
        &self,
        ctx: ToolCallingContext,
        param: Option<serde_json::Value>,
    ) -> ToolResult<CallToolResponse>;
    /// 如果 llm 异常时进行处理，可以返回兜底结果
    async fn handle_error(
        &self,
        _ctx: ToolCallingContext,
        // 已经发出的参数
        _params: Option<serde_json::Value>,
        err: LLMError,
    ) -> ToolResult<CallToolResponse> {
        Err(ToolError::LLMError(err))
    }
    async fn call_tool_stream(
        &self,
        ctx: ToolCallingContext,
        stream: PinBoxToolStreamParam,
    ) -> ToolResult<()>;

    fn alias(&self) -> Option<HashSet<Cow<str>>> {
        None
    }
    fn schema(&self) -> Value;
    async fn definition(&self, ctx: ToolCallingContext) -> Vec<FunctionDefinition>;
}

#[derive(Clone)]
pub struct ToolCallingContext {
    container: Arc<IOCContainer>,
    agent_run_id: Option<String>,
    agent_type: Option<String>,
}
impl ToolCallingContext {
    pub fn new(container: Arc<IOCContainer>) -> Self {
        Self {
            container,
            agent_run_id: None,
            agent_type: None,
        }
    }

    pub fn get<T: Injectable>(&self) -> &T {
        self.container.get::<T>()
    }

    pub fn builder(container: Arc<IOCContainer>) -> ToolCallingContextBuilder {
        ToolCallingContextBuilder::new(container)
    }

    pub fn get_agent_run_id(&self) -> Option<String> { self.agent_run_id.clone() }
    pub fn get_agent_type(&self) -> Option<String> { self.agent_type.clone() }
}

pub struct ToolCallingContextBuilder {
    container: IOCContainer,
    agent_run_id: Option<String>,
    agent_type: Option<String>,
}
impl ToolCallingContextBuilder {
    pub fn new(container: Arc<IOCContainer>) -> Self {
        Self {
            container: container.create_child(),
            agent_run_id: None,
            agent_type: None,
        }
    }
    pub fn with_run_config<RunConfig: Injectable>(mut self, config: RunConfig) -> Self {
        self.container.register::<RunConfig>(config);
        self
    }

    pub fn with_agent_run_id(mut self, agent_run_id: String) -> Self {
        self.agent_run_id = Some(agent_run_id);
        self
    }

    pub fn with_agent_type(mut self, agent_type: String) -> Self {
        self.agent_type = Some(agent_type);
        self
    }

    pub fn build(self) -> ToolCallingContext {
        ToolCallingContext {
            container: Arc::new(self.container),
            agent_run_id: self.agent_run_id,
            agent_type: self.agent_type,
        }
    }
}
