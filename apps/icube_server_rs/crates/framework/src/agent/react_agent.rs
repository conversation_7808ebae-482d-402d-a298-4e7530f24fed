use super::builder::GraphBuilder;
use super::builder::{<PERSON><PERSON><PERSON><PERSON>, ArcOutputMapping};
use super::context::ReActAgentContext;
use super::context::{CallingContext, CreateContextParams};
use super::execution::RunTaskRequest;
use super::state::RunState;
use crate::agent::execution::Execution;
use crate::agent::runner::TaskRunner;
use crate::core::agent::{Agent, DynRunTaskRequest};
use crate::core::checkpoint::{CheckPointer, GraphState};
use crate::core::error::AgentError;
use crate::core::event::Event;
use crate::core::llm::{LLMRunConfig, LLM};
use crate::core::message::MessageContents;
use crate::core::provider::model::ModelProvider;
use crate::core::task::{CombinedCancellationTokenBuilder, TaskControl, TaskHandle};
use crate::graph::builtin_action::interrupt::UserAction;
use crate::graph::{CompiledGraph, RunGraph};
use crate::history::{HistoryRecorder, HistoryStore};
use crate::ioc::IOCContainer;
use crate::prompt::PromptProvider;
use crate::tool::tool_box::Toolbox;
use async_trait::async_trait;
use indexmap::IndexMap;
use serde::de::DeserializeOwned;
use serde::Serialize;
use std::borrow::Cow;
use std::fmt::Debug;
use std::sync::Arc;
use tokio::sync::mpsc::unbounded_channel;
use tokio_stream::wrappers::UnboundedReceiverStream;
use tokio_util::sync::CancellationToken;
use tracing::Instrument;

pub(crate) type ReActAgentGraph<S, Config> = CompiledGraph<RunState<S>, CallingContext<Config>>;

pub struct ReActAgent<S, Config = ()> {
    pub name: Cow<'static, str>,
    pub description: String,
    pub ext_params: Option<serde_json::Value>,
    pub llm: Arc<dyn LLM<LLMConfig = LLMRunConfig>>,
    pub llm_provider: Arc<dyn ModelProvider>,
    pub prompt_provider: Arc<dyn PromptProvider>,
    pub check_pointer: Arc<dyn CheckPointer<S, Config>>,
    pub graph: Option<Arc<ReActAgentGraph<S, Config>>>,
    pub cancellation_token: CancellationToken,
    pub toolbox: Option<Arc<Toolbox>>,
    pub members: Vec<Arc<dyn Agent>>,
    pub container: Arc<IOCContainer>,
    pub output_mapping: Option<ArcOutputMapping<S>>,
    pub history_store: Option<Arc<dyn HistoryStore<Config>>>,
}

impl<S, Config> ReActAgent<S, Config>
where
    S: Debug + Clone + Default + Serialize + DeserializeOwned + Sync + Send + 'static,
    Config: Debug + Clone + Default + DeserializeOwned + Sync + Send + 'static,
{
    pub fn builder<L, GB, LP, PP>() -> AgentBuilder<L, GB, S, Config, LP, PP>
    where
        GB: GraphBuilder<RunState<S>, Config> + 'static,
        LP: ModelProvider,
        L: LLM<LLMConfig = LLMRunConfig>,
        PP: PromptProvider,
    {
        AgentBuilder::new()
    }

    fn context(&self, task: &Execution<S, Config>) -> Arc<ReActAgentContext<Config>> {
        let agents = self
            .members
            .iter()
            .map(|a| (a.name(), Arc::clone(a)))
            .collect::<IndexMap<String, Arc<dyn Agent>>>();

        let history_recorder = self.history_store.as_ref().map(|store| {
            Arc::new(HistoryRecorder::new(
                store.clone(),
                task.history_pendings.clone(),
            ))
        });

        Arc::new(ReActAgentContext {
            name: self.name.clone(),
            llm: self.llm.clone(),
            llm_provider: self.llm_provider.clone(),
            toolbox: self.toolbox.clone(),
            prompt_provider: self.prompt_provider.clone(),
            agents,
            service_registry: self.container.clone(),
            history_recorder,
        })
    }

    pub async fn run_task(
        self: Arc<Self>,
        message: impl Into<RunTaskRequest<S, Config>>,
    ) -> Result<TaskHandle, AgentError> {
        self.run_task_with_request(message.into()).await
    }

    async fn run_task_with_request(
        self: Arc<Self>,
        request: RunTaskRequest<S, Config>,
    ) -> Result<TaskHandle, AgentError> {
        let graph = self.get_graph()?;

        let state = RunState::new(request.initial_state)
            .with_history(&request.history)
            .with_artifacts(request.artifacts)
            .with_turns(request.turns);

        // state.add_message(Message::new_human_message(request.message.clone()));

        let start_node = graph.start_node();
        let node_name = graph.node_name(start_node.index);
        let task = Execution::builder()
            .state(state)
            .graph_state(GraphState::new(node_name.as_ref()))
            .config(request.config)
            .session_id(request.session_context.session_id)
            .message_id(request.session_context.message_id)
            .user_message_id(request.session_context.user_message_id)
            .agent_run_ids(request.agent_run_ids)
            .build()
            .unwrap();
        let checkpoint = task.make_checkpoint();
        self.check_pointer.save(&checkpoint).await?;
        self.run_execution_task(task, request.cancellation_token, Some(request.message))
            .await
    }

    async fn run_execution_task(
        self: Arc<Self>,
        task: Execution<S, Config>,
        cancellation_token: Option<CancellationToken>,
        initial_message: Option<MessageContents>,
    ) -> Result<TaskHandle, AgentError> {
        let (output_tx, output_rx) = unbounded_channel();

        let agent_context = self.context(&task);
        let mut container = self.container.clone().create_child();
        container.register(task.config.clone());

        // 合并 cancel token
        let mut combined_token_builder = CombinedCancellationTokenBuilder::new();
        combined_token_builder.add_token(self.cancellation_token.child_token());
        if let Some(token) = cancellation_token {
            combined_token_builder.add_token(token);
        }
        let ctrl = TaskControl::new(task.id.clone(), combined_token_builder.build().token());
        let ctx = CallingContext::create(CreateContextParams {
            session_ref: task.get_session_ref(),
            agent_ref: agent_context.clone(),
            event_bus: Some(output_tx),
            container: Arc::new(container),
            config: task.config.clone(),
            toolbox: self.toolbox.clone(),
            ctrl: ctrl.clone(),
            initial_message: initial_message.clone(),
        })
        .await?;

        tokio::spawn(
            {
                let graph = self.get_graph()?.clone();
                let check_pointer = self.check_pointer.clone();
                let ctrl = ctrl.clone();
                async move {
                    let mut runner = TaskRunner::new(graph, check_pointer);
                    let turns = task.state.turns;
                    if let Err(e) = runner.run(ctx.clone(), task, ctrl).await {
                        ctx.emit_message(Event::ErrorOccurred { error: e, turns });
                    }
                }
            }
            .in_current_span(),
        );

        Ok(TaskHandle::new(
            UnboundedReceiverStream::new(output_rx),
            ctrl,
        ))
    }

    fn get_graph(&self) -> Result<&Arc<ReActAgentGraph<S, Config>>, AgentError> {
        self.graph.as_ref().ok_or(AgentError::GraphNotInitialized)
    }
}

#[async_trait]
impl<S, Config> Agent for ReActAgent<S, Config>
where
    S: Clone + Debug + Serialize + Sync + Send + 'static + Default + DeserializeOwned,
    Config: Default + Clone + DeserializeOwned + Debug + Sync + Send + 'static,
{
    fn name(&self) -> String {
        self.name.to_string()
    }

    fn description(&self) -> String {
        self.description.clone()
    }

    fn ext_params(&self) -> Option<serde_json::Value> {
        self.ext_params.clone()
    }

    async fn run_task_with_request(
        self: Arc<Self>,
        request: DynRunTaskRequest,
    ) -> Result<TaskHandle, AgentError> {
        let request: RunTaskRequest<S, Config> = request.try_into()?;
        self.run_task_with_request(request).await
    }

    async fn resume_task(
        self: Arc<Self>,
        task_id: &str,
        user_action: UserAction,
    ) -> Result<TaskHandle, AgentError> {
        let checkpoint = self.check_pointer.get(task_id).await?;
        let mut task = Execution::restore(checkpoint);
        task.resume(user_action);
        self.run_execution_task(task, None, None).await
    }

    fn shutdown(self: Arc<Self>) {
        if !self.cancellation_token.is_cancelled() {
            self.cancellation_token.cancel();
        }
        for member in &self.members {
            member.clone().shutdown();
        }
    }
}
