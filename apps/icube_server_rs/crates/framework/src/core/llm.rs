use super::message::{Message, ToolCall};
use crate::llm::options::CallOptions;
use crate::llm::GenerateResult;
use crate::llm::{error::LLMError, TokenUsage};
use crate::tool::tool_box::ToolDefinitions;
use async_trait::async_trait;
use futures::Stream;
use serde::{Deserialize, Serialize};
use std::any::{type_name, Any};
use std::pin::Pin;

pub trait LLMGenerateResult {
    fn generation(&self) -> impl Into<String>;
    fn tool_calls(&self) -> Option<&[ToolCall]>;
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LLMStreamData {
    Content {
        content: String,
        tokens: Option<TokenUsage>,
    },
    ToolCall {
        tokens: Option<TokenUsage>,
        toolcall: ToolCall,
    },
    Finished {
        tokens: Option<TokenUsage>,
    },
    Queuing(LLMRequestWaitInQueueEvent),
    FeeUsage(LLMFeeUsageEvent),
}

impl LLMStreamData {
    pub fn content(&self) -> String {
        match self {
            Self::Content { content, .. } => content.clone(),
            Self::ToolCall {
                toolcall: ToolCall { function, .. },
                ..
            } => function.name.clone(),
            Self::Finished { .. } => "".to_string(),
            Self::Queuing(_) => "".to_string(),
            Self::FeeUsage(_) => "".to_string(),
        }
    }

    pub fn new_content(content: String) -> Self {
        Self::Content {
            content,
            tokens: None,
        }
    }

    pub fn new_tool_call(tool_call: ToolCall) -> Self {
        Self::ToolCall {
            tokens: None,
            toolcall: tool_call,
        }
    }

    pub fn new_finished() -> Self {
        Self::Finished { tokens: None }
    }

    pub fn with_tokens(&mut self, tokens: Option<TokenUsage>) {
        match self {
            Self::Content { tokens: t, .. } => *t = tokens,
            Self::ToolCall { tokens: t, .. } => *t = tokens,
            Self::Finished { tokens: t, .. } => *t = tokens,
            Self::Queuing(_) => (),
            Self::FeeUsage(_) => (),
        }
    }

    pub fn tokens_usage(&self) -> Option<&TokenUsage> {
        match self {
            Self::Content { tokens, .. } => tokens.as_ref(),
            Self::ToolCall { tokens, .. } => tokens.as_ref(),
            Self::Finished { tokens } => tokens.as_ref(),
            Self::Queuing(_) => None,
            Self::FeeUsage(_) => None,
        }
    }
}

pub type PinBoxLLMStream = Pin<Box<dyn Stream<Item = Result<LLMStreamData, LLMError>> + Send>>;

#[derive(Serialize, Deserialize, Clone, Default, Debug)]
pub enum LLMQueueStatus {
    #[default]
    #[serde(rename = "begin")]
    Begin,
    #[serde(rename = "end")]
    End,
}

#[derive(Serialize, Deserialize, Clone, Default, Debug)]
pub struct LLMRequestWaitInQueueEvent {
    pub queue_id: String,
    pub message: String,
    pub position: u32,
    pub contact_type: Option<u32>,
    /// 只有新的 Beta 模型通过 QueueBegin 和 QueueEnd 返回，统一处理成 RequestWaitInQueueEvent 返回给前端
    pub queue_type: Option<usize>,
    /// QueueStatus 是 end 时，表示队列结束，前端不展示排队提醒
    pub queue_status: Option<LLMQueueStatus>,
    pub is_async: Option<bool>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct LLMFeeUsageEvent {
    // 当前类型
    pub contact_type: usize,
    // 剩余使用量
    pub remaining_usage: Option<usize>,
    /// 新的剩余使用量，支持更高精度的浮点数字
    pub remaining_usage_str: Option<String>,
    // 升级费用
    pub upgrade_fee: Option<usize>,
    // 当前的使用权益类型 (1补全调用 2普通模型调用 3高级模型快队列 4高级模型慢队列 5 solo builder)
    pub usage_type: Option<usize>,
    #[serde(rename = "type")]
    pub _type: Option<String>,
    /// max 模式下的使用量
    pub already_usage: Option<String>,
}

#[derive(Serialize, Deserialize, Debug, Default, Clone)]
pub struct ContextTokenUsageEvent {
    pub used_token: usize,
    pub max_token: usize,
}

#[derive(Default, Clone)]
pub struct LLMRunConfig {
    pub tool_definitions: Option<ToolDefinitions>,
}

impl LLMRunConfig {
    pub fn new() -> Self {
        Default::default()
    }

    pub fn with_tool_definitions(mut self, tool_definitions: ToolDefinitions) -> Self {
        self.tool_definitions = Some(tool_definitions);
        self
    }
}

#[async_trait]
pub trait LLM: Sync + Send + 'static {
    type LLMConfig: Sync + Send;

    fn name(&self) -> &'static str;

    async fn generate(&self, messages: &[Message]) -> Result<GenerateResult, LLMError>;

    async fn generate_with_config(
        &self,
        _messages: &[Message],
        _config: &Self::LLMConfig,
    ) -> Result<GenerateResult, LLMError> {
        todo!()
    }

    async fn invoke(&self, prompt: &str) -> Result<String, LLMError> {
        self.generate(&[Message::new_human_message(prompt.to_string())])
            .await
            .map(|res| res.generation().into())
    }

    async fn stream(&self, _messages: &[Message]) -> Result<PinBoxLLMStream, LLMError>;

    async fn stream_with_config(
        &self,
        _messages: &[Message],
        _config: &Self::LLMConfig,
    ) -> Result<PinBoxLLMStream, LLMError> {
        todo!()
    }

    fn add_options(&mut self, _options: CallOptions) {}

    fn with_options(&mut self, _options: CallOptions) {}
}

#[async_trait]
pub trait DynLLM: Sync + Send + 'static {
    fn name(&self) -> &'static str;

    async fn generate(&self, messages: &[Message]) -> Result<GenerateResult, LLMError>;

    async fn generate_with_config(
        &self,
        messages: &[Message],
        config: &(dyn Any + Send + Sync),
    ) -> Result<GenerateResult, LLMError>;

    async fn invoke(&self, prompt: &str) -> Result<String, LLMError>;

    async fn stream(&self, messages: &[Message]) -> Result<PinBoxLLMStream, LLMError>;

    async fn stream_with_config(
        &self,
        messages: &[Message],
        config: &(dyn Any + Send + Sync),
    ) -> Result<PinBoxLLMStream, LLMError>;

    fn add_options(&mut self, _options: CallOptions) {}

    fn with_options(&mut self, _options: CallOptions) {}
}

pub struct DynLLMWrapper<T: LLM>(pub T);

impl<T: LLM> DynLLMWrapper<T> {
    fn cast_config<'a>(
        &self,
        config: &'a (dyn Any + Send + Sync),
    ) -> Result<&'a T::LLMConfig, LLMError> {
        config.downcast_ref::<T::LLMConfig>().ok_or_else(|| {
            LLMError::InvalidRunConfig(format!(
                "llm {} expect config {}",
                self.0.name(),
                type_name::<T::LLMConfig>()
            ))
        })
    }
}

#[async_trait]
impl<T: LLM> DynLLM for DynLLMWrapper<T> {
    fn name(&self) -> &'static str {
        self.0.name()
    }

    async fn generate(&self, messages: &[Message]) -> Result<GenerateResult, LLMError> {
        self.0.generate(messages).await
    }

    async fn generate_with_config(
        &self,
        messages: &[Message],
        config: &(dyn Any + Send + Sync),
    ) -> Result<GenerateResult, LLMError> {
        let config = self.cast_config(config)?;
        self.0.generate_with_config(messages, config).await
    }

    async fn invoke(&self, prompt: &str) -> Result<String, LLMError> {
        self.0.invoke(prompt).await
    }

    async fn stream(&self, messages: &[Message]) -> Result<PinBoxLLMStream, LLMError> {
        self.0.stream(messages).await
    }

    async fn stream_with_config(
        &self,
        messages: &[Message],
        config: &(dyn Any + Send + Sync),
    ) -> Result<PinBoxLLMStream, LLMError> {
        let config = self.cast_config(config)?;
        self.0.stream_with_config(messages, config).await
    }
}

impl<T: LLM> From<T> for DynLLMWrapper<T> {
    fn from(llm: T) -> Self {
        DynLLMWrapper(llm)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    /// Trivial model just for testing the wrapper.
    struct DummyLLM;

    #[derive(Debug, Clone, PartialEq)]
    struct MyLLMConfig {
        model_info: String,
    }

    #[async_trait]
    impl LLM for DummyLLM {
        type LLMConfig = MyLLMConfig;

        fn name(&self) -> &'static str {
            "dummy"
        }

        // We don't need full behaviour for these tests.
        async fn generate(&self, _messages: &[Message]) -> Result<GenerateResult, LLMError> {
            unimplemented!("not required for casting tests")
        }

        async fn stream(&self, _messages: &[Message]) -> Result<PinBoxLLMStream, LLMError> {
            unimplemented!("not required for casting tests")
        }
    }

    #[test]
    fn cast_config_success() {
        let wrapper = DynLLMWrapper(DummyLLM);
        let cfg = MyLLMConfig {
            model_info: "test_model".into(),
        };

        // SAFETY: &cfg is definitely a `dyn Any`.
        let got = wrapper
            .cast_config(&cfg as &(dyn Any + Send + Sync))
            .unwrap();
        assert_eq!(
            *got,
            MyLLMConfig {
                model_info: "test_model".into(),
            }
        );
    }

    #[test]
    fn cast_config_failure() {
        let wrapper = DynLLMWrapper(DummyLLM);
        let wrong_cfg = "hello"; // &str is the wrong type

        let err = wrapper
            .cast_config(&wrong_cfg as &(dyn Any + Send + Sync))
            .expect_err("expected InvalidRunConfig");

        match err {
            LLMError::InvalidRunConfig(_) => {}
            other => panic!("unexpected error variant: {:?}", other),
        }
    }
}
