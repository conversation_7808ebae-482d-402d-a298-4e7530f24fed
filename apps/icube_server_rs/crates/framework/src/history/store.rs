use crate::core::error::AgentError;
use crate::core::message::Message;
use crate::history::HistoryResult;
use async_trait::async_trait;
use tokio::sync::mpsc::UnboundedSender;
use crate::core::event::Event;

#[derive(Clone, serde::Serialize)]
pub struct HistoryMessage {
    pub session_id: String,
    pub message_id: String,
    pub agent_run_id: String,
    pub agent_type: String,
    pub content: Vec<Message>,
}

#[async_trait]
pub trait HistoryStore<C>: Send + Sync + 'static {
    async fn save(&self, req: &HistoryMessage, cfg: &C) -> HistoryResult<()>;
    async fn load(
        &self,
        session_id: &str,
        token_limit: Option<usize>,
        message_ids: Vec<String>,
        cfg: &C,
        event_bus: Option<&UnboundedSender<Event>>,
    ) -> HistoryResult<Vec<Message>>;

    async fn save_exception_message(
        &self,
        agent_error: &AgentError,
        cfg: &C,
    ) -> HistoryResult<()>;

    async fn compress(
        &self,
        session_id: &str,
        cfg: &C,
    ) -> HistoryResult<bool>;

    async fn update_compress_token_usage(
        &self,
        session_id: &str,
        total_token: u32,
        cfg: &C,
    ) -> HistoryResult<()>;
}
