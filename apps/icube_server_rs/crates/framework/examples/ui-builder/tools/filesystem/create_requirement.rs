use async_trait::async_trait;
use framework::core::artifact::{Artifact, Part};
use framework::core::error::{ResultExt, ToolError};
use framework::core::tool::{Tool, ToolCallingContext, ToolResult};
use futures::Stream;
use futures::StreamExt;
use schemars::JsonSchema;
use serde::{Deserialize, Serialize};
use std::borrow::Cow;
use std::path::Path;
use std::pin::Pin;
use tokio::io::AsyncWriteExt;

pub struct CreateRequirement;
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
#[serde(transparent)]
pub struct RequirementContent(String);

#[derive(Debug, Clone, PartialEq, Deserialize, JsonSchema, Default)]
pub struct CreateRequirementParam {
    #[serde(rename = "filePath")]
    pub file_path: String,

    pub content: String,
}

#[async_trait]
impl Tool for CreateRequirement {
    const NAME: &'static str = "create_requirement";
    type Params = CreateRequirementParam;
    type Result = RequirementContent;

    fn description(&self) -> Cow<str> {
        "create requirement".into()
    }

    fn to_artifact(
        &self,
        // 可能需要拿 config
        _ctx: ToolCallingContext,
        param: Option<&Self::Params>,
        result: &Self::Result,
    ) -> Option<Artifact> {
        Some(
            Artifact::new("requirement", "requirement of user")
                .add_part(Part::text(result.0.clone())),
        )
    }

    // 流式调用
    async fn call_stream(
        &self,
        ctx: ToolCallingContext,
        mut stream: Pin<Box<dyn Stream<Item = Self::Params> + Send>>,
    ) -> ToolResult<()> {
        while let Some(e) = stream.next().await {
            tokio::fs::create_dir_all(
                Path::new(&e.file_path)
                    .parent()
                    .expect("parent path must exist"),
            )
            .await
            .with_tool_context(self.name())?;

            let mut new_file = tokio::fs::File::create(e.file_path)
                .await
                .with_tool_context(self.name())?;

            new_file
                .write_all(e.content.as_bytes())
                .await
                .with_tool_context(self.name())?;
            //. ....
        }
        Ok(())
    }

    async fn call(
        &self,
        ctx: ToolCallingContext,
        param: Option<Self::Params>,
    ) -> ToolResult<Self::Result> {
        let Some(param) = param else {
            return Err(ToolError::ValidateError("invalid param".to_string()));
        };
        tokio::fs::create_dir_all(
            Path::new(&param.file_path)
                .parent()
                .expect("parent path must exist"),
        )
        .await
        .with_tool_context(self.name())?;
        let mut new_file = tokio::fs::File::create(param.file_path)
            .await
            .with_tool_context(self.name())?;

        new_file
            .write_all(param.content.as_bytes())
            .await
            .with_tool_context(self.name())?;

        Ok(RequirementContent(param.content))
    }
}
