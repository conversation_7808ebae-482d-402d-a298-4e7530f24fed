use crate::core::message::<PERSON><PERSON><PERSON><PERSON>;
use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serial<PERSON>, Deserialize)]
pub struct Interrupt {
    pub task_id: String,
    pub caused_task_id: Option<String>,
    pub reason: InterruptReason,
    pub trigger_tool_calls: Vec<ToolCall>,
    pub answer: Option<UserAction>,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialize, Deserialize)]
pub struct UserAction {
    pub decision: Decision,
    pub message: Option<String>,
    pub payload: Option<serde_json::Value>,
}

impl Interrupt {
    pub fn new(
        task_id: impl Into<String>,
        reason: InterruptReason,
        trigger_tool_calls: Vec<ToolCall>,
    ) -> Self {
        Self {
            task_id: task_id.into(),
            caused_task_id: None,
            reason,
            trigger_tool_calls,
            answer: None,
        }
    }

    pub fn caused_by(mut self, task_id: impl Into<String>) -> Self {
        self.caused_task_id = Some(task_id.into());
        self
    }

    pub fn get_answer(&self) -> UserAction {
        self.answer.clone().unwrap_or_default()
    }
}

#[derive(Default, Debug, <PERSON><PERSON>, Serial<PERSON>, Deserialize, PartialEq)]
pub enum Decision {
    Approve,
    #[default]
    Reject,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct Question {
    pub task_id: String, // the task_id for sub agent
    pub agent_name: String,
    pub tool_call: ToolCall,
    pub message: Option<String>,
    pub payload: Option<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum InterruptReason {
    AskUser(Question),
    FinishWork,
    // TODO: 增加新的类型
    ToolConfirm {
        id: String,
        message_id: String,
        task_id: String, // the task_id for sub agent
        agent_name: String,
        tool_call: ToolCall,
    },
}
