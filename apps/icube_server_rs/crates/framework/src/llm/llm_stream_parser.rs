use std::collections::HashMap;
use std::future::Future;
use std::pin::Pin;
use tokio::sync::mpsc;
use tokio_stream::{wrappers::UnboundedReceiverStream, StreamExt};
use tracing::Instrument;

use crate::{
    core::{
        error::AgentError,
        llm::{LLMStreamData, PinBoxLLMStream},
        message::ToolCall,
    },
    llm::GenerateResult,
    tool::tool::{PinBoxToolStreamParam, ToolCallStreamFrame},
};

use super::error::LLMError;

pub struct LLMStreamParseTaskHandle {
    result_task: tokio::task::Jo<PERSON><PERSON><PERSON><PERSON><Result<GenerateResult, AgentError>>,
}

impl LLMStreamParseTaskHandle {
    /// 等待所有任务完成并返回结果
    pub async fn await_completion(self) -> Result<GenerateResult, AgentError> {
        // 等待结果提取完成
        let result = self
            .result_task
            .await
            .map_err(|e| AgentError::InternalError(e.to_string()))??;

        Ok(result)
    }
}

pub type ToolStreamHandler = Box<
    dyn Fn(
            ToolCall,
            PinBoxToolStreamParam,
        ) -> Pin<Box<dyn Future<Output = Result<(), AgentError>> + Send>>
        + Send,
>;

pub trait IntoToolStreamHandler {
    fn into_handler(self) -> ToolStreamHandler;
}

impl<F, Fut> IntoToolStreamHandler for F
where
    F: Fn(ToolCall, PinBoxToolStreamParam) -> Fut + Send + 'static,
    Fut: Future<Output = Result<(), AgentError>> + Send + 'static,
{
    fn into_handler(self) -> ToolStreamHandler {
        Box::new(move |tool_call, stream| Box::pin(self(tool_call, stream)))
    }
}

pub type ToolLLMStreamDataHandler = Box<
    dyn Fn(
            Result<LLMStreamData, LLMError>,
        ) -> Pin<Box<dyn Future<Output = Result<(), AgentError>> + Send>>
        + Send,
>;

pub trait IntoToolLLMStreamDataHandler {
    fn into_handler(self) -> ToolLLMStreamDataHandler;
}

impl<F, Fut> IntoToolLLMStreamDataHandler for F
where
    F: Fn(Result<LLMStreamData, LLMError>) -> Fut + Send + 'static,
    Fut: Future<Output = Result<(), AgentError>> + Send + 'static,
{
    fn into_handler(self) -> ToolLLMStreamDataHandler {
        Box::new(move |data| Box::pin(self(data)))
    }
}

/// 用于解析 LLM 流数据的解析器
pub struct LLMStreamParser {
    tool_handler: Option<ToolStreamHandler>,

    stream_handler: Option<ToolLLMStreamDataHandler>,
}

impl Default for LLMStreamParser {
    fn default() -> Self {
        Self::new()
    }
}

impl LLMStreamParser {
    pub fn new() -> Self {
        Self {
            tool_handler: None,
            stream_handler: None,
        }
    }
    pub fn with_toolcall_handler<H>(mut self, tool_handler: H) -> Self
    where
        H: IntoToolStreamHandler,
    {
        self.tool_handler = Some(tool_handler.into_handler());
        self
    }
    pub fn with_llm_frame_handler<H>(mut self, stream_handler: H) -> Self
    where
        H: IntoToolLLMStreamDataHandler,
    {
        self.stream_handler = Some(stream_handler.into_handler());
        self
    }

    /// 启动流处理，返回处理任务
    pub fn process_stream(self, mut stream: PinBoxLLMStream) -> LLMStreamParseTaskHandle {
        let tool_handler = self.tool_handler;
        let stream_handler = self.stream_handler;

        // 创建一个统一的串行处理任务
        let unified_task = tokio::spawn(
            async move {
                let mut result = GenerateResult::default();
                let mut tool_senders: HashMap<String, mpsc::UnboundedSender<ToolCallStreamFrame>> =
                    HashMap::new();
                let mut spawned_tool_tasks = Vec::new();

                // 串行处理流数据
                while let Some(data_result) = stream.next().await {
                    match data_result {
                        Ok(llm_data) => {
                            // 首先执行 stream_handler
                            if let Some(stream_handler) = &stream_handler {
                                stream_handler(Ok(llm_data.clone())).await?;
                            }

                            // 然后处理数据并更新结果
                            match &llm_data {
                                LLMStreamData::Content { content, .. } => {
                                    result.generation = content.clone();
                                }
                                LLMStreamData::ToolCall { toolcall, .. } => {
                                    let t =
                                        result.tool_calls.get_or_insert_with(std::vec::Vec::new);
                                    match t.get_mut(toolcall.index) {
                                        Some(item) => *item = toolcall.clone(),
                                        None => t.push(toolcall.clone()),
                                    }

                                    // 处理工具调用
                                    if let Some(tool_handler) = &tool_handler {
                                        // 如果这是第一次遇到这个工具，立即创建对应的流和任务
                                        if let std::collections::hash_map::Entry::Vacant(e) =
                                            tool_senders.entry(toolcall.id.clone())
                                        {
                                            let (sender, receiver) = mpsc::unbounded_channel();
                                            e.insert(sender);

                                            // 立即创建工具流并启动任务
                                            let tool_stream: PinBoxToolStreamParam =
                                                Box::pin(UnboundedReceiverStream::new(receiver));
                                            let task = tokio::spawn(
                                                (tool_handler)(toolcall.clone(), tool_stream)
                                                    .in_current_span(),
                                            );
                                            spawned_tool_tasks.push(task);
                                        }

                                        // 立即发送帧到对应的工具流
                                        if let Some(sender) = tool_senders.get(&toolcall.id) {
                                            let _ =
                                                sender.send(ToolCallStreamFrame(toolcall.clone()));
                                        }
                                    }
                                }
                               LLMStreamData::Finished { tokens } => {
                                    result.tokens = tokens.clone();
                                    tracing::trace!("llm stream data finished");
                                    break;
                                }
                                _ => {}
                            }
                        }
                        Err(e) => {
                            // 执行 stream_handler 处理错误
                            if let Some(stream_handler) = &stream_handler {
                                stream_handler(Err(e.clone())).await?;
                            }
                            result.error = Some(e);
                            break;
                        }
                    }
                }

                // 关闭所有发送器
                drop(tool_senders);

                // 等待所有工具任务完成
                for tool_task in spawned_tool_tasks {
                    tool_task
                        .await
                        .map_err(|e| AgentError::InternalError(e.to_string()))??;
                }

                Ok(result)
            }
            .in_current_span(),
        );

        LLMStreamParseTaskHandle {
            result_task: unified_task,
        }
    }
}
