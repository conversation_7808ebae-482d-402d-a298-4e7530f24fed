use super::builtin_handlers::end::FinishHandlerFunc;
use super::builtin_handlers::error::ErrorHandlerFunc;
use super::edge::GraphEdge;
use super::error::GraphError;
use super::handler::ErasedHandler;
use super::node::{<PERSON>raphNode, NodeHandle, NodeId, NodeRef};
use crate::core::error::AgentError;
use crate::core::handler::StageHandler;
use crate::graph::builtin_handlers::interrupt::AsInterrupt;
use crate::graph::builtin_action::interrupt::Interrupt;
use crate::graph::InterruptHandler;
use serde_json::Value;
use std::borrow::Cow;
use std::{any::Any, collections::HashMap, future::Future};
use petgraph::prelude::StableDiGraph;

pub type AnyType = Box<dyn Any + Send + Sync + 'static>;

pub struct StateGraph<GlobalState, Context: Sync + Send + 'static> {
    pub(super) name: Cow<'static, str>,
    pub(super) graph: StableDiGraph<GraphNode<GlobalState, Context>, GraphEdge>,
    pub(super) error_handler: Option<ErrorHandlerFunc<GlobalState, Context>>,
    pub(super) finish_handler: Option<FinishHandlerFunc<GlobalState, Context>>,
    pub(super) name_2_index_mapping: HashMap<Cow<'static, str>, NodeId>,
}

impl<GS, C> Default for StateGraph<GS, C>
where
    GS: Send + Sync + 'static,
    C: Send + Sync + 'static,
{
    fn default() -> Self {
        Self::new()
    }
}

pub trait IntoGraphFinishedHandler<GS, C> {
    fn into_handler(self) -> FinishHandlerFunc<GS, C>;
}

impl<GS, C, F, Fut> IntoGraphFinishedHandler<GS, C> for F
where
    F: Fn(C, &mut GS, Value) -> Fut + Send + Sync + 'static,
    Fut: Future<Output = Result<(), AgentError>> + Send + 'static,
{
    fn into_handler(self) -> FinishHandlerFunc<GS, C> {
        Box::new(move |ctx, state, value| Box::pin(self(ctx, state, value)))
    }
}

pub trait IntoGraphErrorHandler<GS, C> {
    fn into_handler(self) -> ErrorHandlerFunc<GS, C>;
}

impl<GS, C, F, Fut> IntoGraphErrorHandler<GS, C> for F
where
    F: Fn(C, &mut GS, AgentError) -> Fut + Send + Sync + 'static,
    Fut: Future<Output = Result<(), AgentError>> + Send + 'static,
{
    fn into_handler(self) -> ErrorHandlerFunc<GS, C> {
        Box::new(move |ctx, state, error| Box::pin(self(ctx, state, error)))
    }
}

impl<GS, C> StateGraph<GS, C>
where
    GS: Send + Sync + 'static,
    C: Send + Sync + 'static,
{
    pub fn new() -> Self {
        StateGraph::<GS, C> {
            name: Cow::Owned("default".into()),
            graph: StableDiGraph::new(),
            error_handler: None,
            finish_handler: None,
            name_2_index_mapping: HashMap::new(),
        }
    }

    pub fn with_name(&mut self, name: impl Into<Cow<'static, str>>) {
        self.name = name.into();
    }

    pub(super) fn inner_add_node(&mut self, node: GraphNode<GS, C>) -> Result<NodeRef, GraphError> {
        let name = node.name();
        if self.name_2_index_mapping.contains_key(&name) {
            return Err(GraphError::DuplicateNodeName(name.to_string()));
        }
        let input_type = node.input_type();
        let output_type = node.output_type();
        let idx = self.graph.add_node(node);
        self.name_2_index_mapping.insert(name, idx.into());

        Ok(NodeRef {
            index: idx.into(),
            input_type,
            output_type,
        })
    }

    pub fn add_interrupt_node<H>(
        &mut self,
        handler: H,
    ) -> Result<NodeHandle<Interrupt, H::Outgoing>, GraphError>
    where
        H: InterruptHandler<GS, C> + 'static,
    {
        self.insert_node(AsInterrupt(handler), GraphNode::Interrupt)
    }

    pub fn add_node<H>(
        &mut self,
        handler: H,
    ) -> Result<NodeHandle<H::Incoming, H::Outgoing>, GraphError>
    where
        H: StageHandler<GS, C> + 'static,
    {
        let node_ref = self.try_add_node(handler)?;
        Ok(NodeHandle::new(node_ref))
    }

    pub fn try_add_node<H>(&mut self, handler: H) -> Result<NodeRef, GraphError>
    where
        H: StageHandler<GS, C> + 'static,
    {
        let erased = ErasedHandler::new(handler);
        let node_ref = self.inner_add_node(GraphNode::Handler(erased))?;
        Ok(node_ref)
    }

    fn insert_node<H, F>(
        &mut self,
        handler: H,
        wrap: F,
    ) -> Result<NodeHandle<H::Incoming, H::Outgoing>, GraphError>
    where
        H: StageHandler<GS, C> + 'static,
        F: FnOnce(ErasedHandler<GS, C>) -> GraphNode<GS, C>,
    {
        let erased = ErasedHandler::new(handler);
        let node_ref = self.inner_add_node(wrap(erased))?;
        Ok(NodeHandle::new(node_ref))
    }

    pub fn connect<I1, O1, I2, O2>(
        &mut self,
        src: &NodeHandle<I1, O1>,
        dst: &NodeHandle<I2, O2>,
    ) -> Result<(), GraphError>
    where
        O1: Into<I2>,
    {
        self.try_connect(src.get_ref(), dst.get_ref())
    }

    pub fn try_connect(&mut self, src: NodeRef, dst: NodeRef) -> Result<(), GraphError> {
        let src_out = src.output_type;
        let dst_in = dst.input_type;
        if src_out != dst_in {
            let src_name = self.graph[*src.index].name();
            let dst_name = self.graph[*dst.index].name();
            return Err(GraphError::TypeMismatch(format!(
                "source({:?}) output type {:?} does not match destination({:?}) input type {:?}",
                src_name, src_out, dst_name, dst_in
            )));
        }
        self.graph.add_edge(
            src.index.into(),
            dst.index.into(),
            GraphEdge::Direct(src.output_type, dst.input_type),
        );
        Ok(())
    }
    pub fn on_error<H>(&mut self, func: H) -> &mut Self
    where
        H: IntoGraphErrorHandler<GS, C>,
    {
        self.error_handler = Some(func.into_handler());
        self
    }

    pub fn on_finished<H>(&mut self, func: H) -> &mut Self
    where
        H: IntoGraphFinishedHandler<GS, C>,
    {
        self.finish_handler = Some(func.into_handler());
        self
    }
}
