#[cfg(test)]
mod tests {
    use crate::agent::builder::GraphBuilder;
    use crate::agent::context::CallingContext;
    use crate::agent::execution::RunTaskRequest;
    use crate::agent::react_agent::ReActAgent;
    use crate::agent::state::RunState;
    use crate::core::agent::Agent;
    use crate::core::artifact::Artifact;
    use crate::core::checkpoint::{CheckPoint, CheckPointError, CheckPointer};
    use crate::core::error::AgentError;
    use crate::core::event::{Event, ProcessedOutput};
    use crate::core::llm::{LLMGenerateResult, LLMRunConfig, LLMStreamData, LLM};
    use crate::core::message::{Message, ToolCall};
    use crate::core::prompt::RenderResult;
    use crate::core::provider::model::{
        LL<PERSON><PERSON>ider, LLMProviderError, LanguageModel, ModelProvider,
    };

    use crate::graph::{CompiledGraph, GraphError, StateGraph};
    use crate::llm::error::LLMError;
    use crate::llm::GenerateResult;
    use crate::prompt::{PromptArgs, PromptError, PromptFormatter, PromptProvider};
    use crate::schemas::StreamData;
    use async_trait::async_trait;
    use futures::Stream;
    use futures::StreamExt;
    use serde::{Deserialize, Serialize};
    use serde_json::Value::Null;
    use std::borrow::Cow;
    use std::fmt::Debug;
    use std::pin::Pin;
    use std::sync::Arc;
    use std::time::Duration;
    use tokio::sync::mpsc::unbounded_channel;
    use tokio_stream::wrappers::UnboundedReceiverStream;
    use tokio_util::sync::CancellationToken;

    // Mock LLM implementation
    #[derive(Clone)]
    struct MockLLM {
        response: StreamData,
    }

    impl MockLLM {
        fn new(response: impl Into<String>) -> Self {
            Self {
                response: StreamData::new(Null, None, response),
            }
        }
    }
    #[derive(Serialize, Deserialize, Clone, Debug, Default)]
    struct TestingConfig {
        foo: String,
    }
    pub type State = RunState<()>;
    pub type Context = CallingContext<Config>;
    pub type Config = TestingConfig;

    struct MockCheckPointer;

    #[async_trait]
    impl<T> CheckPointer<T, Config> for MockCheckPointer
    where
        T: Debug + Default + Serialize + Clone,
    {
        async fn get(&self, task_id: &str) -> Result<CheckPoint<T, Config>, CheckPointError> {
            Ok(CheckPoint::<T, Config>::default())
        }

        async fn save(&self, checkpoint: &CheckPoint<T, Config>) -> Result<(), CheckPointError> {
            Ok(())
        }
    }

    struct MockLLMResult {
        text: StreamData,
    }

    impl LLMGenerateResult for MockLLMResult {
        fn generation(&self) -> impl Into<String> {
            self.text.content.clone()
        }

        fn tool_calls(&self) -> Option<&[ToolCall]> {
            None
        }
    }

    struct MockLLMProvider;

    impl ModelProvider for MockLLMProvider {
        fn get_llm_model(&self, provider: &LLMProvider) -> Result<LanguageModel, LLMProviderError> {
            todo!()
        }
    }

    #[async_trait]
    impl LLM for MockLLM {
        type LLMConfig = LLMRunConfig;
        fn name(&self) -> &'static str {
            "mockllm"
        }

        async fn generate(&self, _messages: &[Message]) -> Result<GenerateResult, LLMError> {
            Ok(GenerateResult {
                generation: self.response.content.clone(),
                ..Default::default()
            })
        }

        async fn stream(
            &self,
            _messages: &[Message],
        ) -> Result<Pin<Box<dyn Stream<Item = Result<LLMStreamData, LLMError>> + Send>>, LLMError>
        {
            let (tx, rx) = unbounded_channel();
            let _ = tx.send(Ok(LLMStreamData::new_content(
                self.response.content.clone(),
            )));
            Ok(Box::pin(UnboundedReceiverStream::new(rx)))
        }
    }

    struct TestPromptProvider;
    struct TestPromptFormatter;

    #[async_trait]
    impl PromptFormatter for TestPromptFormatter {
        async fn format(
            &self,
            _input_variables: PromptArgs,
        ) -> Result<RenderResult, PromptError> {
            Ok(RenderResult {
                system_prompt: "Test template".to_string(),
                user_input: None,
                final_input: None,
                ..Default::default()
            })
        }

        fn as_any(&self) -> &dyn std::any::Any {
            self
        }
    }

    #[async_trait]
    impl PromptProvider for TestPromptProvider {
        async fn get_default_prompt_template(
            &self,
        ) -> Result<Arc<dyn PromptFormatter>, PromptError> {
            todo!()
        }

        async fn get_prompt_template(
            &self,
            _name: &str,
        ) -> Result<Arc<dyn PromptFormatter>, PromptError> {
            Ok(Arc::new(TestPromptFormatter))
        }

        fn as_any(&self) -> &dyn std::any::Any {
            self
        }
    }

    // Mock test state
    #[derive(Debug, Serialize, Deserialize, Clone, Default)]
    struct TestState {
        counter: i32,
    }

    // Mock GraphBuilder
    struct MockGraphBuilder;

    impl GraphBuilder<RunState<TestState>, Config> for MockGraphBuilder {
        fn build_graph(
            self,
            name: Option<Cow<'static, str>>,
        ) -> Result<CompiledGraph<RunState<TestState>, CallingContext<Config>>, GraphError>
        {
            // Create a simple graph with start and end nodes
            let mut graph = StateGraph::new();
            let start = graph.add_node(MockHandler)?;
            let end = graph.add_node(MockEndHandler)?;
            graph.connect(&start, &end)?;
            let compiled = graph.compile()?;
            Ok(compiled)
        }
    }

    // Mock handler for testing
    struct MockHandler;

    #[async_trait]
    impl crate::core::handler::StageHandler<RunState<TestState>, Context> for MockHandler {
        type Incoming = ();
        type Outgoing = String;

        async fn handle(
            &self,
            _ctx: Context,
            state: &mut RunState<TestState>,
            _input: Self::Incoming,
        ) -> Result<Self::Outgoing, AgentError> {
            state.state.counter += 1;
            Ok("test output".to_string())
        }

        fn name(&self) -> Cow<'static, str> {
            "MockHandler".into()
        }
    }

    // Mock end handler for testing
    struct MockEndHandler;

    #[async_trait]
    impl crate::core::handler::StageHandler<RunState<TestState>, CallingContext<Config>>
        for MockEndHandler
    {
        type Incoming = String;
        type Outgoing = ();

        async fn handle(
            &self,
            _ctx: CallingContext<Config>,
            _state: &mut RunState<TestState>,
            _input: Self::Incoming,
        ) -> Result<Self::Outgoing, AgentError> {
            Ok(())
        }

        fn name(&self) -> Cow<'static, str> {
            "MockEndHandler".into()
        }
    }

    #[tokio::test]
    async fn test_react_agent_basic_properties() {
        let llm = MockLLM::new("test response");
        let agent = ReActAgent::builder()
            .with_name("TestAgent")
            .with_description("Test agent description")
            .with_llm(llm)
            .with_llm_provider(MockLLMProvider)
            .with_prompt_provider(TestPromptProvider)
            .with_handle_factory(MockGraphBuilder)
            .with_check_pointer(MockCheckPointer)
            .build()
            .expect("Failed to build agent");

        // Test basic properties
        assert_eq!(agent.name(), "TestAgent");
        assert_eq!(agent.description(), "Test agent description");
        assert!(agent.graph.is_some());
        assert!(agent.toolbox.is_none());
        assert!(agent.members.is_empty());
    }

    #[tokio::test]
    async fn test_react_agent_shutdown() {
        let llm = MockLLM::new("test response");
        let agent = ReActAgent::builder()
            .with_name("TestAgent")
            .with_description("Test agent description")
            .with_llm(llm)
            .with_llm_provider(MockLLMProvider)
            .with_prompt_provider(TestPromptProvider)
            .with_handle_factory(MockGraphBuilder)
            .with_check_pointer(MockCheckPointer)
            .build()
            .expect("Failed to build agent");

        assert!(!agent.cancellation_token.is_cancelled());
        agent.shutdown();
        // assert!(agent.cancellation_token.is_cancelled());
    }

    #[tokio::test]
    async fn test_run_task_with_new_query() {
        let llm = MockLLM::new("test response");
        let agent = ReActAgent::builder()
            .with_name("TestAgent")
            .with_description("Test agent description")
            .with_llm(llm)
            .with_llm_provider(MockLLMProvider)
            .with_prompt_provider(TestPromptProvider)
            .with_handle_factory(MockGraphBuilder)
            .with_check_pointer(MockCheckPointer)
            .build()
            .expect("Failed to build agent");

        let query = "test query".to_string();

        let task_handle = agent.run_task(query).await.unwrap();
        assert!(!task_handle.ctrl.id().is_empty());
    }

    #[tokio::test]
    async fn test_run_task_with_unhandled_event() {
        let llm = MockLLM::new("test response");
        let agent = ReActAgent::builder()
            .with_name("TestAgent")
            .with_description("Test agent description")
            .with_llm(llm)
            .with_llm_provider(MockLLMProvider)
            .with_prompt_provider(TestPromptProvider)
            .with_handle_factory(MockGraphBuilder)
            .with_check_pointer(MockCheckPointer)
            .build()
            .expect("Failed to build agent");

        let question = "test question".to_string();

        let task_handle = agent.run_task(question).await.unwrap();
        assert!(!task_handle.ctrl.id().is_empty());
    }

    #[tokio::test]
    async fn test_run_task_with_request() {
        let llm = MockLLM::new("test response");
        let agent = ReActAgent::builder()
            .with_name("TestAgent")
            .with_description("Test agent description")
            .with_llm(llm)
            .with_llm_provider(MockLLMProvider)
            .with_prompt_provider(TestPromptProvider)
            .with_handle_factory(MockGraphBuilder)
            .with_check_pointer(MockCheckPointer)
            .build()
            .expect("Failed to build agent");

        let question = "test question".to_string();

        let task_handle = agent
            .run_task(
                RunTaskRequest::new(question)
                    .with_initial_state(TestState { counter: 2 })
                    .with_config(TestingConfig {
                        foo: "bar".to_string(),
                    }),
            )
            .await
            .unwrap();
        assert!(!task_handle.ctrl.id().is_empty());
    }

    #[tokio::test]
    async fn test_run_task_cancellation() {
        let llm = MockLLM::new("test response");
        let agent = ReActAgent::builder()
            .with_name("TestAgent")
            .with_description("Test agent description")
            .with_llm(llm)
            .with_llm_provider(MockLLMProvider)
            .with_prompt_provider(TestPromptProvider)
            .with_handle_factory(MockGraphBuilder)
            .with_check_pointer(MockCheckPointer)
            .build()
            .expect("Failed to build agent");

        let question = "test question".to_string();

        // Cancel the token before running the task
        agent.cancellation_token.cancel();

        let task_handle = agent.run_task(question).await.unwrap();
        assert!(!task_handle.ctrl.id().is_empty());

        // Verify the cancellation token is cancelled
        assert!(task_handle.ctrl.token.is_cancelled());
    }

    #[tokio::test]
    async fn test_run_task_with_stream() {
        let llm = MockLLM::new("test response");
        let agent = ReActAgent::builder()
            .with_name("TestAgent")
            .with_description("Test agent description")
            .with_llm(llm)
            .with_llm_provider(MockLLMProvider)
            .with_prompt_provider(TestPromptProvider)
            .with_handle_factory(MockGraphBuilder)
            .with_check_pointer(MockCheckPointer)
            .build()
            .expect("Failed to build agent");

        let question = "test question".to_string();
        let mut task = agent.run_task(question).await.unwrap();
        assert!(!task.ctrl.id().is_empty());

        let ctrl = task.control().clone();
        // Test streaming events
        let mut stream = task.stream();
        let mut event_count = 0;
        let mut received_events = Vec::new();

        while let Some(event) = stream.next().await {
            received_events.push(event);
            event_count += 1;
            // Break after receiving some events to avoid infinite loop
            if event_count >= 3 {
                break;
            }
        }

        // Verify we received at least one event
        assert!(
            event_count > 0,
            "Should receive at least one event from stream"
        );
        assert_eq!(
            received_events.len(),
            event_count,
            "Event count should match received events"
        );

        // Verify task control is still accessible after streaming
        let task_id = ctrl.id();
        assert!(
            !task_id.is_empty(),
            "Task should have a valid ID after streaming"
        );
        assert!(
            !ctrl.token.is_cancelled(),
            "Task should not be cancelled initially"
        );
    }

    #[tokio::test]
    async fn test_run_task_stream_with_timeout() {
        let llm = MockLLM::new("test response");
        let agent = ReActAgent::builder()
            .with_name("TestAgent")
            .with_description("Test agent description")
            .with_llm(llm)
            .with_llm_provider(MockLLMProvider)
            .with_prompt_provider(TestPromptProvider)
            .with_handle_factory(MockGraphBuilder)
            .with_check_pointer(MockCheckPointer)
            .build()
            .expect("Failed to build agent");

        let query = "test query".to_string();

        let mut task = agent.run_task(query).await.unwrap();
        let mut stream = task.stream();

        // Test with timeout to ensure stream doesn't hang indefinitely
        let timeout_result = tokio::time::timeout(Duration::from_millis(100), stream.next()).await;

        // Either we get an event or timeout, both are acceptable
        let mut is_received = false;
        let mut is_error = false;
        match timeout_result {
            Ok(Some(_event)) => {
                is_received = true;
            }
            Ok(None) => {}
            Err(_) => {
                is_error = false;
            }
        }
        assert!(is_received);
        assert!(!is_error);
    }

    #[tokio::test]
    async fn test_agent_output_mapping() {
        let llm = MockLLM::new("test response");
        let agent = ReActAgent::builder()
            .with_name("TestAgent")
            .with_description("Test agent description")
            .with_llm(llm)
            .with_llm_provider(MockLLMProvider)
            .with_prompt_provider(TestPromptProvider)
            .with_handle_factory(MockGraphBuilder)
            .with_check_pointer(MockCheckPointer)
            .with_output_mapping(
                |output: serde_json::Value, run_state: RunState<TestState>| {
                    assert!(serde_json::from_value::<()>(output).is_ok());
                    async move {
                        ProcessedOutput {
                            artifacts: run_state.artifacts().clone(),
                            summary: "".to_string(),
                            payload: Null,
                            history: vec![],
                            ..Default::default()
                        }
                    }
                },
            )
            .build()
            .expect("Failed to build agent");

        let query = "test query".to_string();

        let mut task = agent.run_task(query).await.unwrap();
        let mut stream = task.stream();

        let mut f_output = None;
        while let Some(event) = stream.next().await {
            if let Event::ProcessingComplete { output } = event {
                f_output = Some(output);
            }
        }
        assert_eq!(f_output.unwrap().payload, Null);
    }

    #[tokio::test]
    async fn test_agent_initial_turns() {
        let llm = MockLLM::new("test response");
        let agent = ReActAgent::builder()
            .with_name("TestAgent")
            .with_description("Test agent description")
            .with_llm(llm)
            .with_llm_provider(MockLLMProvider)
            .with_prompt_provider(TestPromptProvider)
            .with_handle_factory(MockGraphBuilder)
            .with_check_pointer(MockCheckPointer)
            .build()
            .expect("Failed to build agent");

        let query = "test query".to_string();

        let mut task = agent
            .run_task(RunTaskRequest::new(query).with_initial_turns(3))
            .await
            .unwrap();
        let mut stream = task.stream();

        let mut f_output = None;
        while let Some(event) = stream.next().await {
            if let Event::ProcessingComplete { output } = event {
                f_output = Some(output);
            }
        }
        assert_eq!(f_output.unwrap().turns, 3);
    }

    #[tokio::test]
    async fn test_artifact() {
        struct MockSetArtifact;

        #[async_trait]
        impl crate::core::handler::StageHandler<RunState<TestState>, CallingContext<Config>>
            for MockSetArtifact
        {
            type Incoming = String;
            type Outgoing = String;

            async fn handle(
                &self,
                _ctx: CallingContext<Config>,
                state: &mut RunState<TestState>,
                _input: Self::Incoming,
            ) -> Result<Self::Outgoing, AgentError> {
                state.add_artifact(Artifact::new("test_artifact", "test description"));
                Ok(_input)
            }

            fn name(&self) -> Cow<'static, str> {
                "MockSetArtifact".into()
            }
        }

        struct MockGraphBuilder;

        impl GraphBuilder<RunState<TestState>, Config> for MockGraphBuilder {
            fn build_graph(
                self,
                name: Option<Cow<'static, str>>,
            ) -> Result<CompiledGraph<RunState<TestState>, CallingContext<Config>>, GraphError>
            {
                // Create a simple graph with start and end nodes
                let mut graph = StateGraph::new();
                let start = graph.add_node(MockHandler)?;
                let end = graph.add_node(MockEndHandler)?;
                let artifact = graph.add_node(MockSetArtifact)?;
                graph.connect(&start, &artifact)?;
                graph.connect(&artifact, &end)?;
                let compiled = graph.compile()?;
                Ok(compiled)
            }
        }
        let llm = MockLLM::new("test response");
        let agent = ReActAgent::builder()
            .with_name("TestAgent")
            .with_description("Test agent description")
            .with_llm(llm)
            .with_llm_provider(MockLLMProvider)
            .with_prompt_provider(TestPromptProvider)
            .with_handle_factory(MockGraphBuilder)
            .with_check_pointer(MockCheckPointer)
            .build()
            .expect("Failed to build agent");

        let query = "test query".to_string();

        let mut task = agent.run_task(query).await.unwrap();
        let mut stream = task.stream();

        let mut f_output = None;
        while let Some(event) = stream.next().await {
            if let Event::ProcessingComplete { output } = event {
                f_output = Some(output);
            }
        }
        assert_eq!(
            f_output.unwrap().artifacts.last().unwrap().name,
            "test_artifact"
        );
    }

    #[tokio::test]
    async fn test_agent_run_task_with_external_cancellation_token() {
        let llm = MockLLM::new("test response");
        let agent = ReActAgent::builder()
            .with_name("TestAgent")
            .with_description("Test agent description")
            .with_llm(llm)
            .with_llm_provider(MockLLMProvider)
            .with_prompt_provider(TestPromptProvider)
            .with_handle_factory(MockGraphBuilder)
            .with_check_pointer(MockCheckPointer)
            .build()
            .expect("Failed to build agent");

        let external_token = CancellationToken::new();
        let question = "test question".to_string();

        // 使用外部取消令牌运行任务
        let task_handle = agent
            .run_task(RunTaskRequest::new(question).with_cancellation_token(external_token.clone()))
            .await
            .unwrap();

        assert!(!task_handle.ctrl.id().is_empty());
        assert!(!task_handle.ctrl.cancellation_token().is_cancelled());

        // 取消外部令牌
        external_token.cancel();

        // 等待一小段时间让异步任务完成
        tokio::time::sleep(Duration::from_millis(10)).await;

        // 任务的取消令牌应该也被取消
        assert!(task_handle.ctrl.cancellation_token().is_cancelled());
    }

    #[tokio::test]
    async fn test_agent_run_task_with_agent_shutdown() {
        let llm = MockLLM::new("test response");
        let agent = ReActAgent::builder()
            .with_name("TestAgent")
            .with_description("Test agent description")
            .with_llm(llm)
            .with_llm_provider(MockLLMProvider)
            .with_prompt_provider(TestPromptProvider)
            .with_handle_factory(MockGraphBuilder)
            .with_check_pointer(MockCheckPointer)
            .build()
            .expect("Failed to build agent");

        let question = "test question".to_string();

        // 运行任务
        let task_handle = agent.clone().run_task(question).await.unwrap();
        assert!(!task_handle.ctrl.cancellation_token().is_cancelled());

        // 关闭 agent
        agent.shutdown();

        // 等待一小段时间让异步任务完成
        tokio::time::sleep(Duration::from_millis(10)).await;

        // 任务的取消令牌应该被取消
        assert!(task_handle.ctrl.cancellation_token().is_cancelled());
    }

    #[tokio::test]
    async fn test_agent_run_task_with_both_tokens_cancelled() {
        let llm = MockLLM::new("test response");
        let agent = ReActAgent::builder()
            .with_name("TestAgent")
            .with_description("Test agent description")
            .with_llm(llm)
            .with_llm_provider(MockLLMProvider)
            .with_prompt_provider(TestPromptProvider)
            .with_handle_factory(MockGraphBuilder)
            .with_check_pointer(MockCheckPointer)
            .build()
            .expect("Failed to build agent");

        let external_token = CancellationToken::new();
        let question = "test question".to_string();

        // 使用外部取消令牌运行任务
        let task_handle = agent
            .clone()
            .run_task(RunTaskRequest::new(question).with_cancellation_token(external_token.clone()))
            .await
            .unwrap();

        assert!(!task_handle.ctrl.cancellation_token().is_cancelled());

        // 同时取消 agent 和外部令牌
        external_token.cancel();
        agent.shutdown();

        // 等待一小段时间让异步任务完成
        tokio::time::sleep(Duration::from_millis(10)).await;

        // 任务的取消令牌应该被取消
        assert!(task_handle.ctrl.cancellation_token().is_cancelled());
    }

    #[tokio::test]
    async fn test_agent_run_task_multiple_tasks_with_different_tokens() {
        let llm = MockLLM::new("test response");
        let agent = ReActAgent::builder()
            .with_name("TestAgent")
            .with_description("Test agent description")
            .with_llm(llm)
            .with_llm_provider(MockLLMProvider)
            .with_prompt_provider(TestPromptProvider)
            .with_handle_factory(MockGraphBuilder)
            .with_check_pointer(MockCheckPointer)
            .build()
            .expect("Failed to build agent");

        let token1 = CancellationToken::new();
        let token2 = CancellationToken::new();

        // 运行两个任务，使用不同的外部取消令牌
        let task1 = agent
            .clone()
            .run_task(
                RunTaskRequest::new("task 1".to_string()).with_cancellation_token(token1.clone()),
            )
            .await
            .unwrap();

        let task2 = agent
            .clone()
            .run_task(
                RunTaskRequest::new("task 2".to_string()).with_cancellation_token(token2.clone()),
            )
            .await
            .unwrap();

        assert!(!task1.ctrl.cancellation_token().is_cancelled());
        assert!(!task2.ctrl.cancellation_token().is_cancelled());

        // 只取消第一个任务的外部令牌
        token1.cancel();

        // 等待一小段时间让异步任务完成
        tokio::time::sleep(Duration::from_millis(10)).await;

        // 只有第一个任务应该被取消
        assert!(task1.ctrl.cancellation_token().is_cancelled());
        assert!(!task2.ctrl.cancellation_token().is_cancelled());

        // 关闭 agent，第二个任务也应该被取消
        agent.shutdown();

        // 等待一小段时间让异步任务完成
        tokio::time::sleep(Duration::from_millis(10)).await;

        assert!(task2.ctrl.cancellation_token().is_cancelled());
    }

    #[tokio::test]
    async fn test_agent_run_task_without_external_token() {
        let llm = MockLLM::new("test response");
        let agent = ReActAgent::builder()
            .with_name("TestAgent")
            .with_description("Test agent description")
            .with_llm(llm)
            .with_llm_provider(MockLLMProvider)
            .with_prompt_provider(TestPromptProvider)
            .with_handle_factory(MockGraphBuilder)
            .with_check_pointer(MockCheckPointer)
            .build()
            .expect("Failed to build agent");

        let question = "test question".to_string();

        // 不使用外部取消令牌运行任务
        let task_handle = agent.clone().run_task(question).await.unwrap();
        assert!(!task_handle.ctrl.cancellation_token().is_cancelled());

        // 关闭 agent
        agent.shutdown();

        // 等待一小段时间让异步任务完成
        tokio::time::sleep(Duration::from_millis(10)).await;

        // 任务应该被取消（因为只有 agent 的取消令牌）
        assert!(task_handle.ctrl.cancellation_token().is_cancelled());
    }

    #[tokio::test]
    async fn test_agent_combined_token_propagation_to_stream() {
        let llm = MockLLM::new("test response");
        let agent = ReActAgent::builder()
            .with_name("TestAgent")
            .with_description("Test agent description")
            .with_llm(llm)
            .with_llm_provider(MockLLMProvider)
            .with_prompt_provider(TestPromptProvider)
            .with_handle_factory(MockGraphBuilder)
            .with_check_pointer(MockCheckPointer)
            .build()
            .expect("Failed to build agent");

        let external_token = CancellationToken::new();
        let question = "test question".to_string();

        let mut task = agent
            .run_task(RunTaskRequest::new(question).with_cancellation_token(external_token.clone()))
            .await
            .unwrap();

        let ctrl = task.control().clone();
        let mut stream = task.stream();

        // 在另一个任务中取消外部令牌
        let external_token_clone = external_token.clone();
        let cancel_handle = tokio::spawn(async move {
            external_token_clone.cancel();
        });

        // 尝试从流中读取事件，应该能检测到取消
        let mut received_events = 0;
        let start_time = std::time::Instant::now();

        while let Some(_event) = stream.next().await {
            received_events += 1;

            // 检查是否已经取消
            if ctrl.cancellation_token().is_cancelled() {
                break;
            }

            // 防止无限等待
            if start_time.elapsed() > Duration::from_millis(200) {
                break;
            }
        }

        // 等待取消任务完成
        cancel_handle.await.unwrap();

        // 验证取消令牌确实被取消了
        assert!(ctrl.cancellation_token().is_cancelled());
    }
}
