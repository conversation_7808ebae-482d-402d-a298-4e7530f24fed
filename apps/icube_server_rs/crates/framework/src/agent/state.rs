use serde::{Deserialize, Serialize};
use std::fmt::Debug;

use crate::core::artifact::{Artifact, Artifacts};
use crate::core::message::ToolCall;
use crate::core::{error::AgentError, message::Message};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ToolCallAction {
    pub status: String,
    pub tool_name: String,
    pub tool_call: ToolCall,
}

#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Deserialize)]
pub struct RunState<T> {
    pub state: T,
    messages: Vec<Message>,
    artifacts: Artifacts,
    pub error: Option<AgentError>,
    pub turns: u32,
    /// 当前轮正在处理的工具索引，本轮工具处理完毕需重置为 0
    pub processing_toolcall_index: usize,
}

impl<T: Serialize + Clone + Debug> RunState<T> {
    pub fn new(state: T) -> Self {
        Self {
            state,
            messages: Vec::new(),
            error: None,
            turns: 0,
            artifacts: Artifacts::default(),
            processing_toolcall_index: 0,
        }
    }

    pub fn with_artifacts(mut self, artifacts: Artifacts) -> Self {
        self.artifacts = artifacts;
        self
    }

    pub fn with_turns(mut self, turns: u32) -> Self {
        self.turns = turns;
        self
    }

    pub fn with_history(mut self, messages: &[Message]) -> Self {
        if !messages.is_empty() {
            self.messages.splice(0..0, messages.iter().cloned());
        }
        self
    }

    pub fn with_message(mut self, message: Message) -> Self {
        self.messages.push(message);
        self
    }

    pub fn messages(&self) -> &[Message] {
        &self.messages
    }

    pub fn artifacts(&self) -> &Artifacts {
        &self.artifacts
    }

    pub fn add_message(&mut self, message: Message) {
        self.messages.push(message);
    }

    pub fn add_artifact(&mut self, artifact: Artifact) {
        self.artifacts.push(artifact);
    }

    pub fn extend_artifacts(&mut self, artifacts: Artifacts) {
        self.artifacts.extend(artifacts);
    }

    pub fn user_data(&self) -> &T {
        &self.state
    }

    pub fn user_data_mut(&mut self) -> &mut T {
        &mut self.state
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[derive(Debug, Clone, Serialize, Deserialize, Default)]
    pub struct UserState {
        pub foo: String,
    }

    #[test]
    fn test_run_state() {
        let mut state = RunState::new(UserState {
            foo: "bar".to_string(),
        });
        state.add_message(Message::new_human_message("Hello, world!"));
        assert_eq!(state.messages().len(), 1);
        assert_eq!(state.artifacts().len(), 0);

        state.add_artifact(Artifact::new("test_artifact", "value"));
        assert_eq!(state.artifacts().len(), 1);

        state = state.with_history(&vec![
            Message::new_human_message("Previous message 1"),
            Message::new_ai_message("Previous message 2"),
        ]);

        assert_eq!(state.messages().len(), 3);
    }
}
