use std::{borrow::Cow, future::Future, pin::Pin};

use crate::core::{error::AgentError, handler::StageHandler};

pub type ErrorHandlerFuncResult = Pin<Box<dyn Future<Output = Result<(), AgentError>> + Send>>;
pub type ErrorHandlerFunc<GS, C> =
    Box<dyn Fn(C, &mut GS, AgentError) -> ErrorHandlerFuncResult + Sync + Send + 'static>;

pub struct GraphErrorHandler<GlobalState, Context> {
    func: Option<ErrorHandlerFunc<GlobalState, Context>>,
}
impl<GlobalState, Context> GraphErrorHandler<GlobalState, Context> {
    pub fn new(func: Option<ErrorHandlerFunc<GlobalState, Context>>) -> Self {
        Self { func }
    }
}

#[async_trait::async_trait]
impl<GS, C> StageHandler<GS, C> for GraphErrorHandler<GS, C>
where
    GS: Send + Sync + 'static,
    C: Send + Sync + 'static,
{
    type Incoming = AgentError;
    type Outgoing = ();
    fn name(&self) -> Cow<'static, str> {
        "__ERROR__".into()
    }
    async fn handle(
        &self,
        ctx: C,
        state: &mut GS,
        input: Self::Incoming,
    ) -> Result<Self::Outgoing, AgentError> {
        if let Some(ref func) = self.func {
            func(ctx, state, input).await?;
        }
        Ok(())
    }
}
