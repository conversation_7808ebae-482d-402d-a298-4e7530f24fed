use crate::supervisor::{<PERSON><PERSON><PERSON>, CallTool, ResumeAction};
use async_trait::async_trait;
use framework::core::error::AgentError;
use framework::graph::builtin_action::interrupt::Interrupt;
use framework::graph::builtin_action::interrupt::InterruptReason::AskUser;
use framework::graph::InterruptHandler;

#[derive(Debug)]
pub struct ResumeHandler;
use crate::{Context, SupervisorState};

#[async_trait]
impl InterruptHandler<SupervisorState, Context> for ResumeHandler {
    type Outgoing = ResumeAction;

    async fn on_resume(
        &self,
        ctx: Context,
        state: &mut SupervisorState,
        input: Interrupt,
    ) -> Result<Self::Outgoing, AgentError> {
        match input.reason {
            AskUser(question) => {
                // if the question is issued by the agent itself, we can call the tool directly
                if question.agent_name == ctx.name() {
                    Ok(ResumeAction::CallTool(CallTool {
                        tool_call: input.trigger_tool_calls[0].clone(),
                    }))
                } else {
                    // otherwise, we need to resume the agent with the user action
                    Ok(ResumeAction::CallAgent(CallAgent {
                        resume: true,
                        task_id: Some(question.task_id),
                        agent_name: question.agent_name,
                        tool_call: input.trigger_tool_calls[0].clone(),
                        user_action: input.answer,
                    }))
                }
            }
            _ => {
                // eprintln!("Unknown interrupt reason: {:?}", input.reason);
                Err(AgentError::InternalError(format!(
                    "Unknown interrupt reason: {:?}",
                    input.reason
                )))
            }
        }
    }
}
