use super::{CallAgent, CallAgentDecision};
use crate::agent::AgentOutput;
use crate::{Context, SupervisorState};
use async_trait::async_trait;
use framework::core::agent::DynRunTaskRequest;
use framework::core::{error::AgentError, event::Event, handler::<PERSON><PERSON><PERSON><PERSON>, message::Message};
use framework::graph::builtin_action::interrupt::Interrupt;
use futures::StreamExt;

#[derive(Debug)]
pub struct AgentCallHandler;

#[async_trait]
impl StageHandler<SupervisorState, Context> for AgentCallHandler {
    type Incoming = CallAgent;
    type Outgoing = CallAgentDecision;

    async fn handle(
        &self,
        ctx: Context,
        state: &mut SupervisorState,
        input: Self::Incoming,
    ) -> Result<Self::Outgoing, AgentError> {
        let human_message = state
            .messages()
            .iter()
            .filter_map(|m| {
                if let Message::HumanMessage(human_message) = m {
                    Some(human_message)
                } else {
                    None
                }
            })
            .map(|s| s.to_string())
            .collect::<Vec<String>>()
            .join(", ");

        let mut task_handle = {
            if input.resume {
                // todo: better error handling
                ctx.emit_message(Event::ResponseGenerated {
                    agent_name: ctx.name().to_string(),
                    agent_run_id: ctx.agent_run_id(),
                    response_text: format!("resume to @{}", input.agent_name),
                });
                ctx.resume_agent(
                    input.agent_name.clone(),
                    input.task_id.unwrap(),
                    input.user_action.unwrap(),
                )
                .await?
            } else {
                ctx.emit_message(Event::ResponseGenerated {
                    agent_name: ctx.name().to_string(),
                    agent_run_id: ctx.agent_run_id(),
                    response_text: format!("handoff to @{}", input.agent_name),
                });
                let req = DynRunTaskRequest::new(human_message)
                    // 先默认把所有的 artifacts 都带进去
                    .with_artifacts(state.artifacts().clone())
                    .with_config(ctx.config().clone());
                ctx.forward_to_agent(input.agent_name.clone(), req).await?
            }
        };

        let mut stream = task_handle.stream();
        while let Some(event) = stream.next().await {
            if let Event::Interrupt(intr) = &event {
                return Ok(CallAgentDecision::Interrupt(
                    Interrupt::new(
                        ctx.task_id(),
                        intr.reason.clone(),
                        vec![input.tool_call.clone()],
                    )
                    .caused_by(&intr.task_id),
                ));
            }
            if let Event::ProcessingComplete { output } = &event {
                // todo: handle output properly
                let agent_output: AgentOutput = serde_json::from_value(output.payload.clone())
                    .map_err(|e| {
                        AgentError::InternalError(format!(
                            "failed to deserialize agent output: {:?}",
                            e
                        ))
                    })?;
                // ctx.emit_message(Event::ResponseGenerated {
                //     agent_name: input.agent_name.clone(),
                //     response_text: format!(
                //         "Agent completed task: {}",
                //         Into::<String>::into(agent_output)
                //     ),
                // });
                // 继承产物
                state.extend_artifacts(output.artifacts.clone());
                state.add_message(Message::new_tool_message(
                    input.tool_call.clone(),
                    output.payload.clone(),
                ));
                break;
            }
            ctx.emit_message(event);
        }

        Ok(CallAgentDecision::Finished)
    }
}
