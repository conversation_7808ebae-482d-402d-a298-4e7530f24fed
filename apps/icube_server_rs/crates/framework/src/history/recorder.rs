use crate::core::error::AgentError;
use crate::core::message::{Message, MessageContent, ToolCall};
use crate::history::store::{HistoryMessage, HistoryStore};
use crate::history::HistoryResult;
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use tokio::sync::mpsc::UnboundedSender;
use crate::core::event::Event;

#[derive(serde::Serialize, Debug, Clone)]
pub struct HistoryEntry {
    pub session_id: String,
    pub message_id: String,
    pub user_message_id: String,
    pub agent_type: String,
    pub agent_run_id: String,
    pub message: Message,
}

impl HistoryEntry {
    pub fn new(message: &Message) -> Self {
        Self {
            session_id: String::new(),
            message_id: String::new(),
            user_message_id: String::new(),
            agent_type: String::new(),
            agent_run_id: String::new(),
            message: message.clone(),
        }
    }

    pub fn session_id(mut self, session_id: impl Into<String>) -> Self {
        self.session_id = session_id.into();
        self
    }

    pub fn message_id(mut self, message_id: impl Into<String>) -> Self {
        self.message_id = message_id.into();
        self
    }

    pub fn user_message_id(mut self, user_message_id: impl Into<String>) -> Self {
        self.user_message_id = user_message_id.into();
        self
    }

    pub fn agent_type(mut self, agent_type: impl Into<String>) -> Self {
        self.agent_type = agent_type.into();
        self
    }

    pub fn agent_run_id(mut self, agent_run_id: impl Into<String>) -> Self {
        self.agent_run_id = agent_run_id.into();
        self
    }
}

impl From<HistoryEntry> for HistoryMessage {
    fn from(value: HistoryEntry) -> Self {
        Self {
            session_id: value.session_id,
            message_id: value.message_id,
            agent_type: value.agent_type,
            content: vec![value.message],
            agent_run_id: value.agent_run_id,
        }
    }
}

impl From<ToolCallPair> for HistoryMessage {
    fn from(value: ToolCallPair) -> Self {
        HistoryMessage {
            session_id: value.session_id,
            message_id: value.message_id,
            agent_type: value.agent_type,
            content: vec![value.request, value.response],
            agent_run_id: value.agent_run_id,
        }
    }
}

struct ToolCallPair {
    session_id: String,
    message_id: String,
    agent_type: String,
    agent_run_id: String,
    request: Message,
    response: Message,
}

pub struct HistoryRecorder<C> {
    store: Arc<dyn HistoryStore<C>>,
    pending: Arc<Mutex<HashMap<String, Message>>>,
}

impl<C> HistoryRecorder<C>
where
    C: 'static,
{
    pub fn new(
        store: Arc<dyn HistoryStore<C>>,
        pending: Arc<Mutex<HashMap<String, Message>>>,
    ) -> Self {
        Self { store, pending }
    }

    pub async fn load(
        &self,
        session_id: &str,
        token_limit: Option<usize>,
        message_ids: Vec<String>,
        cfg: &C,
        event_bus: Option<&UnboundedSender<Event>>,
    ) -> HistoryResult<Vec<Message>> {
        self.store
            .load(session_id, token_limit, message_ids, cfg, event_bus)
            .await
    }

    pub async fn append(&self, entry: HistoryEntry, cfg: &C) -> HistoryResult<()> {
        match &entry.message {
            Message::HumanMessage(_) => self.handle_human_message(&entry, cfg).await,
            Message::AIAssistant { tool_calls, .. } => {
                self.handle_ai_assistant_message(&entry, tool_calls.as_ref(), cfg)
                    .await
            }
            Message::ToolMessage { toolcall, .. } => {
                self.handle_tool_message(&entry, toolcall.id.clone(), cfg)
                    .await
            }
            _ => Ok(()),
        }
    }

    pub async fn save_exception_message(
        &self,
        agent_error: &AgentError,
        cfg: &C,
    ) -> HistoryResult<()> {
        self.store
            .save_exception_message( agent_error, cfg)
            .await
    }

    pub async fn compress(&self,
                          session_id: &str,
                          cfg: &C) -> HistoryResult<bool> {
        self.store
            .compress(session_id, cfg)
            .await
    }

    pub async fn update_compress_token_usage(
        &self,
        session_id: &str,
        total_token: u32,
        cfg: &C,
    ) -> HistoryResult<()> {
        self.store
            .update_compress_token_usage(session_id, total_token, cfg)
            .await
    }

    async fn handle_human_message(&self, entry: &HistoryEntry, cfg: &C) -> HistoryResult<()> {
        let history_message: HistoryMessage = entry.clone().into();
        self.store
            .save(&history_message, cfg)
            .await
            .expect("Failed to save history message");
        Ok(())
    }

    async fn handle_ai_assistant_message(
        &self,
        entry: &HistoryEntry,
        tool_calls: Option<&Vec<ToolCall>>,
        cfg: &C,
    ) -> HistoryResult<()> {
        let Some(tool_calls) = tool_calls else {
            return Ok(());
        };

        for tool_call in tool_calls {
            let function_name = &tool_call.function.name;
            /*
            if let Some(agent_name) = function_name.strip_prefix("run_agent_") {
                self.save_run_agent_tool_call_pair(
                    entry,
                    tool_call,
                    &format!(
                        "Sub Agent {} is Going to process the instruction.",
                        agent_name
                    ),
                    cfg,
                )
                .await?;
                continue;
            }
            */

            if function_name == "finish" {
                self.save_finish_message(entry, tool_call, cfg).await?;
                continue;
            }

            // 其它情况：pending 记录
            let mut pending = self.pending.lock().unwrap();
            pending.insert(tool_call.id.clone(), entry.message.clone());
        }
        Ok(())
    }

    /*
    async fn save_run_agent_tool_call_pair(
        &self,
        entry: &HistoryEntry,
        tool_call: &ToolCall,
        msg: &str,
        cfg: &C,
    ) -> HistoryResult<()> {
        let tool_call_pair = ToolCallPair {
            session_id: entry.session_id.clone(),
            message_id: entry.message_id.clone(),
            agent_type: entry.agent_type.clone(),
            agent_run_id: entry.agent_run_id.clone(),
            request: entry.message.clone(),
            response: Message::new_tool_message(
                tool_call.clone(),
                serde_json::Value::String(msg.to_string()),
            ),
        };
        self.store.save(&tool_call_pair.into(), cfg).await?;
        Ok(())
    }
    */

    async fn save_finish_message(
        &self,
        entry: &HistoryEntry,
        tool_call: &ToolCall,
        cfg: &C,
    ) -> HistoryResult<()> {
        let mut finish_msg_entry = entry.clone();
        let summary_text = tool_call
            .function
            .arguments
            .get("summary")
            .and_then(|v| v.as_str())
            .map(|s| s.to_string())
            .unwrap_or_default();
        let new_message_contents = MessageContent::Text(summary_text).into();
        if let Message::AIAssistant { content, .. } = &mut finish_msg_entry.message {
            *content = new_message_contents;
        }
        self.store.save(&finish_msg_entry.into(), cfg).await?;
        Ok(())
    }

    async fn handle_tool_message(
        &self,
        entry: &HistoryEntry,
        toolcall_id: String,
        cfg: &C,
    ) -> HistoryResult<()> {
        let msg = {
            let mut pending = self.pending.lock().unwrap();
            pending.remove(&toolcall_id)
        };
        if let Some(mut msg) = msg {
            // msg 可能存在多个 tool_calls 但本次只处理其中一个，此处只保留本次的 toolcall_id
            msg.retain_tool_calls_by_id(&toolcall_id);

            let tool_call_pair = ToolCallPair {
                session_id: entry.session_id.clone(),
                message_id: entry.message_id.clone(),
                agent_type: entry.agent_type.clone(),
                agent_run_id: entry.agent_run_id.clone(),
                request: msg,
                response: entry.message.clone(),
            };
            self.store.save(&tool_call_pair.into(), cfg).await?;
        }
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use crate::core::error::AgentError;
    use crate::core::message::{FunctionCall, Message, ToolCall};
    use crate::history::store::HistoryMessage;
    use crate::history::{HistoryEntry, HistoryRecorder, HistoryResult, HistoryStore};
    use async_trait::async_trait;
    use serde_json::Value;
    use std::sync::{Arc, Mutex};
    use tokio::sync::mpsc::UnboundedSender;
    use crate::core::event::Event;

    struct MockStore {
        data: Arc<Mutex<Vec<HistoryMessage>>>,
    }
    impl MockStore {
        fn new() -> Self {
            Self {
                data: Arc::new(Mutex::new(Vec::new())),
            }
        }

        fn get_data(&self) -> Vec<HistoryMessage> {
            self.data.lock().unwrap().clone()
        }
    }

    #[async_trait]
    impl HistoryStore<String> for MockStore {
        async fn save(&self, message: &HistoryMessage, _cfg: &String) -> HistoryResult<()> {
            let mut data = self.data.lock().unwrap();
            data.push(message.clone());
            Ok(())
        }

        async fn load(
            &self,
            session_id: &str,
            token_limit: Option<usize>,
            message_ids: Vec<String>,
            _cfg: &String,
            _event_bus: Option<&UnboundedSender<Event>>,
        ) -> HistoryResult<Vec<Message>> {
            todo!()
        }

        async fn save_exception_message(
            &self,
            _agent_error: &AgentError,
            _cfg: &String,
        ) -> HistoryResult<()> {
            todo!()
        }

        async fn compress(
            &self,
            session_id: &str,
            cfg: &String,
        ) -> HistoryResult<bool> {
            todo!()
        }

        async fn update_compress_token_usage(
            &self,
            session_id: &str,
            total_token: u32,
            cfg: &String,
        ) -> HistoryResult<()> {
            todo!()
        }
    }

    fn mock_recorder(store: Arc<MockStore>) -> HistoryRecorder<String> {
        HistoryRecorder::new(store, Default::default())
    }

    #[tokio::test]
    async fn test_append_human_message() {
        let store = Arc::new(MockStore::new());
        let recorder = mock_recorder(store.clone());
        let entry = HistoryEntry::new(&Message::new_human_message("Hello"))
            .session_id("session1")
            .message_id("msg1")
            .agent_type("test_agent");

        assert!(recorder.append(entry, &"".into()).await.is_ok());
        assert_eq!(store.get_data().len(), 1);
    }

    #[tokio::test]
    async fn test_append_tool_call() {
        let store = Arc::new(MockStore::new());
        let recorder = mock_recorder(store.clone());
        let tool_call = ToolCall {
            id: "test".into(),
            index: 1,
            function: FunctionCall::default(),
        };
        let msg = Message::new_ai_message("Hello").with_tool_calls(Some(vec![tool_call.clone()]));
        let entry = HistoryEntry::new(&msg)
            .session_id("session1")
            .message_id("msg1")
            .agent_type("test_agent");

        assert!(recorder.append(entry, &"".into()).await.is_ok());
        assert_eq!(store.get_data().len(), 0);

        let msg = Message::new_tool_message(tool_call, Value::Null);
        let entry = HistoryEntry::new(&msg)
            .session_id("session1")
            .message_id("msg1")
            .agent_type("test_agent");

        assert!(recorder.append(entry, &"".into()).await.is_ok());
        assert_eq!(store.get_data().len(), 1);
    }
}
