//! Ratatui-based CLI interface for UI Builder

use crate::cli::app::UserDecision;
use crate::utils::formatter::format_event;
use crate::Config;
use anyhow::Result;
use crossterm::event::{self, Event, KeyCode, KeyEventKind};
use framework::agent::execution::RunTaskRequest;
use framework::agent::react_agent::ReActAgent;
use framework::core::agent::Agent;
use framework::core::event::Event as FrameworkEvent;
use framework::graph::builtin_action::interrupt::Decision::{Approve, Reject};
use framework::graph::builtin_action::interrupt::{InterruptReason, UserAction};
use futures::StreamExt;
use ratatui::DefaultTerminal;
use std::sync::Arc;
use tokio::sync::mpsc;
use tokio::sync::mpsc::UnboundedSender;

use super::app::App;
use super::ui_renderer;

// Define agent type aliases
type SupervisorAgent = Arc<ReActAgent<(), Config>>;

/// Ratatui-based CLI interface
pub struct RatatuiCLI {
    supervisor: SupervisorAgent,
    app: App,
}

impl RatatuiCLI {
    /// Create a new Ratatui CLI instance
    pub fn new(
        supervisor: SupervisorAgent,
    ) -> Self {
        Self {
            supervisor,
            app: App::new(),
        }
    }

    fn handle_workflow_message(&mut self, message: String) {
        if message.starts_with("INTERRUPT_PRD:") {
            let prd_content = message
                .strip_prefix("INTERRUPT_PRD:")
                .unwrap_or("")
                .to_string();
            self.app.set_interrupt_confirmation(prd_content);
        } else if message.starts_with("TASK_ID:") {
            let task_id = message.strip_prefix("TASK_ID:").unwrap_or("").to_string();
            self.app.set_task_id(task_id);
        } else {
            self.app.add_message(message, false);
            if matches!(self.app.mode, crate::cli::app::AppMode::Normal) {
                self.app.refresh_components_with_highlight();
            }
        }
    }

    /// Run the interactive CLI session with ratatui
    pub async fn run(&mut self, query: String) -> Result<()> {
        println!("Starting CLI with query: '{}'", query);
        // color_eyre::install().map_err(|e| anyhow::anyhow!("Failed to install color_eyre: {}", e))?;
        let terminal = ratatui::init();
        println!("Terminal initialized successfully");
        let result = self.run_with_terminal(terminal, query).await;
        ratatui::restore();
        println!("CLI session ended");
        result
    }

    async fn run_with_terminal(
        &mut self,
        mut terminal: DefaultTerminal,
        query: String,
    ) -> Result<()> {
        // Create message channel for workflow communication
        let (message_tx, mut message_rx) = mpsc::unbounded_channel::<String>();

        // Set initial query if provided
        if !query.is_empty() {
            self.app.set_input(query.clone());
            self.app.add_message(format!("User: {}", query), true);
        }

        // Start the workflow in background if query is provided
        let workflow_handle = if !query.is_empty() {
            Some(tokio::spawn({
                let supervisor = self.supervisor.clone();
                let query = query.clone();
                let tx = message_tx.clone();
                async move { Self::run_workflow(supervisor, query, tx).await }
            }))
        } else {
            None
        };

        loop {
            // Check for workflow messages BEFORE drawing the UI
            let mut messages_processed = false;
            while let Ok(message) = message_rx.try_recv() {
                messages_processed = true;
                self.handle_workflow_message(message);
            }

            // Always draw the UI to ensure latest state is displayed
            terminal.draw(|frame| ui_renderer::draw(frame, &mut self.app))?;

            // Use timeout to avoid blocking on event::read()
            // Shorter timeout to be more responsive to new messages from workflow
            let has_keyboard_event = crossterm::event::poll(std::time::Duration::from_millis(50))?;

            if has_keyboard_event {
                if let Event::Key(key) = event::read()? {
                    if key.kind == KeyEventKind::Press {
                        match key.code {
                            KeyCode::Char('q') => break,
                            KeyCode::Esc => {
                                if matches!(self.app.mode, crate::cli::app::AppMode::PrdViewer) {
                                    self.app.clear_interrupt();
                                    self.app.prd_scroll = 0;
                                    self.app.refresh_components();

                                    while let Ok(message) = message_rx.try_recv() {
                                        self.handle_workflow_message(message);
                                    }

                                    terminal
                                        .draw(|frame| ui_renderer::draw(frame, &mut self.app))?;
                                } else {
                                    self.app.handle_key_event(key);
                                }
                            }
                            KeyCode::Enter => {
                                let input = self.app.get_input().trim().to_string();
                                let was_prd_mode =
                                    matches!(self.app.mode, crate::cli::app::AppMode::PrdViewer);

                                let user_decision = self.app.handle_key_event(key);

                                if was_prd_mode {
                                    self.app.refresh_components();

                                    while let Ok(message) = message_rx.try_recv() {
                                        self.handle_workflow_message(message);
                                    }

                                    terminal
                                        .draw(|frame| ui_renderer::draw(frame, &mut self.app))?;
                                }

                                if let Some(decision) = user_decision {
                                    if let Some(task_id) = self.app.get_task_id().cloned() {
                                        let supervisor = self.supervisor.clone();
                                        let tx = message_tx.clone();
                                        tokio::spawn(async move {
                                            Self::resume_workflow_with_decision(
                                                supervisor, task_id, decision, tx,
                                            )
                                            .await;
                                        });
                                    }
                                } else if (was_prd_mode
                                    || (matches!(self.app.mode, crate::cli::app::AppMode::Normal)
                                        && matches!(
                                            self.app.interrupt_state,
                                            crate::cli::app::InterruptState::None
                                        )))
                                    && !input.is_empty()
                                {
                                    let supervisor = self.supervisor.clone();
                                    let tx = message_tx.clone();
                                    tokio::spawn(async move {
                                        let _ = Self::run_workflow(supervisor, input, tx).await;
                                    });
                                }
                            }
                            KeyCode::Down => {
                                if matches!(self.app.mode, crate::cli::app::AppMode::PrdViewer) {
                                    self.app.prd_scroll = self.app.prd_scroll.saturating_add(1);
                                } else {
                                    self.app.handle_key_event(key);
                                }
                            }
                            KeyCode::Up => {
                                if matches!(self.app.mode, crate::cli::app::AppMode::PrdViewer) {
                                    self.app.prd_scroll = self.app.prd_scroll.saturating_sub(1);
                                } else {
                                    self.app.handle_key_event(key);
                                }
                            }
                            _ => {
                                let user_decision = self.app.handle_key_event(key);

                                if let Some(decision) = user_decision {
                                    if let Some(task_id) = self.app.get_task_id().cloned() {
                                        let supervisor = self.supervisor.clone();
                                        let tx = message_tx.clone();
                                        tokio::spawn(async move {
                                            let _ = Self::resume_workflow_with_decision(
                                                supervisor, task_id, decision, tx,
                                            )
                                            .await;
                                        });
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // Continue the loop even without keyboard events to ensure message processing
            // This fixes the issue where workflow messages weren't showing after PRD view mode

            // Check if workflow is complete (only break if there was an initial workflow)
            if let Some(ref handle) = workflow_handle {
                if handle.is_finished() {
                    // Don't break immediately, let user continue using the CLI
                    // break;
                }
            }
        }

        Ok(())
    }

    /// Run the multi-agent workflow
    async fn run_workflow(
        supervisor: SupervisorAgent,
        user_query: String,
        message_tx: mpsc::UnboundedSender<String>,
    ) -> Result<Vec<String>> {
        let mut messages = Vec::new();

        if user_query.is_empty() {
            let msg = "❌ User query is empty".to_string();
            let _ = message_tx.send(msg.clone());
            messages.push(msg);
            return Ok(messages);
        }

        let msg = "🚀 Starting multi-agent workflow...".to_string();
        let _ = message_tx.send(msg.clone());
        messages.push(msg);

        // Start the real multi-agent workflow
        let mut task = supervisor
            .clone()
            .run_task(RunTaskRequest::new(user_query))
            .await
            .map_err(|e| anyhow::anyhow!("Failed to start task: {}", e))?;

        let ctrl = task.control();
        let task_id = ctrl.id();
        let msg = format!("📋 Task ID: {}", task_id);
        let _ = message_tx.send(msg.clone());
        messages.push(msg);

        let _ = message_tx.send(format!("TASK_ID:{}", task_id));

        loop {
            let should_resume = false;
            let resume_action: Option<UserAction> = None;

            let need_resume = {
                let mut stream = task.stream();
                let mut should_resume = false;
                while let Some(event) = stream.next().await {
                    // Display formatted event
                    let formatted_event = format_event(&event);
                    let _ = message_tx.send(formatted_event.clone());
                    messages.push(formatted_event);

                    if let FrameworkEvent::Interrupt(interrupt) = &event {
                        if let InterruptReason::AskUser(question) = &interrupt.reason {
                            // Extract PRD content if available
                            if let Some(content) = question
                                .payload
                                .as_ref()
                                .and_then(|p| p.get("AIAssistant"))
                                .and_then(|ai| ai.get("tool_calls"))
                                .and_then(|tc| tc.as_array())
                                .and_then(|arr| arr.first())
                                .and_then(|call| call.get("function"))
                                .and_then(|func| func.get("arguments"))
                                .and_then(|args| args.get("content"))
                            {
                                let msg = "📋 PRD Content Generated - Please review and confirm"
                                    .to_string();
                                let _ = message_tx.send(msg.clone());
                                messages.push(msg);

                                let prd_content = if let Some(content_str) = content.as_str() {
                                    content_str.to_string()
                                } else {
                                    serde_json::to_string(content).unwrap_or_else(|_| {
                                        "Failed to serialize content".to_string()
                                    })
                                };

                                // Send special message to trigger PRD view mode
                                let msg = format!("INTERRUPT_PRD:{}", prd_content);
                                let _ = message_tx.send(msg);
                            }

                            // Wait for user confirmation instead of auto-approving
                            should_resume = false;
                            break;
                        }
                    }
                }
                should_resume
            };

            if need_resume {
                // For now, auto-approve PRD (in a real implementation, this would be user input)
                let user_action = UserAction {
                    message: None,
                    payload: None,
                    decision: Approve,
                };

                task = supervisor
                    .clone()
                    .resume_task(&task_id, user_action)
                    .await
                    .map_err(|e| anyhow::anyhow!("Failed to resume task: {}", e))?;
            }

            if !should_resume {
                break;
            }
        }

        Ok(messages)
    }

    async fn resume_workflow_with_decision(
        supervisor: Arc<ReActAgent<(), Config>>,
        task_id: String,
        decision: UserDecision,
        message_tx: UnboundedSender<String>,
    ) {
        if let Err(e) = message_tx.send(format!(
            "🔄 Resuming workflow {} with decision: {:?}",
            task_id, decision
        )) {
            eprintln!("Failed to send resume message: {}", e);
            return;
        }

        // Add small delay to ensure message is processed before continuing
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

        let user_action = match decision {
            UserDecision::Approve => {
                if let Err(e) = message_tx
                    .send("✅ User approved the requirement. Continuing workflow...".to_string())
                {
                    eprintln!("Failed to send approval message: {}", e);
                    return;
                }
                UserAction {
                    message: None,
                    payload: None,
                    decision: Approve,
                }
            }
            UserDecision::Reject => {
                if let Err(e) = message_tx
                    .send("❌ User rejected the requirement. Stopping workflow...".to_string())
                {
                    eprintln!("Failed to send rejection message: {}", e);
                    return;
                }
                UserAction {
                    message: None,
                    payload: None,
                    decision: Reject,
                }
            }
            UserDecision::AdditionalInput(input) => {
                if let Err(e) =
                    message_tx.send(format!("📝 User provided additional input: {}", input))
                {
                    eprintln!("Failed to send additional input message: {}", e);
                    return;
                }
                UserAction {
                    message: Some(input),
                    payload: None,
                    decision: Approve,
                }
            }
        };

        let mut task = match supervisor.resume_task(&task_id, user_action).await {
            Ok(task) => {
                if let Err(e) = message_tx
                    .send("🔄 Task resumed successfully, continuing workflow...".to_string())
                {
                    eprintln!("Failed to send message: {}", e);
                    return;
                }
                task
            }
            Err(e) => {
                if let Err(send_e) = message_tx.send(format!("❌ Failed to resume task: {}", e)) {
                    eprintln!("Failed to send error message: {}", send_e);
                }
                return;
            }
        };

        loop {
            let need_resume = {
                let mut stream = task.stream();
                let mut should_resume = false;

                while let Some(event) = stream.next().await {
                    let formatted_event = format_event(&event);
                    if let Err(e) = message_tx.send(formatted_event) {
                        eprintln!("Failed to send workflow event message: {}", e);
                        return;
                    }

                    if let FrameworkEvent::Interrupt(interrupt) = &event {
                        if let InterruptReason::AskUser(question) = &interrupt.reason {
                            if let Some(content) = question
                                .payload
                                .as_ref()
                                .and_then(|p| p.get("AIAssistant"))
                                .and_then(|ai| ai.get("tool_calls"))
                                .and_then(|tc| tc.as_array())
                                .and_then(|arr| arr.first())
                                .and_then(|call| call.get("function"))
                                .and_then(|func| func.get("arguments"))
                                .and_then(|args| args.get("content"))
                            {
                                let msg = "📋 PRD Content Generated - Please review and confirm"
                                    .to_string();
                                if let Err(e) = message_tx.send(msg) {
                                    eprintln!("Failed to send PRD notification: {}", e);
                                    return;
                                }

                                let prd_content = if let Some(content_str) = content.as_str() {
                                    content_str.to_string()
                                } else {
                                    serde_json::to_string(content).unwrap_or_else(|_| {
                                        "Failed to serialize content".to_string()
                                    })
                                };

                                if let Err(e) =
                                    message_tx.send(format!("INTERRUPT_PRD:{}", prd_content))
                                {
                                    eprintln!("Failed to send PRD content: {}", e);
                                    return;
                                }
                            }

                            should_resume = false;
                            break;
                        }
                    }
                }
                should_resume
            };

            if !need_resume {
                break;
            }
        }
    }
}
