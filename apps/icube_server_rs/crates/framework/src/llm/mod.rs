use serde::{Deserialize, Serialize};

use crate::core::message::Tool<PERSON>all;
use crate::llm::error::LLMError;

#[cfg(test)]
mod deadlock_tests;
pub mod error;
pub mod llm_stream_parser;
pub mod options;
#[cfg(test)]
mod parser_tests;
#[cfg(test)]
mod performance_tests;

#[derive(Debug, Serialize, Deserialize, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct TokenUsage {
    pub prompt_tokens: u32,
    pub completion_tokens: u32,
    pub total_tokens: u32,
}

#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct GenerateResult {
    pub tokens: Option<TokenUsage>,
    pub generation: String,
    // pub reasoning_content: String,
    pub tool_calls: Option<Vec<ToolCall>>,
    pub error: Option<LLMError>,
}
impl GenerateResult {
    pub fn is_need_retry(&self) -> bool {
        if self.is_json_invalid_retry() {
            return true;
        }
        false
    }

    pub fn is_json_invalid_retry(&self) -> bool {
        self.error
            .as_ref()
            .map(LLMError::is_json_invalid_retry)
            .unwrap_or(false)
    }

    pub fn is_json_invalid_need_fix(&self) -> bool {
        self.error
            .as_ref()
            .map(LLMError::is_json_invalid_need_fix)
            .unwrap_or(false)
    }

    pub fn first_toolcall(&self) -> Option<&ToolCall> {
        self.tool_calls.as_ref().and_then(|v| v.first())
    }

    pub fn first_toolcall_name(&self) -> Option<String> {
        self.first_toolcall().map(|v| v.function.name.clone())
    }

    pub fn should_mock_finish_toolcall(&self) -> bool {
        self.first_toolcall().is_none() && self.error.is_none()
    }

    pub fn is_prompt_too_long_retry(&self) -> bool {
        if let Some(LLMError::PromptTooLongError { should_retry, .. }) = &self.error {
            *should_retry
        } else {
            false
        }
    }

    pub fn toolcall_count(&self) -> usize {
        self.tool_calls.as_ref().map_or(0, |v| v.len())
    }
}

impl crate::core::llm::LLMGenerateResult for GenerateResult {
    fn generation(&self) -> impl Into<String> {
        self.generation.clone()
    }

    fn tool_calls(&self) -> Option<&[ToolCall]> {
        self.tool_calls.as_deref()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::llm::LLMGenerateResult;

    #[test]
    fn test_toolcall_count() {
        let result = GenerateResult::default();
        assert_eq!(result.toolcall_count(), 0);

        let result = GenerateResult {
            tool_calls: Some(vec![]),
            ..Default::default()
        };
        assert_eq!(result.toolcall_count(), 0);

        let result = GenerateResult {
            tool_calls: Some(vec![Default::default(), Default::default()]),
            ..Default::default()
        };
        assert_eq!(result.toolcall_count(), 2);
    }
}
