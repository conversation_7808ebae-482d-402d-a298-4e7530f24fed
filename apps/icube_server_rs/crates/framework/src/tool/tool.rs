use crate::{
    core::{
        error::ToolError,
        message::<PERSON><PERSON><PERSON><PERSON>,
        tool::{CallToolResponse, DynTool, Tool, ToolCallingContext, ToolResult},
    },
    llm::error::LLMError,
    schemas::FunctionDefinition,
};
use async_trait::async_trait;
use futures::Stream;
use serde_json::{json, Value};
use std::{any::type_name, borrow::Cow, collections::HashSet, ops::Deref, pin::Pin, sync::Arc};
use tokio_stream::StreamExt;

use super::tool_box::ToolDefinitionTag;

#[derive(Debug)]
pub struct ToolCallStreamFrame(pub ToolCall);
impl Deref for ToolCallStreamFrame {
    type Target = ToolCall;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}
impl From<ToolCallStreamFrame> for ToolCall {
    fn from(value: ToolCallStreamFrame) -> Self {
        value.0
    }
}
pub type PinBoxToolStreamParam =
    Pin<Box<dyn Stream<Item = ToolCallStreamFrame> + Send + Sync + Unpin + 'static>>;

#[async_trait]
impl<T> DynTool for T
where
    T: Tool + Sync + Send + 'static,
{
    fn name(&self) -> Cow<str> {
        self.name()
    }

    fn tag(&self) -> ToolDefinitionTag {
        self.tag()
    }

    fn description(&self) -> Cow<str> {
        self.description()
    }

    async fn call_tool(
        &self,
        ctx: ToolCallingContext,
        param: Option<serde_json::Value>,
    ) -> ToolResult<CallToolResponse> {
        // dbg!(&param);
        let param = param
            .map(serde_json::from_value::<T::Params>)
            .transpose()
            .map_err(|e| ToolError::InvalidParam {
                tool_name: self.name().into(),
                param_type: type_name::<T::Params>().into(),
                message: e.to_string(),
            })?;
        match self.call(ctx.clone(), param.clone()).await {
            Ok(result) => {
                // 从 toolcall 结果提取 artifact
                let artifact = self.to_artifact(ctx.clone(), param.as_ref(), &result);
                let toolmessage_result = self
                    .to_toolmessage_result(ctx, param.as_ref(), &result)
                    .await
                    .map_err(|e| ToolError::InvalidResponseFormat(e.to_string()))?;
                let mut res = CallToolResponse::success(toolmessage_result, json!(result));
                if let Some(artifact) = artifact {
                    res.with_artifacts(artifact);
                }
                Ok(res)
            }
            Err(e) => Ok(CallToolResponse::fail(e)),
        }
    }

    async fn handle_error(
        &self,
        ctx: ToolCallingContext,
        params: Option<serde_json::Value>,
        err: LLMError,
    ) -> ToolResult<CallToolResponse> {
        let param = params.map(|p| serde_json::from_value::<T::Params>(p).unwrap_or_default());
        match self
            .handle_error(ctx.clone(), param.clone(), err.clone())
            .await
        {
            Ok(result) => {
                // 从 toolcall 结果提取 artifact
                let artifact = self.to_artifact(ctx.clone(), param.as_ref(), &result);
                let toolmessage_result = self
                    .to_toolmessage_result(ctx, param.as_ref(), &result)
                    .await
                    .map_err(|e| ToolError::InvalidResponseFormat(e.to_string()))?;
                let mut res = CallToolResponse::success(toolmessage_result, json!(result));
                if let Some(artifact) = artifact {
                    res.with_artifacts(artifact);
                }
                Ok(res)
            }
            Err(ToolError::LLMError(e)) => Err(e.into()),
            Err(e) => Ok(CallToolResponse::fail(e)),
        }
    }

    async fn call_tool_stream(
        &self,
        ctx: ToolCallingContext,
        stream: PinBoxToolStreamParam,
    ) -> ToolResult<()> {
        let param_stream = stream.filter_map(|frame| {
            let res = serde_json::from_value::<T::Params>(ToolCall::from(frame).function.arguments);
            match res {
                Ok(param) => Some(param),
                // 如果发送回来的数据不够的话，先过滤掉
                Err(e) => {
                    eprintln!("call_tool_stream: {}", e);
                    None
                }
            }
        });
        self.call_stream(ctx, Box::pin(param_stream)).await
    }
    fn alias(&self) -> Option<HashSet<Cow<str>>> {
        self.alias()
    }

    fn schema(&self) -> Value {
        self.schema()
    }

    async fn definition(&self, ctx: ToolCallingContext) -> Vec<FunctionDefinition> {
        self.definition(ctx).await
    }
}

pub trait IntoDynTool {
    fn into_dyn(self) -> Arc<dyn DynTool>;
}

impl<T> IntoDynTool for T
where
    T: Tool + Sync + Send + 'static,
{
    fn into_dyn(self) -> Arc<dyn DynTool>
    where
        Self: Sized + Sync + Send + 'static,
    {
        Arc::new(self)
    }
}

impl IntoDynTool for Arc<dyn DynTool> {
    fn into_dyn(self) -> Arc<dyn DynTool> {
        self
    }
}
impl IntoDynTool for Box<dyn DynTool> {
    fn into_dyn(self) -> Arc<dyn DynTool> {
        Arc::from(self)
    }
}
