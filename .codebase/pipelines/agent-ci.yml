name: Agent CI
trigger:
  change:
    branches: [stable, integration_*, test_*]
    paths:
      - ".codebase/pipelines/agent-ci.yml"
      - "apps/icube_server_rs/**"
jobs:
  ci-check:
    ignore-error: true
    runs-on:
      env: boe
    name: Clippy
    image: hub.byted.org/compile/rust.compile.lyra:104d25a9d81d1f75f2652d9d71ac14b9
    envs:
      all_proxy: http://sys-proxy-rd-relay.byted.org:3128
      http_proxy: http://sys-proxy-rd-relay.byted.org:3128
      https_proxy: http://sys-proxy-rd-relay.byted.org:3128
      no_proxy: &no_proxy localhost,.byted.org,byted.org,.bytedance.net,bytedance.net,127.0.0.1,*********/8,***********/16,**********/10,**********/12,***********/16,10.0.0.0/8,::1,fe80::/10,fd00::/8,/var/run/zti-agent.sock
      RUST_BACKTRACE: full
    steps:
      - name: Shallow Clone
        uses: actions/checkout
        inputs:
          depth: 1
      - id: Get_Home
        name: Get Home Dir
        commands:
          - echo "::set-output name=home::$(echo ~)"
      - id: Checksum
        uses: actions/checksum@v1
        inputs:
          paths:
            - apps/icube_server_rs/modules/ai-agent/Cargo.toml
            - .codebase
      - id: Cache
        uses: actions/cache
        inputs:
          key: icube-rust-agent-clippy-v1-${{Steps.Checksum.Outputs.hash}}
          paths:
            - ${{Steps.Get_Home.Outputs.home}}/.cargo
            - ${{Steps.Get_Home.Outputs.home}}/.rustup
          restore_keys:
            - icube-rust-agent-clippy-v1-${{Steps.Checksum.Outputs.hash}}
      - name: Prepare
        commands:
          - . /etc/profile
          - echo "cargo version:"
          - cargo --version
          - echo "rustc version:"
          - rustc --version
      - name: CI
        commands:
          - cd apps/icube_server_rs/modules/ai-agent
          - export TTNET_LIB_DIR_PATH="$(pwd)/deps/ttnet/linux"
          - ../../../../scripts/ci/agent_ci_check.sh clippy

  build:
    runs-on:
      env: boe
    name: Build
    image: hub.byted.org/compile/rust.compile.lyra:104d25a9d81d1f75f2652d9d71ac14b9
    envs:
      all_proxy: http://sys-proxy-rd-relay.byted.org:3128
      http_proxy: http://sys-proxy-rd-relay.byted.org:3128
      https_proxy: http://sys-proxy-rd-relay.byted.org:3128
      no_proxy: &no_proxy localhost,.byted.org,byted.org,.bytedance.net,bytedance.net,127.0.0.1,*********/8,***********/16,**********/10,**********/12,***********/16,10.0.0.0/8,::1,fe80::/10,fd00::/8,/var/run/zti-agent.sock
      RUST_BACKTRACE: full
    steps:
      - name: Shallow Clone
        uses: actions/checkout
        inputs:
          depth: 1
      - id: Get_Home
        name: Get Home Dir
        commands:
          - echo "::set-output name=home::$(echo ~)"
      - id: Checksum
        uses: actions/checksum@v1
        inputs:
          paths:
            - apps/icube_server_rs/modules/ai-agent/Cargo.toml
            - .codebase
      - id: Cache
        uses: actions/cache
        inputs:
          key: icube-rust-agent-build-v1-${{Steps.Checksum.Outputs.hash}}
          paths:
            - ${{Steps.Get_Home.Outputs.home}}/.cargo
            - ${{Steps.Get_Home.Outputs.home}}/.rustup
          restore_keys:
            - icube-rust-agent-build-v1-${{Steps.Checksum.Outputs.hash}}
      - name: Prepare
        commands:
          - . /etc/profile
          - echo "cargo version:"
          - cargo --version
          - echo "rustc version:"
          - rustc --version
      - name: CI
        commands:
          - cd apps/icube_server_rs/modules/ai-agent
          - export TTNET_LIB_DIR_PATH="$(pwd)/deps/ttnet/linux"
          - ../../../../scripts/ci/agent_ci_check.sh build

  test-v3:
    runs-on:
      env: boe
    name: Test v3
    image: hub.byted.org/compile/rust.compile.lyra:104d25a9d81d1f75f2652d9d71ac14b9
    envs:
      all_proxy: http://sys-proxy-rd-relay.byted.org:3128
      http_proxy: http://sys-proxy-rd-relay.byted.org:3128
      https_proxy: http://sys-proxy-rd-relay.byted.org:3128
      no_proxy: &no_proxy localhost,.byted.org,byted.org,.bytedance.net,bytedance.net,127.0.0.1,*********/8,***********/16,**********/10,**********/12,***********/16,10.0.0.0/8,::1,fe80::/10,fd00::/8,/var/run/zti-agent.sock
      RUST_BACKTRACE: full
    steps:
      - name: Shallow Clone
        uses: actions/checkout
        inputs:
          depth: 1
      - id: Get_Home
        name: Get Home Dir
        commands:
          - echo "::set-output name=home::$(echo ~)"
      - id: Checksum
        uses: actions/checksum@v1
        inputs:
          paths:
            - apps/icube_server_rs/modules/ai-agent/Cargo.toml
            - .codebase
      - id: Cache
        uses: actions/cache
        inputs:
          key: icube-rust-agent-v1-test-v3-${{Steps.Checksum.Outputs.hash}}
          paths:
            - ${{Steps.Get_Home.Outputs.home}}/.cargo
            - ${{Steps.Get_Home.Outputs.home}}/.rustup
          restore_keys:
            - icube-rust-agent-v1-test-v3-${{Steps.Checksum.Outputs.hash}}
      - name: Prepare
        commands:
          - . /etc/profile
          - echo "cargo version:"
          - cargo --version
          - echo "rustc version:"
          - rustc --version
      - name: CI
        commands:
          - cd apps/icube_server_rs/modules/ai-agent
          - export TTNET_LIB_DIR_PATH="$(pwd)/deps/ttnet/linux"
          - ../../../../scripts/ci/agent_ci_check.sh test --package ai-agent --lib -- domain::agent_v3 infrastructure::adapter::slardar_tracing
  test-framework:
    runs-on:
      env: boe
    name: Test Framework
    image: hub.byted.org/compile/rust.compile.lyra:104d25a9d81d1f75f2652d9d71ac14b9
    envs:
      all_proxy: http://sys-proxy-rd-relay.byted.org:3128
      http_proxy: http://sys-proxy-rd-relay.byted.org:3128
      https_proxy: http://sys-proxy-rd-relay.byted.org:3128
      no_proxy: &no_proxy localhost,.byted.org,byted.org,.bytedance.net,bytedance.net,127.0.0.1,*********/8,***********/16,**********/10,**********/12,***********/16,10.0.0.0/8,::1,fe80::/10,fd00::/8,/var/run/zti-agent.sock
      RUST_BACKTRACE: full
    steps:
      - name: Shallow Clone
        uses: actions/checkout
        inputs:
          depth: 1
      - id: Get_Home
        name: Get Home Dir
        commands:
          - echo "::set-output name=home::$(echo ~)"
      - id: Checksum
        uses: actions/checksum@v1
        inputs:
          paths:
            - apps/icube_server_rs/crates/framework/Cargo.toml
            - .codebase
      - id: Cache
        uses: actions/cache
        inputs:
          key: icube-rust-agent-v1-test-framework-${{Steps.Checksum.Outputs.hash}}
          paths:
            - ${{Steps.Get_Home.Outputs.home}}/.cargo
            - ${{Steps.Get_Home.Outputs.home}}/.rustup
          restore_keys:
            - icube-rust-agent-v1-test-framework-${{Steps.Checksum.Outputs.hash}}
      - name: Prepare
        commands:
          - . /etc/profile
          - echo "cargo version:"
          - cargo --version
          - echo "rustc version:"
          - rustc --version
      - name: CI
        commands:
          - cd apps/icube_server_rs/crates/framework
          - ../../../../scripts/ci/agent_ci_check.sh test
  clippy-framework:
      runs-on:
        env: boe
      name: Clippy Framework
      image: hub.byted.org/compile/rust.compile.lyra:104d25a9d81d1f75f2652d9d71ac14b9
      envs:
        all_proxy: http://sys-proxy-rd-relay.byted.org:3128
        http_proxy: http://sys-proxy-rd-relay.byted.org:3128
        https_proxy: http://sys-proxy-rd-relay.byted.org:3128
        no_proxy: &no_proxy localhost,.byted.org,byted.org,.bytedance.net,bytedance.net,127.0.0.1,*********/8,***********/16,**********/10,**********/12,***********/16,10.0.0.0/8,::1,fe80::/10,fd00::/8,/var/run/zti-agent.sock
        RUST_BACKTRACE: full
      steps:
        - name: Shallow Clone
          uses: actions/checkout
          inputs:
            depth: 1
        - id: Get_Home
          name: Get Home Dir
          commands:
            - echo "::set-output name=home::$(echo ~)"
        - id: Checksum
          uses: actions/checksum@v1
          inputs:
            paths:
              - apps/icube_server_rs/crates/framework/Cargo.toml
              - .codebase
        - id: Cache
          uses: actions/cache
          inputs:
            key: icube-rust-agent-clippy-framework-v1-${{Steps.Checksum.Outputs.hash}}
            paths:
              - ${{Steps.Get_Home.Outputs.home}}/.cargo
              - ${{Steps.Get_Home.Outputs.home}}/.rustup
            restore_keys:
              - icube-rust-agent-clippy-framework-v1-${{Steps.Checksum.Outputs.hash}}
        - name: Prepare
          commands:
            - . /etc/profile
            - echo "cargo version:"
            - cargo --version
            - echo "rustc version:"
            - rustc --version
        - name: CI
          commands:
            - cd apps/icube_server_rs/crates/framework
            - ../../../../scripts/ci/agent_ci_check.sh clippy -- -D warnings
