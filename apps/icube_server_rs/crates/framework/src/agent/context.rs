use indexmap::IndexMap;
use serde_json::{json, Value};
use std::borrow::Cow;
use std::ops::Deref;
use std::sync::Arc;
use tokio::sync::mpsc::UnboundedSender;

use crate::core::agent::{Agent, DynRunTaskRequest, SessionContext};
use crate::core::error::AgentError;
use crate::core::event::Event;
use crate::core::llm::{LLMRunConfig, LLM};
use crate::core::message::{Message, MessageContents};
use crate::core::provider::model::ModelProvider;
use crate::core::task::{TaskControl, TaskHandle};
use crate::core::tool::{CallToolResponse, ToolCallingContext, ToolCallingContextBuilder};
use crate::graph::builtin_action::interrupt::UserAction;
use crate::history::{<PERSON>E<PERSON>ry, HistoryRecorder};
use crate::ioc::{IOCContainer, Injectable};
use crate::llm::error::LLMError;
use crate::prompt::{PromptFormatter, PromptProvider};
use crate::tool::tool::PinBoxToolStreamParam;
use crate::tool::tool_box::{BuildinAction, ToolDefinitionTag, ToolDefinitions, Toolbox};
use crate::FunctionDefinition;

#[derive(Clone, Debug, Default)]
pub struct SessionRef {
    task_id: String,
    session_id: String,
    message_id: String,
    user_message_id: String,
    agent_run_ids: Vec<String>,
}

/// Parameters for creating a CallingContext
pub struct CreateContextParams<Config> {
    pub session_ref: SessionRef,
    pub agent_ref: Arc<ReActAgentContext<Config>>,
    pub event_bus: Option<UnboundedSender<Event>>,
    pub container: Arc<IOCContainer>,
    pub config: Arc<Config>,
    pub toolbox: Option<Arc<Toolbox>>,
    pub ctrl: TaskControl,
    pub initial_message: Option<MessageContents>,
}

impl SessionRef {
    pub fn new(
        task_id: impl Into<String>,
        session_id: impl Into<String>,
        message_id: impl Into<String>,
        user_message_id: impl Into<String>,
        agent_run_ids: impl Into<Vec<String>>,
    ) -> Self {
        Self {
            task_id: task_id.into(),
            session_id: session_id.into(),
            message_id: message_id.into(),
            user_message_id: user_message_id.into(),
            agent_run_ids: agent_run_ids.into(),
        }
    }
}

struct ContextInner<Config> {
    session_ref: SessionRef,
    agent_ref: Arc<ReActAgentContext<Config>>,
    event_bus: Option<UnboundedSender<Event>>,
    container: Arc<IOCContainer>,
    config: Arc<Config>,
    #[allow(dead_code)]
    toolbox: Option<Arc<Toolbox>>,
    tool_definitions: Arc<ToolDefinitions>,
    ctrl: TaskControl,
    initial_message: Option<MessageContents>,
}

#[derive(Clone)]
pub struct CallingContext<Config> {
    inner: Arc<ContextInner<Config>>,
}

impl<C> ContextInner<C> {
    fn get_history_recorder(&self) -> Option<Arc<HistoryRecorder<C>>> {
        self.agent_ref.history_recorder.clone()
    }
}

impl<Config> Deref for CallingContext<Config> {
    type Target = ReActAgentContext<Config>;

    fn deref(&self) -> &Self::Target {
        self.inner.agent_ref.as_ref()
    }
}

// pub struct CallToolRequest {
//     tool_name: String,
//     params: Value,
// }
// impl CallToolRequest {
//     pub fn new(tool_name: String, params: Value) -> Self {
//         Self { tool_name, params }
//     }
// }

impl<Config: 'static> CallingContext<Config> {
    pub fn get_service<T: Injectable>(&self) -> &T {
        self.inner.container.get::<T>()
    }

    pub fn config(&self) -> &Config {
        self.inner.config.as_ref()
    }

    pub fn get_agent_name(&self) -> String {
        self.inner.agent_ref.name()
    }

    pub fn get_initial_message(&self) -> Option<&MessageContents> {
        self.inner.initial_message.as_ref()
    }

    pub async fn call_tool(
        &self,
        tool_name: String,
        args: Value,
    ) -> Result<CallToolResponse, AgentError> {
        let ctx = self.tool_calling_context_builder()
            .with_agent_run_id(self.agent_run_id())
            .with_agent_type(self.get_agent_name())
            .build();
        self.call_tool_with_context(tool_name, args, None, ctx)
            .await
    }

    pub async fn call_tool_stream(
        &self,
        tool_name: String,
        tool_stream: PinBoxToolStreamParam,
    ) -> Result<(), AgentError> {
        let ctx = self.tool_calling_context_builder().build();
        self.call_tool_stream_with_context(tool_name, tool_stream, ctx)
            .await
    }

    pub fn has_tool(&self, tool_name: &str) -> bool {
        // def 里有
        if self.inner.tool_definitions.has_tool(tool_name) {
            if let Ok(name) = self.inner.tool_definitions.get_tool_origin_name(tool_name) {
                // toolbox里也有
                // 比如 run_agent 这个 tool，def 里有，但是 toolbox 里没有
                if self.inner.toolbox.as_ref().unwrap().has_tool(name) {
                    return true;
                }
            }
        }
        false
    }

    pub async fn call_tool_stream_with_context(
        &self,
        received_tool_name: String,
        tool_stream: PinBoxToolStreamParam,
        ctx: ToolCallingContext,
    ) -> Result<(), AgentError> {
        let tool_name = self
            .inner
            .tool_definitions
            .get_tool_origin_name(&received_tool_name)?;
        let toolbox =
            self.inner.toolbox.as_ref().ok_or_else(|| {
                AgentError::InternalError("Toolbox is not initialized".to_string())
            })?;

        // let expected_keys = self
        //     .inner
        //     .tool_definitions
        //     .get(&received_tool_name)
        //     .and_then(|td| td.parameters.as_object())
        //     // 用 required 来校验，效果好点
        //     .and_then(|p| p.get("required")).map(|p| JSONUtil::new(p).string_arr_keys())
        //     .unwrap_or_default();

        // // 对流的数据进行过滤
        // // Fix JSON 问题：writeTofile params 里 path 没显示完整的时候，就被 fixjson 修复成完整的数据传给 tool_call_stream 去执行了，实际 path, content 都可能不完整，而且也不保证参数的顺序
        // let tool_stream = Box::pin(tool_stream.filter_map(move |frame| {
        //     let received_keys =  JSONUtil::new(&frame
        //         .function
        //         .arguments).obj_keys();

        //     let is_valid = JSONUtil::has_common_prefix(&expected_keys, &received_keys);

        //     if !is_valid {
        //         tracing::warn!(
        //             "Skipping frame due to parameter order mismatch. Expected keys: {:?}, Received keys: {:?}",
        //             &expected_keys,
        //             &received_keys
        //         );
        //         return None;
        //     }
        //     Some(frame)
        // }));
        toolbox
            .call_tool_stream(ctx, tool_name, tool_stream)
            .await
            .map_err(AgentError::from)
    }

    pub fn tool_calling_context_builder(&self) -> ToolCallingContextBuilder {
        ToolCallingContext::builder(self.inner.container.clone())
    }

    pub async fn call_tool_with_context(
        &self,
        tool_name: String,
        args: Value,
        error: Option<LLMError>,
        ctx: ToolCallingContext,
    ) -> Result<CallToolResponse, AgentError> {
        let tool_name = self
            .inner
            .tool_definitions
            .get_tool_origin_name(&tool_name)?;
        let toolbox =
            self.inner.toolbox.as_ref().ok_or_else(|| {
                AgentError::InternalError("Toolbox is not initialized".to_string())
            })?;

        toolbox
            .call_tool_with_error_handle(ctx, tool_name, args, error)
            .await
            .map_err(AgentError::from)
    }

    pub fn get_tool_definitions(&self) -> &ToolDefinitions {
        self.inner.tool_definitions.as_ref()
    }

    async fn init_tool_definitions(
        members: &IndexMap<String, Arc<dyn Agent>>,
        toolbox: Option<Arc<Toolbox>>,
        container: Arc<IOCContainer>,

    ) -> Result<ToolDefinitions, AgentError> {
        let mut all_tools = IndexMap::new();
        for member in members.values() {
            let name = format!("run_agent_{}", member.name());
            let mut td: crate::tool::tool_box::ToolDefinition = FunctionDefinition {
                name,
                description: member.description(),
                parameters: json!({
                    "type": "object",
                    "properties": {
                        "instruction": {
                            "type": "string",
                            "description": "the instruction you need to provide the corresponding Agent with, such as the goal to achieve and any important considerations. Please keep it as simple as possible.",
                        }
                    },
                    "required": ["instruction"]
                }),
            }
            .into();

            if let Some(ext_params) = member.ext_params() {
                if let (Some(ext_properties), Some(base_properties)) = (
                    ext_params.get("properties").and_then(|p| p.as_object()),
                    td.parameters
                        .get_mut("properties")
                        .and_then(|p| p.as_object_mut()),
                ) {
                    base_properties.clear();
                    for (key, value) in ext_properties {
                        base_properties.insert(key.clone(), value.clone());
                    }
                }

                if let (Some(ext_required), Some(base_required)) = (
                    ext_params.get("required").and_then(|r| r.as_array()),
                    td.parameters
                        .get_mut("required")
                        .and_then(|r| r.as_array_mut()),
                ) {
                    base_required.clear();
                    for req in ext_required {
                        if !base_required.contains(req) {
                            base_required.push(req.clone());
                        }
                    }
                }
            }

            td.tag = ToolDefinitionTag::BuildinAction(BuildinAction::ForwardAgent);
            all_tools.insert(td.name.clone(), td);
        }
        if let Some(toolbox) = toolbox {
            let ctx = ToolCallingContext::new(container.clone());
            let defs = toolbox
                .get_tool_definitions(ctx)
                .await
                .map_err(AgentError::from)?;
            all_tools.extend(defs);
        }
        Ok(ToolDefinitions::new(all_tools))
    }

    async fn append_message_to_recorder(
        &self,
        message: &Message,
        agent_type: &str,
    ) -> Result<(), AgentError> {
        let session_ref = &self.inner.session_ref;
        let agent_run_id = self.agent_run_id();
        let history_entry = HistoryEntry::new(message)
            .session_id(&session_ref.session_id)
            .message_id(&session_ref.message_id)
            .user_message_id(&session_ref.user_message_id)
            .agent_run_id(agent_run_id)
            .agent_type(agent_type);

        if let Some(history_recorder) = &self.inner.get_history_recorder() {
            history_recorder
                .append(history_entry, self.config())
                .await?;
        }
        Ok(())
    }

    pub async fn append_message_with_agent_type(
        &self,
        message: &Message,
        agent_type: String,
    ) -> Result<(), AgentError> {
        self.append_message_to_recorder(message, &agent_type).await
    }

    pub async fn append_message(&self, message: &Message) -> Result<(), AgentError> {
        let agent_name = self.inner.agent_ref.name();
        self.append_message_to_recorder(message, &agent_name).await
    }

    pub async fn load_messages(
        &self,
        token_limit: Option<usize>,
        message_ids: Vec<String>,
    ) -> Result<Vec<Message>, AgentError> {
        let Some(history_recorder) = self.inner.get_history_recorder() else {
            return Ok(Vec::new());
        };
        let session_ref = &self.inner.session_ref;
        let messages = history_recorder
            .load(
                &session_ref.session_id,
                token_limit,
                message_ids,
                self.config(),
                self.inner.event_bus.as_ref(),
            )
            .await?;
        Ok(messages)
    }

    pub async fn save_exception_message(
        &self,
        agent_error: &AgentError,
    ) -> Result<(), AgentError> {
        if let Some(history_recorder) = &self.inner.get_history_recorder() {
            history_recorder
                .save_exception_message(agent_error, self.config())
                .await?;
        }
        Ok(())
    }

    pub async fn compress_messages(&self) -> Result<bool, AgentError> {
        let Some(history_recorder) = self.inner.get_history_recorder() else {
            return Ok(false);
        };
        let session_ref = &self.inner.session_ref;
        let compressed = history_recorder
            .compress(
                &session_ref.session_id,
                self.config(),
            )
            .await?;
        Ok(compressed)
    }

    pub async fn update_compress_token_usage(&self, total_token: u32) -> Result<(), AgentError> {
        let Some(history_recorder) = self.inner.get_history_recorder() else {
            return Ok(());
        };
        let session_ref = &self.inner.session_ref;
        history_recorder
            .update_compress_token_usage(
                &session_ref.session_id,
                total_token,
                self.config(),
            )
            .await?;
        Ok(())
    }

    pub fn agent_run_id(&self) -> String {
        self.inner.session_ref.agent_run_ids.join("-")
    }

    pub fn gen_next_agent_run(&self) -> Vec<String> {
        let mut agent_run_ids = self.inner.session_ref.agent_run_ids.clone();
        let next_agent_run_id = bson::oid::ObjectId::new().to_string();
        agent_run_ids.push(next_agent_run_id);
        agent_run_ids
    }

    pub fn is_root_agent_run(&self) -> bool {
        self.inner.session_ref.agent_run_ids.len() == 1
    }
}

impl<Config: 'static> CallingContext<Config> {
    pub fn new(
        session_ref: SessionRef,
        agent_ref: Arc<ReActAgentContext<Config>>,
        event_bus: Option<UnboundedSender<Event>>,
        container: Arc<IOCContainer>,
        config: Arc<Config>,
        toolbox: Option<Arc<Toolbox>>,
    ) -> Self {
        Self {
            inner: Arc::new(ContextInner {
                session_ref,
                agent_ref,
                event_bus,
                container,
                config,
                toolbox,
                tool_definitions: Arc::new(ToolDefinitions::default()),
                ctrl: TaskControl::default(),
                initial_message: None,
            }),
        }
    }

    /// tool_defs 创建完成后在 ctx 上固化下来
    pub async fn create(params: CreateContextParams<Config>) -> Result<Self, AgentError> {
        let CreateContextParams {
            session_ref,
            agent_ref,
            event_bus,
            container,
            config,
            toolbox,
            ctrl,
            initial_message,
        } = params;
        let tool_defs =
            Self::init_tool_definitions(&agent_ref.agents, toolbox.clone(), container.clone())
                .await?;
        Ok(Self {
            inner: Arc::new(ContextInner {
                session_ref,
                agent_ref,
                event_bus,
                container,
                config,
                toolbox,
                tool_definitions: Arc::new(tool_defs),
                ctrl,
                initial_message,
            }),
        })
    }

    pub fn task_id(&self) -> &str {
        self.inner.session_ref.task_id.as_str()
    }

    pub fn emit_message(&self, event: Event) {
        if let Some(event_bus) = &self.inner.event_bus {
            let _ = event_bus.send(event);
        }
    }

    pub fn task_control(&self) -> &TaskControl {
        &self.inner.ctrl
    }

    pub async fn forward_to_agent(
        &self,
        agent_name: String,
        message: impl Into<DynRunTaskRequest>,
    ) -> Result<TaskHandle, AgentError> {
        let agent = self.get_agent_by_name(&agent_name)?;
        // 默认把 token 透传下去，确保子 agent可以停止
        let request: DynRunTaskRequest = message.into();
        let request = request
            .with_cancellation_token(self.task_control().child_token())
            .with_session_context(SessionContext {
                session_id: self.inner.session_ref.session_id.clone(),
                message_id: self.inner.session_ref.message_id.clone(),
                user_message_id: self.inner.session_ref.user_message_id.clone(),
            });

        agent.run_task_with_request(request).await
    }

    pub async fn resume_agent(
        &self,
        agent_name: String,
        session_id: String,
        user_action: UserAction,
    ) -> Result<TaskHandle, AgentError> {
        let agent = self.get_agent_by_name(&agent_name)?;
        agent.resume_task(&session_id, user_action).await
    }
}

#[derive(Clone)]
pub struct ReActAgentContext<C> {
    pub name: Cow<'static, str>,
    pub llm: Arc<dyn LLM<LLMConfig = LLMRunConfig>>,
    pub llm_provider: Arc<dyn ModelProvider>,
    pub prompt_provider: Arc<dyn PromptProvider>,
    pub toolbox: Option<Arc<Toolbox>>,
    pub agents: IndexMap<String, Arc<dyn Agent>>,
    pub service_registry: Arc<IOCContainer>,
    pub history_recorder: Option<Arc<HistoryRecorder<C>>>,
}

impl<C> ReActAgentContext<C> {
    pub fn name(&self) -> String {
        self.name.to_string()
    }

    pub fn llm(&self) -> Result<Arc<dyn LLM<LLMConfig = LLMRunConfig>>, AgentError> {
        Ok(self.llm.clone())
    }

    pub fn llm_provider(&self) -> Arc<dyn ModelProvider> {
        self.llm_provider.clone()
    }

    pub async fn system_prompt(&self) -> Result<Arc<dyn PromptFormatter>, AgentError> {
        self.prompt_provider
            .get_prompt_template(&self.name())
            .await
            .map_err(Into::into)
    }

    pub fn get_agent_by_name(&self, name: &str) -> Result<Arc<dyn Agent>, AgentError> {
        self.agents
            .get(name)
            .cloned()
            .ok_or_else(|| AgentError::AgentNotFound(name.to_string()))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::agent::test_util::{
            create_test_context, create_test_context_with_test_tool,
            create_test_context_with_toolbox, create_test_tool, create_toolbox_with_tools,
            TestContextBuilder,
        };
    
    use std::sync::Arc;

    /// 测试 has_tool 函数的各种场景
    #[tokio::test]
    async fn test_has_tool_scenarios() {
        // 场景1: 工具在 tool_definitions 和 toolbox 中都存在
        let context = create_test_context_with_test_tool::<()>("test_tool", "A test tool")
            .await
            .unwrap();

        assert!(
            context.has_tool("test_tool"),
            "工具应该存在于 definitions 和 toolbox 中"
        );

        // 场景2: 工具不存在于 tool_definitions 中
        let context = create_test_context::<()>();
        assert!(
            !context.has_tool("nonexistent_tool"),
            "不存在的工具应该返回 false"
        );

        // 场景3: 工具存在于 tool_definitions 但不存在于 toolbox 中
        // 这种情况需要手动构造
        let context = create_context_with_definition_only_tool().await;
        assert!(
            !context.has_tool("definition_only_tool"),
            "仅在 definitions 中存在的工具应该返回 false"
        );

        // 场景4: 测试空字符串工具名
        let context = create_test_context::<()>();
        assert!(!context.has_tool(""), "空字符串工具名应该返回 false");

        // 场景5: 测试多个工具的情况
        let tools = vec![
            create_test_tool("tool1", "First tool"),
            create_test_tool("tool2", "Second tool"),
            create_test_tool("tool3", "Third tool"),
        ];
        let toolbox = create_toolbox_with_tools(tools).unwrap();
        let context: CallingContext<()> = create_test_context_with_toolbox(toolbox).await.unwrap();

        assert!(context.has_tool("tool1"), "tool1 应该存在");
        assert!(context.has_tool("tool2"), "tool2 应该存在");
        assert!(context.has_tool("tool3"), "tool3 应该存在");
        assert!(!context.has_tool("tool4"), "tool4 不应该存在");
    }

    /// 测试 has_tool 函数的边界情况
    #[tokio::test]
    async fn test_has_tool_edge_cases() {
        // 测试工具名大小写敏感性
        let context = create_test_context_with_test_tool::<()>("TestTool", "A test tool")
            .await
            .unwrap();

        assert!(context.has_tool("TestTool"), "精确匹配的工具名应该存在");
        assert!(
            !context.has_tool("testtool"),
            "大小写不匹配的工具名应该不存在"
        );
        assert!(
            !context.has_tool("TESTTOOL"),
            "大小写不匹配的工具名应该不存在"
        );

        // 测试包含特殊字符的工具名
        let context =
            create_test_context_with_test_tool::<()>("test-tool_123", "Special char tool")
                .await
                .unwrap();

        assert!(
            context.has_tool("test-tool_123"),
            "包含特殊字符的工具名应该正常工作"
        );

        // 测试很长的工具名
        let long_name = "a".repeat(100);
        let context = create_test_context_with_test_tool::<()>(&long_name, "Long name tool")
            .await
            .unwrap();

        assert!(context.has_tool(&long_name), "长工具名应该正常工作");
    }

    /// 创建一个只在 tool_definitions 中存在但不在 toolbox 中存在的工具的上下文
    /// 这用于测试 has_tool 函数的逻辑：需要同时在 definitions 和 toolbox 中存在
    async fn create_context_with_definition_only_tool() -> CallingContext<()> {
        // 由于无法直接操作 tool_definitions，我们创建一个空的 toolbox
        // 这样可以模拟工具定义存在但 toolbox 中没有对应工具的情况
        let toolbox = Arc::new(Toolbox::new());

        // 使用现有的构建器创建上下文
        TestContextBuilder::<()>::new()
            .with_toolbox(toolbox)
            .build_async()
            .await
            .unwrap()
    }

    /// 测试 has_tool 函数在 toolbox 为 None 的情况下的行为
    #[tokio::test]
    async fn test_has_tool_with_none_toolbox() {
        // 创建没有 toolbox 的上下文
        let context = TestContextBuilder::<()>::new().build();

        // 当 toolbox 为 None 时，has_tool 应该返回 false
        // 注意：这个测试可能会 panic，因为代码中有 unwrap()
        // 这实际上暴露了代码中的一个潜在问题

        // 由于 UnwindSafe 的问题，我们直接测试而不使用 catch_unwind
        // 如果这里 panic，说明 has_tool 函数需要更好的错误处理
        let result = context.has_tool("any_tool");

        // 根据当前实现，这应该会 panic 或返回 false
        // 我们期望它不会 panic 并返回 false
        assert!(!result, "当 toolbox 为 None 时应该返回 false 而不是 panic");
    }
}
