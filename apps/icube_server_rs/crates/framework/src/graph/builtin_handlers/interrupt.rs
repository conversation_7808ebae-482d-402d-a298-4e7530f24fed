use std::borrow::Cow;
use crate::core::{error::Agent<PERSON>rror, handler::<PERSON><PERSON><PERSON><PERSON>};
use crate::graph::builtin_action::interrupt::Interrupt;
use async_trait::async_trait;
use serde::Serialize;

pub(crate) struct AsInterrupt<H>(pub(crate) H);

#[async_trait]
pub trait InterruptHandler<S, C>: Send + Sync {
    type Outgoing: Serialize + Send + Sync + 'static;

    async fn on_resume(
        &self,
        ctx: C,
        state: &mut S,
        interrupt: Interrupt,
    ) -> Result<Self::Outgoing, AgentError>;
}

#[async_trait]
impl<S, C, H> StageHandler<S, C> for AsInterrupt<H>
where
    H: InterruptHandler<S, C> + Send + Sync,
    S: Send + Sync,
    C: Send + Sync + 'static,
    H::Outgoing: Serialize + Send + Sync,
{
    type Incoming = Interrupt;
    type Outgoing = H::Outgoing;

    fn name(&self) -> Cow<'static, str> {
        "__INTERRUPT__".into()
    }

    async fn handle(
        &self,
        ctx: C,
        state: &mut S,
        input: Interrupt,
    ) -> Result<Self::Outgoing, AgentError> {
        self.0.on_resume(ctx, state, input).await
    }
}
