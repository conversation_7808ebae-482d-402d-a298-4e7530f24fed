use std::borrow::Cow;

use crate::core::{error::AgentError, handler::StageHandler};
use serde_json::Value;

#[derive(Debug)]
pub struct GraphStartHandler;

#[async_trait::async_trait]
impl<GS, C: Send + Sync + 'static> StageHandler<GS, C> for GraphStartHandler {
    type Incoming = Value;
    type Outgoing = Value;

    fn name(&self) -> Cow<'static, str> {
        "__START__".into()
    }

    async fn handle(
        &self,
        _ctx: C,
        _state: &mut GS,
        _input: Self::Incoming,
    ) -> Result<Self::Outgoing, AgentError> {
        // GraphStartHandler should pass through the input as-is
        // The actual type conversion will be handled by the downcasting logic
        Ok(_input)
    }
}
