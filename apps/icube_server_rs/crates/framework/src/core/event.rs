use std::sync::Arc;

use super::{artifact::Artifacts, error::AgentError, message::Message};
use crate::core::llm::{ContextTokenUsageEvent, LLMFeeUsageEvent, LLMRequestWaitInQueueEvent};
use crate::graph::builtin_action::interrupt::Interrupt;
use serde::{Deserialize, Serialize};
use serde_json::Value;

#[derive(Debug, <PERSON><PERSON>, Default)]
pub struct ProcessedOutput {
    pub history: Vec<Message>,
    pub payload: Value,
    pub artifacts: Artifacts,
    pub summary: String,
    pub turns: u32,
}

#[derive(Debug, Clone)]
pub enum Event {
    NewQueryReceived {
        query: String,
    },
    AskUser {
        question: String,
    },
    HumanInputReceived {
        response: String,
        original_context_id: String,
    },
    WaitingForUserReply,
    ProcessingComplete {
        output: ProcessedOutput,
    },
    ResponseGenerated {
        agent_name: String,
        agent_run_id: String,
        response_text: String,
    },
    ToolcallUpdate(ToolcallUpdateEvent),
    ResponseSentSuccessfully {
        original_context_id: String,
    },
    <PERSON>rrorOccurred {
        error: AgentError,
        turns: u32,
    },
    ShutdownAgent,
    Interrupt(Box<Interrupt>),
    Queuing(LLMRequestWaitInQueueEvent),
    FeeUsage(LLMFeeUsageEvent),
    ContextTokenUsage(ContextTokenUsageEvent),
    Compressing(bool),
}

impl From<LLMRequestWaitInQueueEvent> for Event {
    fn from(event: LLMRequestWaitInQueueEvent) -> Self {
        Self::Queuing(event)
    }
}

impl From<LLMFeeUsageEvent> for Event {
    fn from(event: LLMFeeUsageEvent) -> Self {
        Self::FeeUsage(event)
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ToolcallUpdateEvent {
    pub id: String,
    pub name: String,
    pub agent_name: Option<String>,
    pub agent_run_id: Option<String>,
    pub thought: Option<String>,
    pub params: Option<Arc<Value>>,
    pub state: ToolCallState,
    pub meta: Option<Arc<Value>>,
    pub need_confirm: bool,
    pub model_toolcall_id: String,
}
impl ToolcallUpdateEvent {
    pub fn params(&self) -> Option<&serde_json::Value> {
        self.params.as_ref().map(|p| p.as_ref())
    }

    pub fn params_default(&self) -> serde_json::Value {
        self.params
            .as_ref()
            .map(|p| p.as_ref().clone())
            .unwrap_or_default()
    }

    pub fn meta(&self) -> Option<&serde_json::Value> {
        self.meta.as_ref().map(|m| m.as_ref())
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ToolCallState {
    // 确认阶段
    PendingConfirmation,
    Confirmed,
    Skipped,

    // 执行阶段
    // 流式处理
    Streaming,
    Running,
    Completed(ToolCallResult),
    Failed(String),
}
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ToolCallResult {
    Success(Arc<serde_json::Value>),
    PartialSuccess(Arc<serde_json::Value>),
}
impl ToolCallResult {
    pub fn new_success(v: serde_json::Value) -> Self {
        Self::Success(Arc::new(v))
    }
    pub fn results(&self) -> &serde_json::Value {
        match self {
            ToolCallResult::Success(v) => v,
            ToolCallResult::PartialSuccess(v) => v,
        }
    }
}

impl ToolCallState {
    pub fn mut_if_completed(&mut self, result: ToolCallResult) {
        if let ToolCallState::Completed(r) = self {
            *r = result;
        }
    }
    pub fn mut_if_failed(&mut self, err: String) {
        if let ToolCallState::Failed(error_message) = self {
            *error_message = err;
        }
    }

    // 状态查询方法
    pub fn is_pending_confirmation(&self) -> bool {
        matches!(self, ToolCallState::PendingConfirmation)
    }

    pub fn is_confirmed(&self) -> bool {
        matches!(self, ToolCallState::Confirmed)
    }

    pub fn is_executing(&self) -> bool {
        matches!(self, ToolCallState::Running)
    }

    pub fn is_finished(&self) -> bool {
        matches!(
            self,
            ToolCallState::Completed(_) | ToolCallState::Failed(_) | ToolCallState::Skipped
        )
    }

    pub fn toolmessage_render_status(&self) -> String {
        match self {
            ToolCallState::Completed(_) => "done",
            ToolCallState::Running => "running",
            ToolCallState::Failed(_) => "done",
            ToolCallState::Skipped => "skipped",
            ToolCallState::PendingConfirmation => "running",
            ToolCallState::Confirmed => "running",
            ToolCallState::Streaming => "running",
        }
        .to_string()
    }
}
