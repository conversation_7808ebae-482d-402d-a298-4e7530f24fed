use crate::core::llm::{<PERSON>yn<PERSON><PERSON>, Dyn<PERSON><PERSON><PERSON>rapper, LLM};
use crate::tool::tool_box::ToolDefinitions;
use serde::{Deserialize, Serialize};
use std::fmt::{Display, Formatter};
use std::sync::Arc;
use thiserror::Error;

pub type LanguageModel = Arc<dyn DynLLM>;

pub trait IntoLanguageModel {
    fn into_model(self) -> LanguageModel;
}

impl<T: LLM> IntoLanguageModel for T {
    fn into_model(self) -> LanguageModel {
        Arc::new(DynLLMWrapper(self))
    }
}

impl<T> IntoLanguageModel for Arc<T>
where
    T: LLM + Clone,
{
    fn into_model(self) -> LanguageModel {
        Arc::new(DynLLMWrapper((*self).clone()))
    }
}

#[derive(Eq, Hash, PartialEq, Clone, Debug)]
pub enum LLMProvider {
    OpenRoute,
    OpenAI,
    Deepseek,
    LLMRawChat,
}

impl Display for <PERSON><PERSON><PERSON>ider {
    fn fmt(&self, f: &mut Formatter<'_>) -> std::fmt::Result {
        match self {
            LLMProvider::OpenRoute => write!(f, "open-router"),
            LLMProvider::OpenAI => write!(f, "openai"),
            LLMProvider::Deepseek => write!(f, "deepseek"),
            LLMProvider::LLMRawChat => write!(f, "llmrawchat"),
        }
    }
}

impl From<LLMProvider> for String {
    fn from(val: LLMProvider) -> Self {
        val.to_string()
    }
}

#[derive(Clone, Error, Debug, Serialize, Deserialize)]
pub enum LLMProviderError {
    #[error("provider {0} not found")]
    NotFound(String),

    #[error("provider {0} already exists")]
    AlreadyExists(String),
}
#[derive(Debug)]
pub struct LLMProviderConfig {
    #[allow(dead_code)]
    provider: LLMProvider,
    tool_definitions: ToolDefinitions,
}

impl LLMProviderConfig {
    pub fn new(provider: LLMProvider) -> Self {
        Self {
            provider,
            tool_definitions: ToolDefinitions::default(),
        }
    }
    pub fn with_tool_definitions(mut self, defs: ToolDefinitions) -> Self {
        self.tool_definitions = defs;
        self
    }
}
#[cfg_attr(any(test, feature = "testing"), mockall::automock)]
pub trait ModelProvider: Sync + Send + 'static {
    fn get_llm_model(&self, provider: &LLMProvider) -> Result<LanguageModel, LLMProviderError>;
}

#[cfg(test)]
mod tests {
    use crate::core::llm::{LLMStreamData, LLM};
    use crate::core::message::Message;
    use crate::core::provider::model::{
        IntoLanguageModel, LLMProvider, LLMProviderError, LanguageModel, ModelProvider,
    };
    use crate::language_models::deepseek::Deepseek;
    use crate::llm::error::LLMError;
    use crate::llm::GenerateResult;

    use async_trait::async_trait;
    use futures::Stream;
    use std::collections::hash_map::Entry;
    use std::collections::HashMap;
    use std::pin::Pin;
    use std::sync::Arc;

    #[derive(Default)]
    struct TestModelProvider {
        registry: HashMap<LLMProvider, LanguageModel>,
    }

    impl TestModelProvider {
        fn new() -> Self {
            Self::default()
        }

        /// Registers a model with the provider.
        fn register_model<M: IntoLanguageModel>(
            &mut self,
            provider: LLMProvider,
            model: M,
        ) -> Result<(), LLMProviderError> {
            match self.registry.entry(provider.clone()) {
                Entry::Occupied(_) => Err(LLMProviderError::NotFound(provider.to_string())),
                Entry::Vacant(entry) => {
                    entry.insert(model.into_model());
                    Ok(())
                }
            }
        }
    }

    impl ModelProvider for TestModelProvider {
        fn get_llm_model(&self, provider: &LLMProvider) -> Result<LanguageModel, LLMProviderError> {
            self.registry
                .get(provider)
                .cloned()
                .ok_or_else(|| LLMProviderError::NotFound(provider.to_string()))
        }
    }

    #[derive(Clone)]
    struct OpenAI;

    #[async_trait]
    impl LLM for OpenAI {
        type LLMConfig = ();

        fn name(&self) -> &'static str {
            "openai"
        }

        async fn generate(&self, _messages: &[Message]) -> Result<GenerateResult, LLMError> {
            todo!()
        }

        async fn stream(
            &self,
            _messages: &[Message],
        ) -> Result<Pin<Box<dyn Stream<Item = Result<LLMStreamData, LLMError>> + Send>>, LLMError>
        {
            todo!()
        }
    }

    #[test]
    fn test_model_provider() -> Result<(), LLMProviderError> {
        let mut provider = TestModelProvider::new();
        let openai_model = Arc::new(OpenAI);
        let deepseek_model = Deepseek::default();

        provider.register_model(LLMProvider::Deepseek, deepseek_model)?;
        provider.register_model(LLMProvider::OpenAI, openai_model)?;

        let retrieved_model = provider.get_llm_model(&LLMProvider::Deepseek)?;
        assert_eq!(retrieved_model.name(), "deepseek");
        Ok(())
    }
}
