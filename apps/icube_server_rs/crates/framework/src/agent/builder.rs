use super::context::CallingContext;
use super::react_agent::ReActAgent;
use super::state::RunState;
use crate::core::agent::Agent;
use crate::core::checkpoint::CheckPointer;
use crate::core::error::AgentError;
use crate::core::event::ProcessedOutput;
use crate::core::llm::{LLMRunConfig, LLM};
use crate::core::provider::model::ModelProvider;
use crate::graph::{CompiledGraph, GraphError};
use crate::history::HistoryStore;
use crate::ioc::ioc::{IOCContainer, Injectable};
use crate::llm::options::CallOptions;
use crate::prompt::PromptProvider;
use crate::schemas::FunctionDefinition;
use crate::tool::tool_box::Toolbox;
use serde_json::Value;
use std::borrow::Cow;
use std::future::Future;
use std::pin::Pin;
use std::sync::Arc;
use tokio_util::sync::CancellationToken;

pub trait GraphBuilder<S, Config> {
    fn build_graph(
        self,
        name: Option<Cow<'static, str>>,
    ) -> Result<CompiledGraph<S, CallingContext<Config>>, GraphError>;
}
pub type PinBoxFuture<O> = Pin<Box<dyn Future<Output = O> + Send + Sync + 'static>>;
pub type ArcOutputMapping<S> =
    Arc<dyn Fn(Value, RunState<S>) -> PinBoxFuture<ProcessedOutput> + Send + Sync + 'static>;

pub struct AgentBuilder<L, GB, S, Config, LP, PP> {
    name: Option<Cow<'static, str>>,
    description: Option<String>,
    ext_params: Option<serde_json::Value>,
    handle_factory: Option<GB>,
    tool_box: Option<Arc<Toolbox>>,
    llm: Option<L>,
    llm_provider: Option<LP>,
    prompt_provider: Option<PP>,
    check_pointer: Option<Arc<dyn CheckPointer<S, Config>>>,
    members: Vec<Arc<dyn Agent>>,
    history_store: Option<Arc<dyn HistoryStore<Config>>>,
    container: IOCContainer,
    output_mapping: Option<ArcOutputMapping<S>>,
}

impl<L, GB, S, Config, LP, PP> Default for AgentBuilder<L, GB, S, Config, LP, PP>
where
    L: LLM<LLMConfig = LLMRunConfig>,
    GB: GraphBuilder<RunState<S>, Config>,
    LP: ModelProvider,
    PP: PromptProvider,
{
    fn default() -> Self {
        Self::new()
    }
}
pub trait IntoOutputMappingHandler<S> {
    fn into_handler(self) -> ArcOutputMapping<S>;
}

impl<F, Fut, S> IntoOutputMappingHandler<S> for F
where
    F: Fn(Value, RunState<S>) -> Fut + Send + Sync + 'static,
    Fut: Future<Output = ProcessedOutput> + Send + Sync + 'static,
{
    fn into_handler(self) -> ArcOutputMapping<S> {
        Arc::new(move |v, s| Box::pin(self(v, s)))
    }
}

impl<L, GB, S, Config, LP, PP> AgentBuilder<L, GB, S, Config, LP, PP>
where
    L: LLM<LLMConfig = LLMRunConfig>,
    GB: GraphBuilder<RunState<S>, Config>,
    LP: ModelProvider,
    PP: PromptProvider,
{
    pub fn new() -> Self {
        AgentBuilder {
            name: None,
            description: None,
            ext_params: None,
            handle_factory: None,
            tool_box: None,
            llm: None,
            llm_provider: None,
            prompt_provider: None,
            check_pointer: None,
            members: Vec::new(),
            container: IOCContainer::default(),
            output_mapping: None,
            history_store: None,
        }
    }

    pub fn register_service<T: Injectable>(mut self, service: T) -> Self {
        self.container.register::<T>(service);
        self
    }

    pub fn with_name<N: Into<Cow<'static, str>>>(mut self, name: N) -> Self {
        self.name = Some(name.into());
        self
    }

    pub fn with_description<N: Into<String>>(mut self, name: N) -> Self {
        self.description = Some(name.into());
        self
    }

    pub fn with_ext_params(mut self, ext_params: serde_json::Value) -> Self {
        self.ext_params = Some(ext_params);
        self
    }

    pub fn with_llm(mut self, llm: L) -> Self {
        self.llm = Some(llm);
        self
    }

    pub fn with_llm_provider(mut self, llm: LP) -> Self {
        self.llm_provider = Some(llm);
        self
    }

    pub fn with_prompt_provider(mut self, provider: PP) -> Self {
        self.prompt_provider = Some(provider);
        self
    }

    pub fn with_check_pointer<C>(mut self, ck: C) -> Self
    where
        C: CheckPointer<S, Config>,
    {
        self.check_pointer = Some(Arc::new(ck));
        self
    }

    pub fn with_handle_factory(mut self, f: GB) -> Self {
        self.handle_factory = Some(f);
        self
    }

    pub fn with_tool_box(mut self, tool_box: Arc<Toolbox>) -> Self {
        self.tool_box = Some(tool_box);
        self
    }

    pub fn with_members(mut self, members: Vec<Arc<dyn Agent>>) -> Self {
        self.members = members;
        self
    }

    pub fn with_history_store<HS>(mut self, store: HS) -> Self
    where
        HS: HistoryStore<Config>,
    {
        self.history_store = Some(Arc::new(store));
        self
    }

    pub fn with_output_mapping<H>(mut self, handler: H) -> Self
    where
        H: IntoOutputMappingHandler<S>,
    {
        self.output_mapping = Some(handler.into_handler());
        self
    }

    pub fn build(self) -> Result<Arc<ReActAgent<S, Config>>, AgentError> {
        let mut all_tools = Vec::new();
        if let Some(tool_box) = &self.tool_box {
            let func_def = tool_box
                .list_tools()
                .iter()
                .map(|t| {
                    let def = FunctionDefinition {
                        name: t.name().to_string(),
                        description: t.description().to_string(),
                        parameters: t.schema(),
                    };
                    def
                })
                .collect::<Vec<_>>();
            all_tools.extend_from_slice(&func_def);
        }

        let mut llm = self.llm.expect("llm not set for AgentBuilder");
        llm.with_options(CallOptions::new().with_functions(all_tools));

        let members = self.members.into_iter().collect();

        let check_pointer = self
            .check_pointer
            .ok_or_else(|| AgentError::InternalError("failed to get check_pointer".to_string()))?;

        let graph = self
            .handle_factory
            .map(|g| g.build_graph(self.name.clone()))
            .transpose()
            .map_err(AgentError::BuildGraphError)?;

        let llm_provider = self.llm_provider.ok_or_else(|| {
            AgentError::InternalError("llm provider not set for agent".to_string())
        })?;

        let prompt_provider = self.prompt_provider.ok_or_else(|| {
            AgentError::InternalError("prompt provider not set for agent".to_string())
        })?;

        // if let Some(ref g) = graph {
        //     match g.write_to_dot_file() {
        //         Ok(path) => {
        //             println!("Graph written to file: {:?}", path);
        //         }
        //         Err(e) => eprintln!("Failed to write graph to file: {}", e),
        //     }
        // }

        // TODO: maybe not good to use agent to make handlers?
        // TODO: use a AgentContext that wrap all the service and ?
        let agent = ReActAgent {
            name: self.name.unwrap_or_else(|| Cow::from("default")),
            description: self.description.unwrap_or_default(),
            ext_params: self.ext_params,
            graph: graph.map(Arc::new),
            cancellation_token: CancellationToken::new(),
            llm: Arc::new(llm),
            llm_provider: Arc::new(llm_provider),
            prompt_provider: Arc::new(prompt_provider),
            check_pointer,
            toolbox: self.tool_box,
            members,
            output_mapping: self.output_mapping,
            container: Arc::new(self.container),
            history_store: self.history_store,
        };
        Ok(Arc::new(agent))
    }
}
