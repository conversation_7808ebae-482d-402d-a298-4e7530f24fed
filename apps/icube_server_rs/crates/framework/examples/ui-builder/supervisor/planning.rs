use crate::supervisor::util::ToolDefinitionUtil;
use crate::supervisor::{CallAgent, CallTool};
use crate::utils::formatter::format_tool_call;
use async_trait::async_trait;
use framework::core::{
    error::AgentError,
    event::Event,
    handler::StageHandler,
    llm::{LLMGenerateResult, LLMRunConfig},
    message::Message,
};
use framework::prompt_args;
use framework::tool::tool_box::ToolDefinition;
use framework::FunctionDefinition;
use serde_json::json;

use super::PlanningDecision;
use crate::{Context, SupervisorState};

#[derive(Debug)]
pub struct PlanningStateHandler;

#[async_trait]
impl StageHandler<SupervisorState, Context> for PlanningStateHandler {
    type Incoming = ();
    type Outgoing = PlanningDecision;

    async fn handle(
        &self,
        ctx: Context,
        state: &mut SupervisorState,
        _input: Self::Incoming,
    ) -> Result<Self::Outgoing, AgentError> {
        let llm = ctx.llm()?;

        let prompt = ctx.system_prompt().await?;
        let render_result = prompt.render(prompt_args! {}).await?;

        let mut messages = Vec::new();
        if !render_result.system_prompt.is_empty() {
            messages.push(Message::new_system_message(render_result.system_prompt));
        }

        if let Some(initial_message) = ctx.get_initial_message() {
            state.add_message(Message::new_human_message(initial_message.clone()));
        }

        messages.extend_from_slice(state.messages());

        state.turns += 1;

        let mut tool_defs = ctx.get_tool_definitions().clone();
        let finish_work_def: ToolDefinition = FunctionDefinition {
            name: "finish_work".into(),
            description: "when you finish work, you should call this method".to_string(),
            parameters: json!({
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "name of the agent",
                    }
                },
                "required": ["location"]
            }),
        }
        .into();
        tool_defs.insert("finish_work".to_string(), finish_work_def);

        ToolDefinitionUtil::append_thought_to_tool_definitions(&mut tool_defs);

        let llm_run_config = LLMRunConfig::default().with_tool_definitions(tool_defs);

        let resp = llm
            .generate_with_config(&messages, &llm_run_config)
            .await
            .map_err(|e| AgentError::InternalError(e.to_string()))?;

        // ctx.emit_message(Event::ResponseGenerated {
        //     agent_name: ctx.name(),
        //     response_text: resp.generation().into(),
        // });
        let msg = Message::new_ai_message(resp.generation().into())
            .with_tool_calls(resp.tool_calls().map(|calls| calls.to_vec()));
        state.add_message(msg);

        if let Some(tool_calls) = resp.tool_calls() {
            if let Some(call) = tool_calls.iter().next() {
                if call.function.name.starts_with("run_agent_") {
                    let agent_name = call
                        .function
                        .name
                        .strip_prefix("run_agent_")
                        .unwrap_or("unknown_agent");

                    // ctx.emit_message(Event::ResponseGenerated {
                    //     agent_name: ctx.name(),
                    //     response_text: format!("forward query to agent {}", agent_name),
                    // });

                    return Ok(PlanningDecision {
                        call_agent: Some(CallAgent {
                            agent_name: agent_name.to_string(),
                            tool_call: call.clone(),
                            resume: false,
                            task_id: None,
                            user_action: None,
                        }),
                        call_tool: None,
                    });
                }
                ctx.emit_message(Event::ResponseGenerated {
                    agent_name: ctx.name(),
                    agent_run_id: ctx.agent_run_id(),
                    response_text: format_tool_call(call),
                });
                return Ok(PlanningDecision {
                    call_agent: None,
                    call_tool: Some(CallTool {
                        tool_call: call.clone(),
                    }),
                });
            }
        }
        Ok(PlanningDecision::default())
    }
}
