use crate::supervisor::{CallTool, ResumeAction};
use async_trait::async_trait;
use framework::core::error::AgentError;
use framework::core::message::Message;
use framework::graph::builtin_action::interrupt::Decision;
use framework::graph::builtin_action::interrupt::Interrupt;
use framework::graph::builtin_action::interrupt::InterruptReason::AskUser;
use framework::graph::InterruptHandler;

use crate::{Context, SubAgentState};

#[derive(Debug)]
pub struct ResumeHandler;

#[async_trait]
impl InterruptHandler<SubAgentState, Context> for ResumeHandler {
    type Outgoing = ResumeAction;

    async fn on_resume(
        &self,
        ctx: Context,
        state: &mut SubAgentState,
        interrupt: Interrupt,
    ) -> Result<Self::Outgoing, AgentError> {
        match &interrupt.reason {
            AskUser(question) => {
                let answer = interrupt.get_answer();
                // ctx.emit_message(Event::ResponseGenerated {
                //     agent_name: ctx.name(),
                //     response_text: format!(
                //         "receive user reply: {:?}, decision {:?}",
                //         answer.message, answer.decision
                //     ),
                // });

                match answer.decision {
                    Decision::Approve => {
                        if let Some(payload) = &question.payload {
                            if let Ok(message) = serde_json::from_value::<Message>(payload.clone())
                            {
                                state.add_message(message);
                            }
                        }
                    }
                    Decision::Reject => {
                        if let Some(user_message) = &answer.message {
                            state.add_message(Message::new_human_message(user_message.clone()));
                        }

                        return Ok(ResumeAction::RePlan);
                    }
                }

                Ok(ResumeAction::CallTool(CallTool {
                    tool_call: interrupt.trigger_tool_calls[0].clone(),
                }))
            }
            _ => {
                eprintln!("Unknown interrupt reason: {:?}", interrupt.reason);
                Err(AgentError::InternalError(format!(
                    "Unknown interrupt reason: {:?}",
                    interrupt.reason
                )))
            }
        }
    }
}
