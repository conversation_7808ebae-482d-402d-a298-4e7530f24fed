use async_openai::error::OpenAIError;
use async_openai::types::{
    ChatCompletionMessageToolCall, ChatCompletionRequestAssistantMessageArgs,
    ChatCompletionRequestMessage, ChatCompletionRequestSystemMessageArgs,
    ChatCompletionRequestToolMessageArgs, ChatCompletionRequestUserMessageArgs,
    ChatCompletionStreamOptions, ChatCompletionToolChoiceOption, CreateChatCompletionRequest,
    CreateChatCompletionRequestArgs, ResponseFormat,
};
use async_trait::async_trait;
use futures::{Stream, StreamExt};
use jr::repair_json_object_v2;
use reqwest::Client;
use serde_json::Value;
use std::{collections::HashMap, pin::Pin, str};

use super::models::ApiResponse;
use super::DeepseekError;
use crate::core::llm::{LLMRunConfig, LLMStreamData, LLM};
use crate::core::message::{FunctionCall, Message, ToolCall};
use crate::llm::error::LLMError;
use crate::llm::options::CallOptions;
use crate::llm::{GenerateResult, TokenUsage};
use crate::schemas::convert::{LangchainIntoOpenAI, TryLangchainIntoOpenAI};
use crate::FunctionDefinition;

use super::models::{DeepseekMessage, Payload};

#[derive(Debug, Clone)]
struct ToolCallAccumulator {
    id: Option<String>,
    name: Option<String>,
    arguments: String,
}

pub enum DeepseekModel {
    DeepseekChat,
    DeepseekReasoner,
}

impl std::fmt::Display for DeepseekModel {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DeepseekModel::DeepseekChat => write!(f, "deepseek-chat"),
            DeepseekModel::DeepseekReasoner => write!(f, "deepseek-reasoner"),
        }
    }
}

#[derive(Clone)]
pub struct Deepseek {
    model: String,
    options: CallOptions,
    api_key: String,
    base_url: String,
    json_mode: bool,
    include_reasoning: bool,
}

impl Default for Deepseek {
    fn default() -> Self {
        Self::new()
    }
}

impl Deepseek {
    pub fn new() -> Self {
        Self {
            model: DeepseekModel::DeepseekChat.to_string(),
            options: CallOptions::default(),
            api_key: std::env::var("DEEPSEEK_API_KEY").unwrap_or_default(),
            base_url: "https://api.deepseek.com".to_string(),
            json_mode: false,
            include_reasoning: false,
        }
    }

    pub fn with_model<S: Into<String>>(mut self, model: S) -> Self {
        self.model = model.into();
        self
    }

    pub fn with_options(mut self, options: CallOptions) -> Self {
        self.options = options;
        self
    }

    pub fn with_api_key<S: Into<String>>(mut self, api_key: S) -> Self {
        self.api_key = api_key.into();
        self
    }

    pub fn with_base_url<S: Into<String>>(mut self, base_url: S) -> Self {
        self.base_url = base_url.into();
        self
    }

    pub fn with_json_mode(mut self, json_mode: bool) -> Self {
        self.json_mode = json_mode;
        self
    }

    pub fn with_include_reasoning(mut self, include_reasoning: bool) -> Self {
        self.include_reasoning = include_reasoning;
        self
    }

    fn to_openai_messages(
        &self,
        messages: &[Message],
    ) -> Result<Vec<ChatCompletionRequestMessage>, LLMError> {
        let mut openai_messages: Vec<ChatCompletionRequestMessage> = Vec::new();
        for m in messages {
            match m {
                Message::AIAssistant {
                    content,
                    tool_calls,
                } => openai_messages.push(match tool_calls {
                    Some(calls) => {
                        let function = calls
                            .iter()
                            .map(|tc| ChatCompletionMessageToolCall {
                                id: tc.id.clone(),
                                r#type: async_openai::types::ChatCompletionToolType::Function,
                                function: async_openai::types::FunctionCall {
                                    name: tc.function.name.clone(),
                                    arguments: serde_json::to_string(&tc.function.arguments)
                                        .expect("failed to serialize arguments"),
                                },
                            })
                            .collect::<Vec<_>>();
                        ChatCompletionRequestAssistantMessageArgs::default()
                            .tool_calls(function)
                            .content(String::from(content.clone()))
                            .build()?
                            .into()
                    }
                    None => ChatCompletionRequestAssistantMessageArgs::default()
                        .content(String::from(content.clone()))
                        .build()?
                        .into(),
                }),
                Message::HumanMessage(content) => openai_messages.push(
                    ChatCompletionRequestUserMessageArgs::default()
                        .content(String::from(content.clone()))
                        .build()?
                        .into(),
                ),
                Message::SystemMessage(content) => openai_messages.push(
                    ChatCompletionRequestSystemMessageArgs::default()
                        .content(String::from(content.clone()))
                        .build()?
                        .into(),
                ),
                Message::ToolMessage { toolcall, result } => {
                    openai_messages.push(
                        ChatCompletionRequestToolMessageArgs::default()
                            .content(result.to_string())
                            .tool_call_id(toolcall.id.clone())
                            .build()?
                            .into(),
                    );
                }
            }
        }
        Ok(openai_messages)
    }

    fn generate_request(
        &self,
        messages: &[Message],
        stream: bool,
        config: &LLMRunConfig,
    ) -> Result<CreateChatCompletionRequest, LLMError> {
        let messages: Vec<ChatCompletionRequestMessage> = self.to_openai_messages(messages)?;
        let mut request_builder = CreateChatCompletionRequestArgs::default();
        if let Some(temperature) = self.options.temperature {
            request_builder.temperature(temperature);
        }
        if let Some(max_tokens) = self.options.max_tokens {
            request_builder.max_tokens(max_tokens);
        }
        if stream {
            if let Some(include_usage) = self.options.stream_usage {
                request_builder.stream_options(ChatCompletionStreamOptions { include_usage });
            }
            request_builder.stream(true);
        }
        request_builder.model(self.model.to_string());
        if let Some(stop_words) = &self.options.stop_words {
            request_builder.stop(stop_words);
        }

        // 优先用当前请求传入的
        if let Some(functions) = &config.tool_definitions {
            let functions: Result<Vec<_>, OpenAIError> = functions
                .values()
                .map(|f| Into::<FunctionDefinition>::into(f).try_into_openai())
                .collect();
            request_builder.tools(functions?);
        } else if let Some(functions) = &self.options.functions {
            let functions: Result<Vec<_>, OpenAIError> = functions
                .clone()
                .into_iter()
                .map(|f| f.try_into_openai())
                .collect();
            request_builder.tools(functions?);
        }

        if let Some(behavior) = &self.options.function_call_behavior {
            request_builder
                .tool_choice::<ChatCompletionToolChoiceOption>(behavior.clone().into_openai());
        }

        if let Some(response_format) = &self.options.response_format {
            request_builder
                .response_format::<ResponseFormat>(response_format.clone().into_openai());
        }

        request_builder.messages(messages);
        Ok(request_builder.build()?)
    }

    async fn generate_with_config(
        &self,
        messages: &[Message],
        config: &LLMRunConfig,
    ) -> Result<GenerateResult, LLMError> {
        let client = Client::new();
        let is_stream = self.options.streaming_func.is_some();

        let payload = self.generate_request(messages, is_stream, config)?;
        let res = client
            .post(format!("{}/v1/chat/completions", self.base_url))
            .header("Authorization", format!("Bearer {}", self.api_key))
            .header("Content-Type", "application/json")
            .json(&payload)
            .send()
            .await?;

        let status = res.status().as_u16();

        let res = match status {
            400 => {
                println!("{:?}", res.bytes().await.unwrap());
                Err(LLMError::DeepseekError(DeepseekError::InvalidFormatError(
                    "Invalid request format".to_string(),
                )))
            }
            401 => Err(LLMError::DeepseekError(DeepseekError::AuthenticationError(
                "Invalid API Key".to_string(),
            ))),
            402 => Err(LLMError::DeepseekError(
                DeepseekError::InsufficientBalanceError("Insufficient balance".to_string()),
            )),
            422 => Err(LLMError::DeepseekError(
                DeepseekError::InvalidParametersError("Invalid parameters".to_string()),
            )),
            429 => Err(LLMError::DeepseekError(DeepseekError::RateLimitError(
                "Rate limit reached".to_string(),
            ))),
            500 => Err(LLMError::DeepseekError(DeepseekError::ServerError(
                "Server error".to_string(),
            ))),
            503 => Err(LLMError::DeepseekError(
                DeepseekError::ServerOverloadedError("Server overloaded".to_string()),
            )),
            200 => Ok(res.json::<ApiResponse>().await?),
            _ => {
                let error_body = res
                    .text()
                    .await
                    .unwrap_or_else(|_| "Unknown error".to_string());
                Err(LLMError::DeepseekError(DeepseekError::ServerError(
                    format!("HTTP {}: {}", status, error_body),
                )))
            }
        }?;

        let choice = res.choices.first();

        let mut generation = choice
            .map(|c| c.message.content.clone())
            .unwrap_or_default();

        let tool_calls = choice.and_then(|c| c.message.tool_calls.as_ref()).map(|t| {
            t.iter()
                .cloned()
                .enumerate()
                .map(|(index, tc)| ToolCall {
                    id: tc.id,
                    index,
                    function: FunctionCall {
                        name: tc.function.name,
                        arguments: serde_json::from_str(&tc.function.arguments).unwrap_or_default(),
                    },
                })
                .collect::<Vec<_>>()
        });
        // dbg!(&generation);
        // dbg!(&choice);
        // dbg!(&tool_calls);

        // If include_reasoning is enabled and the model is deepseek-reasoner,
        // append the reasoning content to the generation if available
        if self.include_reasoning && self.model == DeepseekModel::DeepseekReasoner.to_string() {
            if let Some(reasoning) = choice.and_then(|c| c.message.reasoning_content.clone()) {
                generation = format!("Reasoning:\n{}\n\nAnswer:\n{}", reasoning, generation);
            }
        }

        let tokens = Some(TokenUsage {
            prompt_tokens: res.usage.prompt_tokens,
            completion_tokens: res.usage.completion_tokens,
            total_tokens: res.usage.total_tokens,
        });

        Ok(GenerateResult {
            tokens,
            generation,
            tool_calls,
            ..Default::default()
        })
    }

    fn build_payload(&self, messages: &[Message], stream: bool) -> Payload {
        let mut response_format = None;
        if self.json_mode {
            response_format = Some(ResponseFormat::JsonObject);
        }

        let mut payload = Payload {
            model: self.model.clone(),
            messages: messages
                .iter()
                .map(DeepseekMessage::from_message)
                .collect::<Vec<_>>(),
            max_tokens: self.options.max_tokens,
            stream: None,
            temperature: self.options.temperature,
            top_p: self.options.top_p,
            frequency_penalty: None,
            presence_penalty: None,
            stop: self.options.stop_words.clone(),
            response_format,
        };

        if stream {
            payload.stream = Some(true);
        }

        // Apply frequency_penalty if it's in the options range
        if let Some(fp) = self.options.frequency_penalty {
            if (-2.0..=2.0).contains(&fp) {
                payload.frequency_penalty = Some(fp);
            }
        }

        // Apply presence_penalty if it's in the options range
        if let Some(pp) = self.options.presence_penalty {
            if (-2.0..=2.0).contains(&pp) {
                payload.presence_penalty = Some(pp);
            }
        }

        payload
    }

    fn parse_sse_chunk(chunk: &[u8]) -> Result<Vec<Value>, LLMError> {
        let text = str::from_utf8(chunk).map_err(|e| LLMError::ParsingError(e.to_string()))?;
        let mut values = Vec::new();

        for line in text.lines() {
            if let Some(data) = line.strip_prefix("data: ") {
                if data == "[DONE]" {
                    continue;
                }
                let value: Value = serde_json::from_str(data).map_err(|e| {
                    LLMError::ParsingError(format!("Failed to parse SSE data: {}", e))
                })?;
                values.push(value);
            }
        }

        Ok(values)
    }

    fn extract_error_from_json(json_value: &Value) -> Option<LLMError> {
        json_value.get("error").map(|error| {
            let error_message = error
                .get("message")
                .and_then(|m| m.as_str())
                .unwrap_or("Unknown API error")
                .to_string();
            let error_code = error.get("code").and_then(|c| c.as_u64()).unwrap_or(0);
            LLMError::OtherError(format!("API error ({}): {}", error_code, error_message))
        })
    }

    fn extract_usage_from_chunk(chunk: &Value) -> Option<TokenUsage> {
        chunk.get("usage").map(|usage| TokenUsage {
            prompt_tokens: usage
                .get("prompt_tokens")
                .and_then(|t| t.as_u64())
                .unwrap_or(0) as u32,
            completion_tokens: usage
                .get("completion_tokens")
                .and_then(|t| t.as_u64())
                .unwrap_or(0) as u32,
            total_tokens: usage
                .get("total_tokens")
                .and_then(|t| t.as_u64())
                .unwrap_or(0) as u32,
        })
    }

    fn process_reasoning_content(
        delta: &Value,
        usage: &Option<TokenUsage>,
        include_reasoning: bool,
        is_reasoner: bool,
    ) -> Option<LLMStreamData> {
        if include_reasoning && is_reasoner {
            delta
                .get("reasoning_content")
                .and_then(|c| c.as_str())
                .filter(|reasoning| !reasoning.is_empty())
                .map(|reasoning| {
                    let mut data = LLMStreamData::new_content(format!("Reasoning: {}", reasoning));
                    data.with_tokens(usage.clone());
                    data
                })
        } else {
            None
        }
    }

    fn process_content_delta(
        delta: &Value,
        usage: &Option<TokenUsage>,
        generation: &mut String,
    ) -> Option<LLMStreamData> {
        delta
            .get("content")
            .and_then(|c| c.as_str())
            .filter(|content| !content.is_empty())
            .map(|content| {
                generation.push_str(content);
                let mut data = LLMStreamData::new_content(generation.clone());
                data.with_tokens(usage.clone());
                data
            })
    }

    fn process_tool_call_delta(
        delta: &Value,
        tool_call_accumulators: &mut HashMap<usize, ToolCallAccumulator>,
        usage: &Option<TokenUsage>,
    ) -> Vec<LLMStreamData> {
        let mut results = Vec::new();

        let Some(tool_calls) = delta.get("tool_calls").and_then(|tc| tc.as_array()) else {
            return results;
        };
        // dbg!(&delta);
        for tool_call in tool_calls {
            // dbg!(&tool_call);
            let Some(index) = tool_call.get("index").and_then(|i| i.as_u64()) else {
                continue;
            };

            let index = index as usize;
            let accumulator =
                tool_call_accumulators
                    .entry(index)
                    .or_insert_with(|| ToolCallAccumulator {
                        id: None,
                        name: None,
                        arguments: String::new(),
                    });

            // Accumulate tool call ID
            if let Some(id) = tool_call
                .get("id")
                .and_then(|i| i.as_str())
                .filter(|s| !s.is_empty())
            {
                accumulator.id = Some(id.to_string());
            }

            // Accumulate function information
            if let Some(function) = tool_call.get("function") {
                if let Some(name) = function
                    .get("name")
                    .and_then(|n| n.as_str())
                    .filter(|s| !s.is_empty())
                {
                    accumulator.name = Some(name.to_string());
                }
                if let Some(arguments) = function.get("arguments").and_then(|a| a.as_str()) {
                    accumulator.arguments.push_str(arguments);
                }
            }

            // Check if we have a complete tool call
            if let Some(name) = &accumulator.name {
                let json = {
                    if accumulator.arguments.is_empty() {
                        None
                    } else {
                        match repair_json_object_v2(&accumulator.arguments) {
                            Ok(json) => {
                                if serde_json::from_str::<Value>(&json).is_ok() {
                                    Some(json)
                                } else {
                                    None
                                }
                            }
                            Err(_) => None,
                        }
                    }
                };
                // dbg!(&accumulator.arguments);
                if let Some(json) = json {
                    // dbg!(&json);
                    let tool_call = ToolCall {
                        // 有时候没有 Id，需要跳过
                        id: accumulator.id.clone().unwrap_or_default(),
                        index,
                        function: FunctionCall {
                            name: name.clone(),
                            arguments: serde_json::from_str(&json).unwrap_or_default(),
                        },
                    };
                    let mut data = LLMStreamData::new_tool_call(tool_call);
                    data.with_tokens(usage.clone());
                    results.push(data);
                }
            }
        }

        results
    }

    fn process_finish_reason(choice: &Value, usage: &Option<TokenUsage>) -> Option<LLMStreamData> {
        choice
            .get("finish_reason")
            .and_then(|f| f.as_str())
            .filter(|reason| *reason == "tool_calls" || *reason == "stop")
            .map(|_| {
                let mut data = LLMStreamData::new_finished();
                data.with_tokens(usage.clone());
                data
            })
    }

    fn process_chunk_data(
        chunk: &Value,
        tool_call_accumulators: &mut HashMap<usize, ToolCallAccumulator>,
        include_reasoning: bool,
        is_reasoner: bool,
        generation: &mut String,
    ) -> Result<Vec<LLMStreamData>, LLMError> {
        // Check for errors first
        if let Some(error) = Self::extract_error_from_json(chunk) {
            return Err(error);
        }

        let usage = Self::extract_usage_from_chunk(chunk);
        let mut results = Vec::new();

        if let Some(choices) = chunk.get("choices").and_then(|c| c.as_array()) {
            if let Some(choice) = choices.first() {
                if let Some(delta) = choice.get("delta") {
                    // Process reasoning content
                    if let Some(reasoning_data) = Self::process_reasoning_content(
                        delta,
                        &usage,
                        include_reasoning,
                        is_reasoner,
                    ) {
                        results.push(reasoning_data);
                    }

                    // Process content
                    if let Some(content_data) =
                        Self::process_content_delta(delta, &usage, generation)
                    {
                        results.push(content_data);
                    }

                    // Process tool calls
                    let tool_call_data =
                        Self::process_tool_call_delta(delta, tool_call_accumulators, &usage);
                    results.extend(tool_call_data);

                    // 当收到 finish_reason 时，清除所有工具调用累积器
                    // 这确保了工具调用完成后状态被正确清理
                    if choice.get("finish_reason").and_then(|f| f.as_str()) == Some("tool_calls") {
                        tool_call_accumulators.clear();
                    }

                    // Process finish reason
                    if let Some(finish_data) = Self::process_finish_reason(choice, &usage) {
                        results.push(finish_data);
                    }
                }
            }
        }

        Ok(results)
    }

    async fn stream_internal(
        &self,
        messages: &[Message],
        config: &LLMRunConfig,
    ) -> Result<Pin<Box<dyn Stream<Item = Result<LLMStreamData, LLMError>> + Send>>, LLMError> {
        let client = Client::new();
        let payload = self.generate_request(messages, true, config)?;
        dbg!(&payload);
        let response = client
            .post(format!("{}/v1/chat/completions", self.base_url))
            .header("Authorization", format!("Bearer {}", self.api_key))
            .header("Content-Type", "application/json")
            .json(&payload)
            .send()
            .await?;

        let mut stream = response.bytes_stream();

        let include_reasoning = self.include_reasoning;
        let is_reasoner = self.model == DeepseekModel::DeepseekReasoner.to_string();

        use async_stream::stream;
        let processed_stream = stream! {
            let mut buffer = Vec::new();
            let mut tool_call_accumulators: HashMap<usize, ToolCallAccumulator> = HashMap::new();
            let mut generation = String::new();

            while let Some(chunk_result) = stream.next().await {
                match chunk_result {
                    Ok(chunk) => {
                        buffer.extend_from_slice(&chunk);

                        // Try to parse as direct JSON first (for error responses)
                        if let Ok(text) = str::from_utf8(&buffer) {
                            if let Ok(json_value) = serde_json::from_str::<Value>(text) {
                                if let Some(error) = Self::extract_error_from_json(&json_value) {
                                    yield Err(error);
                                    return;
                                }
                            }
                        }

                        // Try to parse SSE data
                        if let Ok(chunks) = Self::parse_sse_chunk(&buffer) {
                            buffer.clear();

                            for chunk in chunks {
                                match Self::process_chunk_data(
                                    &chunk,
                                    &mut tool_call_accumulators,
                                    include_reasoning,
                                    is_reasoner,
                                    &mut generation,
                                ) {
                                    Ok(stream_data_list) => {
                                        for data in stream_data_list {
                                            yield Ok(data);
                                        }
                                    }
                                    Err(e) => {
                                        yield Err(e);
                                        return;
                                    }
                                }
                            }
                        }
                    }
                    Err(e) => {
                        yield Err(LLMError::OtherError(format!("Stream chunk error: {}", e)));
                        break;
                    }
                }
            }
        };

        Ok(Box::pin(processed_stream))
    }
}

#[async_trait]
impl LLM for Deepseek {
    type LLMConfig = LLMRunConfig;
    fn name(&self) -> &'static str {
        "deepseek"
    }

    async fn generate(
        &self,
        messages: &[Message],
    ) -> Result<GenerateResult, LLMError> {
        self.generate_with_config(messages, &LLMRunConfig::default())
            .await
    }

    async fn generate_with_config(
        &self,
        messages: &[Message],
        config: &Self::LLMConfig,
    ) -> Result<GenerateResult, LLMError> {
        let messages: Vec<Message> = messages.to_vec();
        match &self.options.streaming_func {
            Some(func) => {
                let mut complete_response = String::new();
                let mut stream = self.stream_with_config(&messages, config).await?;
                while let Some(data) = stream.next().await {
                    match data {
                        Ok(value) => {
                            let mut func = func.lock().await;
                            complete_response = value.content();
                            let _ = func(value.content().to_string()).await;
                        }
                        Err(e) => return Err(e),
                    }
                }
                let generate_result = GenerateResult {
                    generation: complete_response,
                    ..Default::default()
                };
                Ok(generate_result)
            }
            None => self.generate_with_config(&messages, config).await,
        }
    }

    async fn stream(
        &self,
        messages: &[Message],
    ) -> Result<Pin<Box<dyn Stream<Item = Result<LLMStreamData, LLMError>> + Send>>, LLMError> {
        let processed_messages: Vec<Message> = messages.to_vec();

        self.stream_internal(&processed_messages, &LLMRunConfig::default())
            .await
    }

    async fn stream_with_config(
        &self,
        messages: &[Message],
        config: &Self::LLMConfig,
    ) -> Result<Pin<Box<dyn Stream<Item = Result<LLMStreamData, LLMError>> + Send>>, LLMError> {
        let processed_messages: Vec<Message> = messages.to_vec();

        self.stream_internal(&processed_messages, config).await
    }

    fn add_options(&mut self, options: CallOptions) {
        self.options = options;
    }

    fn with_options(&mut self, options: CallOptions) {
        self.options = options;
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    #[ignore]
    async fn test_deepseek_generate() {
        use crate::core::message::Message;
        let messages = vec![Message::new_human_message("Hello".to_string())];

        let client = Deepseek::new();
        let res = client.generate(&messages).await;
        assert!(res.is_ok());
    }

    #[tokio::test]
    #[ignore]
    async fn test_deepseek_stream() {
        use crate::core::message::Message;
        let messages = vec![Message::new_human_message("Hello".to_string())];

        let client = Deepseek::new();
        let res = client.stream(&messages).await;
        assert!(res.is_ok());
    }

    #[tokio::test]
    #[ignore]
    async fn test_deepseek_reasoner() {
        use crate::core::message::Message;
        let messages = vec![Message::new_human_message(
            "9.11 and 9.8, which is greater?".to_string(),
        )];

        // Create a client with the DeepseekReasoner model and enable reasoning content
        let client = Deepseek::new()
            .with_model(DeepseekModel::DeepseekReasoner.to_string())
            .with_include_reasoning(true);

        let res = client.generate(&messages).await;
        assert!(res.is_ok());

        // The response will contain both the reasoning and answer content
        if let Ok(result) = res {
            println!("Generation result: {}", result.generation);
        }
    }
}
