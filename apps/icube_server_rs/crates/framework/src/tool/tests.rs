use std::{borrow::Cow, collections::HashSet, sync::Arc};

use crate::{
    core::{
        artifact::{Artifact, Part},
        error::ToolError,
        tool::{CallToolResponse, DynTool, Tool, ToolCallingContext, Tool<PERSON><PERSON>ult},
    },
    ioc::<PERSON><PERSON><PERSON><PERSON><PERSON>,
    tool::tool::IntoDynTool,
};
use async_trait::async_trait;
use schemars::JsonSchema;
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, JsonSchema, Default)]
struct MockToolParams {
    input: String,
    count: Option<u32>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
struct MockToolResult {
    output: String,
    processed_count: u32,
}

struct MockTool {
    should_fail: bool,
}

impl MockTool {
    fn new() -> Self {
        Self { should_fail: false }
    }

    fn new_failing() -> Self {
        Self { should_fail: true }
    }
}

#[async_trait]
impl Tool for MockTool {
    const NAME: &'static str = "mock_tool";
    type Params = MockToolParams;
    type Result = MockToolResult;

    fn description(&self) -> Cow<str> {
        Cow::Borrowed("A mock tool for testing purposes")
    }

    fn alias(&self) -> Option<HashSet<Cow<str>>> {
        let mut aliases = HashSet::new();
        aliases.insert(Cow::Borrowed("mock"));
        aliases.insert(Cow::Borrowed("test_tool"));
        Some(aliases)
    }

    async fn call(
        &self,
        ctx: ToolCallingContext,
        param: Option<Self::Params>,
    ) -> ToolResult<Self::Result> {
        if self.should_fail {
            return Err(ToolError::ExecuteError {
                tool_name: Tool::name(self).into(),
                message: "Mock tool intentionally failed".to_string(),
            });
        }

        let param = param.unwrap_or(MockToolParams {
            input: "default".to_string(),
            count: None,
        });

        let count = param.count.unwrap_or(1);
        let output = format!("Processed: {} (count: {})", param.input, count);

        Ok(MockToolResult {
            output,
            processed_count: count,
        })
    }

    fn to_artifact(
        &self,
        // 可能需要拿 config
        _ctx: ToolCallingContext,
        param: Option<&Self::Params>,
        result: &Self::Result,
    ) -> Option<Artifact> {
        if let Some(param) = param {
            if param.input.contains("artifact") {
                let artifact = Artifact::new("mock_artifact", "Artifact generated by mock tool")
                    .add_part(Part::Text {
                        text: result.output.clone(),
                        metadata: None,
                    });
                return Some(artifact);
            }
        }
        None
    }

    async fn to_toolmessage_result(
        &self,
        _ctx: ToolCallingContext,
        _param: Option<&Self::Params>,
        result: &Self::Result,
    ) -> Result<Value, serde_json::Error> {
        serde_json::to_value(json!({
            "message": result.output,
            "count": result.processed_count
        }))
    }
}

#[tokio::test]
async fn test_tool_basic_functionality() {
    let tool = MockTool::new();

    assert_eq!(Tool::name(&tool), "mock_tool");
    assert_eq!(Tool::description(&tool), "A mock tool for testing purposes");

    let aliases = Tool::alias(&tool).unwrap();
    assert!(aliases.contains(&Cow::Borrowed("mock")));
    assert!(aliases.contains(&Cow::Borrowed("test_tool")));

    let schema = Tool::schema(&tool);
    assert!(schema.is_object());
}

#[tokio::test]
async fn test_tool_call_success() {
    let tool = MockTool::new();

    let params = MockToolParams {
        input: "test input".to_string(),
        count: Some(5),
    };
    let ctx = ToolCallingContext::new(Arc::new(IOCContainer::new()));

    let result = tool.call(ctx, Some(params)).await.unwrap();
    assert_eq!(result.output, "Processed: test input (count: 5)");
    assert_eq!(result.processed_count, 5);
}

#[tokio::test]
async fn test_dyn_tool_call_success() {
    let tool = MockTool::new();
    let dyn_tool: std::sync::Arc<dyn DynTool> = tool.into_dyn();

    let param_json = json!({
        "input": "dynamic test",
        "count": 3
    });
    let ctx = ToolCallingContext::new(Arc::new(IOCContainer::new()));
    let response = dyn_tool.call_tool(ctx, Some(param_json)).await.unwrap();

    match response {
        CallToolResponse::Success(success_response) => {
            assert_eq!(success_response.code, 0);
            assert!(success_response.artifact.is_none());

            let result = &success_response.toolmessage_result;
            assert_eq!(result["message"], "Processed: dynamic test (count: 3)");
            assert_eq!(result["count"], 3);
        }
        CallToolResponse::Fail { .. } => panic!("Expected success response"),
    }
}

#[tokio::test]
async fn test_dyn_tool_call_with_artifact() {
    let tool = MockTool::new();
    let dyn_tool: std::sync::Arc<dyn DynTool> = tool.into_dyn();

    let param_json = json!({
        "input": "artifact test",
        "count": 2
    });
    let ctx = ToolCallingContext::new(Arc::new(IOCContainer::new()));
    let response = dyn_tool.call_tool(ctx, Some(param_json)).await.unwrap();

    match response {
        CallToolResponse::Success(success_response) => {
            assert_eq!(success_response.code, 0);

            let artifact = success_response
                .artifact
                .as_ref()
                .expect("Should have artifact");
            assert_eq!(artifact.name, "mock_artifact");
            assert_eq!(artifact.description, "Artifact generated by mock tool");
            assert_eq!(artifact.parts.len(), 1);

            if let Part::Text { text, .. } = &artifact.parts[0] {
                assert_eq!(text, "Processed: artifact test (count: 2)");
            } else {
                panic!("Expected text part");
            }

            let result = success_response.toolmessage_result;
            assert_eq!(result["message"], "Processed: artifact test (count: 2)");
            assert_eq!(result["count"], 2);
        }
        CallToolResponse::Fail { .. } => panic!("Expected success response"),
    }
}

#[tokio::test]
async fn test_dyn_tool_call_failure() {
    let tool = MockTool::new_failing();
    let dyn_tool: std::sync::Arc<dyn DynTool> = tool.into_dyn();
    let ctx = ToolCallingContext::new(Arc::new(IOCContainer::new()));
    let response = dyn_tool.call_tool(ctx, None).await.unwrap();

    match response {
        CallToolResponse::Fail {
            code,
            error_message,
        } => {
            assert_eq!(code, 1);
            assert!(error_message.contains("Mock tool intentionally failed"));
        }
        CallToolResponse::Success { .. } => panic!("Expected failure response"),
    }
}
