# codebase ci syntax: https://bytedance.larkoffice.com/wiki/wikcn98ZWMAHiIwkxhJNeBaH5Bd
# Ton 机器列表: https://bytedance.larkoffice.com/wiki/wikcnIC9a4aEcvajRUnrioMgMfX
# 自有构建机列表: https://tron.bytedance.net/manage

# Native IDE 构建
# 环境变量定义:
# - BUILD_QUALITY: 构建产物类型，枚举值 dev、alpha、beta、stable
# - CUSTOM_BUILD_INTEL: 是否构建 intel 产物，字符串 true、false
# - CUSTOM_BUILD_UNIVERSAL: 是否构建 universal 产物，字符串 true、false
# - CUSTOM_USE_SELF_HOSTED: 是否使用自有构建机构建 darwin 平台，字符串 true、false
# - CUSTOM_WIN32_USE_SELF_HOSTED: 是否使用自有构建机构建 win32 平台，字符串 true、false

name: Trae Client Builder
trigger:
  manual: {}
jobs:
  # IDE 构建 | Tron 构建机 | darwin | arm64
  ide_apple_tron:
    name: ide_apple_tron
    image: none
    runs-on:
      cluster: tron.macOS_applesilicon.nvm
      macos: {}
    if: ${{ Event.Inputs.CUSTOM_USE_SELF_HOSTED == "false" && Event.Inputs.CUSTOM_BUILD_DARWIN != "false" }}
    steps:
      - commands:
        - git clean -dfx || true
      - uses: actions/checkout
      - commands:
        - nvm use || nvm install $(cat .nvmrc)
        - sh -x build/ide/build_desktop.sh
    envs: &common_envs
      FROM_GITLAB_CI: "1"
      PLATFORM: darwin
      ARCH: arm64
      BUILD_ARCH: arm64
      HTTP_PROXY: http://sys-proxy-rd-relay.byted.org:8118
      ELECTRON_BUILDER_BINARIES_MIRROR: https://bnpm.bytedance.net/mirrors/electron-builder-binaries/
      ELECTRON_MIRROR: https://bnpm.bytedance.net/mirrors/electron/
      ICUBE_BUILD_DESKTOP: "true"
      GITHUB_TOKEN: ${{GITHUB_TOKEN}}
      CI_JOB_TOKEN: ${{CI_JOB_TOKEN}}
      TOS_ACCESS_KEY: ${{TOS_ACCESS_KEY}}

  # IDE 构建 | Tron 构建机 | darwin | x64
  # ide_intel_tron:
  #   name: ide_intel_tron
  #   runs-on:
  #     cluster: tron.macOS_0102_intel.nvm
  #     macos: {}
  #   if: ${{ Event.Inputs.CUSTOM_USE_SELF_HOSTED == "false" && Event.Inputs.CUSTOM_BUILD_INTEL == "true" && Event.Inputs.CUSTOM_BUILD_DARWIN != "false" }}
  #   image: none
  #   steps:
  #     - commands:
  #       - git clean -dfx || true
  #     - uses: actions/checkout
  #     - commands:
  #       - nvm use || nvm install $(cat .nvmrc)
  #       - sh -x build/ide/build_desktop.sh
  #   envs:
  #     <<: *common_envs
  #     PLATFORM: darwin
  #     ARCH: x64
  #     BUILD_ARCH: x64

  # IDE 构建 | Tron 构建机 | darwin | universal
  # ide_universal_tron:
  #   name: ide_universal_tron
  #   runs-on:
  #     cluster: tron.macOS_applesilicon.nvm
  #     macos: {}
  #   if: ${{ Event.Inputs.CUSTOM_USE_SELF_HOSTED == "false" && Event.Inputs.CUSTOM_BUILD_INTEL == "true" && Event.Inputs.CUSTOM_BUILD_DARWIN != "false" && Event.Inputs.CUSTOM_BUILD_UNIVERSAL == "true" }}
  #   image: none
  #   steps:
  #     - commands:
  #       - git clean -dfx || true
  #     - uses: actions/checkout
  #     - commands:
  #       - nvm use || nvm install $(cat .nvmrc)
  #       - sh -x build/ide/build_desktop.sh
  #   envs:
  #     <<: *common_envs
  #     PLATFORM: darwin
  #     ARCH: arm64
  #     BUILD_ARCH: universal

  # IDE 构建 | Tron 构建机 | win32 | x64
  ide_win32_x64_tron:
    name: ide_win32_x64_tron
    runs-on:
      cluster: tron.windows_02.node14
      windows: {}
    image: none
    if: ${{ (Event.Inputs.CUSTOM_USE_SELF_HOSTED == "false" || Event.Inputs.CUSTOM_WIN32_USE_SELF_HOSTED == "false") && Event.Inputs.CUSTOM_BUILD_WINDOWS == "true" }}
    steps:
      - commands:
        - if (Test-Path .git) { git clean -dfx } else { Write-Host "No git repository found" }
      - uses: actions/checkout
      - commands:
        - $PSVersionTable.PSVersion
        - choco config set proxy http://sys-proxy-rd-relay.byted.org:8118
        - choco install -y fnm
        - fnm env --use-on-cd | Out-String | Invoke-Expression
        - fnm install 20.18.1
        - fnm use 20.18.1
        - node -v
        - $env:REPO_ROOT = Get-Location
        - chcp 65001
        - node "$env:REPO_ROOT/build/ide/build_desktop.js"
    envs:
      <<: *common_envs
      PLATFORM: win32
      ARCH: x64
      BUILD_ARCH: x64

  # dev 包 | 自有构建机 | darwin | arm64
  ide_dev_apple_self_hosted:
    name: ide_dev_apple_self_hosted
    runs-on:
      cluster: icube.darwin.apple.dev
      macos: {}
    image: none
    if: ${{ Event.Inputs.BUILD_QUALITY == "dev" && Event.Inputs.CUSTOM_USE_SELF_HOSTED == "true" && Event.Inputs.CUSTOM_BUILD_DARWIN != "false" }}
    steps:
      - commands:
        - git clean -dfx || true
      - uses: actions/checkout
      - commands:
        - nvm use || nvm install $(cat .nvmrc)
        - sh -x build/ide/build_desktop.sh
    envs:
      <<: *common_envs
      PLATFORM: darwin
      ARCH: arm64
      BUILD_ARCH: arm64

  # dev 包 | 自有构建机 | darwin | x64
  ide_dev_intel_self_hosted:
    name: ide_dev_intel_self_hosted
    runs-on:
      cluster: icube.darwin.intel.dev
      macos: {}
    image: none
    steps:
      - commands:
        - git clean -dfx || true
      - uses: actions/checkout
      - commands:
        - nvm use || nvm install $(cat .nvmrc)
        - sh -x build/ide/build_desktop.sh
    if: ${{ Event.Inputs.BUILD_QUALITY == "dev" && Event.Inputs.CUSTOM_BUILD_INTEL != "false" && Event.Inputs.CUSTOM_USE_SELF_HOSTED == "true" && Event.Inputs.CUSTOM_BUILD_DARWIN != "false" }}
    envs:
      <<: *common_envs
      PLATFORM: darwin
      ARCH: x64
      BUILD_ARCH: x64

  # dev 包 | 自有构建机 | darwin | x64
  ide_dev_win32_x64_self_hosted:
    name: ide_dev_win32_x64_self_hosted
    runs-on:
      cluster: icube.win32.x64.dev
      windows: {}
    image: none
    if: ${{ Event.Inputs.BUILD_QUALITY == "dev" && Event.Inputs.CUSTOM_BUILD_WINDOWS == "true" && Event.Inputs.CUSTOM_USE_SELF_HOSTED == "true" && Event.Inputs.CUSTOM_WIN32_USE_SELF_HOSTED == "true" }}
    steps:
      - commands:
        - if (Test-Path .git) { git clean -dfx } else { Write-Host "No git repository found" }
      - uses: actions/checkout
      - commands:
        - $PSVersionTable.PSVersion
        - node -v
        - $env:REPO_ROOT = Get-Location
        - node "$env:REPO_ROOT/build/ide/build_desktop.js"
    envs:
      <<: *common_envs
      PLATFORM: win32
      ARCH: x64
      BUILD_ARCH: x64

  # alpha、beta 包 | 自有构建机 | darwin | arm64
  ide_test_apple_self_hosted:
    name: ide_test_apple_self_hosted
    runs-on:
      cluster: icube.darwin.apple.test
      macos: {}
    image: none
    if: ${{ (Event.Inputs.BUILD_QUALITY == "alpha" || Event.Inputs.BUILD_QUALITY == "beta") && Event.Inputs.CUSTOM_USE_SELF_HOSTED == "true" && Event.Inputs.CUSTOM_BUILD_DARWIN != "false" }}
    steps:
      - commands:
        - git clean -dfx || true
      - uses: actions/checkout
      - commands:
        - nvm use || nvm install $(cat .nvmrc)
        - sh -x build/ide/build_desktop.sh
    envs:
      <<: *common_envs
      PLATFORM: darwin
      ARCH: arm64
      BUILD_ARCH: arm64

  # alpha、beta 包 | 自有构建机 | darwin | x64 | 交叉编译
  ide_test_intel_self_hosted:
    name: ide_test_intel_self_hosted
    runs-on:
      cluster: icube.darwin.apple.test
      macos: {}
    image: none
    if: ${{ (Event.Inputs.BUILD_QUALITY == "alpha" || Event.Inputs.BUILD_QUALITY == "beta") && Event.Inputs.CUSTOM_BUILD_INTEL!= "false" && Event.Inputs.CUSTOM_USE_SELF_HOSTED == "true" && Event.Inputs.CUSTOM_BUILD_DARWIN != "false" }}
    steps:
      - commands:
        - git clean -dfx || true
      - uses: actions/checkout
      - commands:
        - nvm use || nvm install $(cat .nvmrc)
        - sh -x build/ide/build_desktop.sh
    envs:
      <<: *common_envs
      PLATFORM: darwin
      ARCH: arm64
      BUILD_ARCH: x64

  # alpha、beta 包 | 自有构建机 | darwin | x64
  ide_test_win32_x64_self_hosted:
    name: ide_test_win32_x64_self_hosted
    runs-on:
      cluster: icube.win32.x64.test
      windows: {}
    image: none
    if: ${{ (Event.Inputs.BUILD_QUALITY == "alpha" || Event.Inputs.BUILD_QUALITY == "beta") && Event.Inputs.CUSTOM_BUILD_WINDOWS == "true" && Event.Inputs.CUSTOM_USE_SELF_HOSTED == "true" && Event.Inputs.CUSTOM_WIN32_USE_SELF_HOSTED == "true" }}
    steps:
      - commands:
        - if (Test-Path .git) { git clean -dfx } else { Write-Host "No git repository found" }
      - uses: actions/checkout
      - commands:
        - $PSVersionTable.PSVersion
        - node -v
        - $env:REPO_ROOT = Get-Location
        - node "$env:REPO_ROOT/build/ide/build_desktop.js"
    envs:
      <<: *common_envs
      PLATFORM: win32
      ARCH: x64
      BUILD_ARCH: x64

  # stable 包 | 自有构建机 | darwin | arm64
  ide_stable_apple_self_hosted:
    name: ide_stable_apple_self_hosted
    runs-on:
      cluster: icube.darwin.apple.stable
      macos: {}
    image: none
    if: ${{ Event.Inputs.BUILD_QUALITY == "stable" && Event.Inputs.CUSTOM_USE_SELF_HOSTED == "true" && Event.Inputs.CUSTOM_BUILD_DARWIN != "false" }}
    steps:
      - commands:
        - git clean -dfx || true
      - uses: actions/checkout
      - commands:
        - nvm use || nvm install $(cat .nvmrc)
        - sh -x build/ide/build_desktop.sh
    envs:
      <<: *common_envs
      PLATFORM: darwin
      ARCH: arm64
      BUILD_ARCH: arm64

  # stable 包 | 自有构建机 | darwin | x64
  ide_stable_intel_self_hosted:
    name: ide_stable_intel_self_hosted
    runs-on:
      cluster: icube.darwin.intel.stable
      macos: {}
    image: none
    if: ${{ Event.Inputs.BUILD_QUALITY == "stable" && Event.Inputs.CUSTOM_USE_SELF_HOSTED == "true" && Event.Inputs.CUSTOM_BUILD_DARWIN != "false" }}
    steps:
      - commands:
        - git clean -dfx || true
      - uses: actions/checkout
      - commands:
        - nvm use || nvm install $(cat .nvmrc)
        - sh -x build/ide/build_desktop.sh
    envs:
      <<: *common_envs
      PLATFORM: darwin
      ARCH: x64
      BUILD_ARCH: x64

  # stable 包 | 自有构建机 | darwin | x64
  ide_stable_win32_x64_self_hosted:
    name: ide_stable_win32_x64_self_hosted
    runs-on:
      cluster: icube.win32.x64.stable
      windows: {}
    image: none
    if: ${{ Event.Inputs.BUILD_QUALITY == "stable" && Event.Inputs.CUSTOM_BUILD_WINDOWS == "true" && Event.Inputs.CUSTOM_USE_SELF_HOSTED == "true" && Event.Inputs.CUSTOM_WIN32_USE_SELF_HOSTED == "true" }}
    steps:
      - commands:
        - if (Test-Path .git) { git clean -dfx } else { Write-Host "No git repository found" }
      - uses: actions/checkout
      - commands:
        - $PSVersionTable.PSVersion
        - node -v
        - $env:REPO_ROOT = Get-Location
        - node "$env:REPO_ROOT/build/ide/build_desktop.js"
    envs:
      <<: *common_envs
      PLATFORM: win32
      ARCH: x64
      BUILD_ARCH: x64
