use crate::agent::state::RunState;
use crate::graph::builtin_action::interrupt::Interrupt;
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::{
    collections::HashMap,
    fmt::Debug,
    sync::{<PERSON>, Mutex},
};
use thiserror::Error;

use super::message::Message;

#[derive(Debu<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ial<PERSON>, Deserialize)]
pub struct GraphState {
    pub last_node_name: String,
    pub next_node_name: String,
    pub next_node_input: serde_json::Value,
}

impl GraphState {
    pub fn new(node_name: &str) -> Self {
        Self {
            last_node_name: node_name.into(),
            next_node_name: node_name.into(),
            next_node_input: serde_json::Value::Null,
        }
    }
}

#[derive(Debug, <PERSON><PERSON>, Default, Serialize, Deserialize)]
pub struct CheckPoint<T, Config> {
    pub session_id: String,
    pub message_id: String,
    pub user_message_id: String,
    pub task_id: String,
    pub agent_run_ids: Vec<String>,
    pub state: RunState<T>,
    pub graph_state: GraphState,
    pub config: Arc<Config>,
    pub interrupt: Option<Interrupt>,
    pub history_pendings: Arc<Mutex<HashMap<String, Message>>>,
}

// impl<T, Config> Default for CheckPoint<T, Config>
// where
//     T: Debug + Default + Serialize + Clone,
//     Config: Debug + Default + Serialize + Clone,
// {
//     fn default() -> Self {
//         Self {
//             session_id: String::new(),
//             message_id: String::new(),
//             task_id: String::new(),
//             state: RunState::new(T::default()),
//             graph_state: GraphState {
//                 last_node_name: String::new(),
//                 next_node_name: String::new(),
//                 next_node_input: serde_json::Value::Null,
//             },
//             interrupt: None,
//             config: Arc::new(Config::default()),
//         }
//     }
// }

#[derive(Debug, Clone, Error, Serialize, Deserialize)]
pub enum CheckPointError {
    #[error("Failed to get checkpoint from task_id: {task_id:?}")]
    NotFound { task_id: String },
    #[error("IO error: {0}")]
    IOError(String),
    #[error("Deserialize error: {0}")]
    DeserializeError(String),
}

// checkpointer
#[async_trait]
pub trait CheckPointer<T, Config>: Send + Sync + 'static {
    async fn get(&self, task_id: &str) -> Result<CheckPoint<T, Config>, CheckPointError>;
    async fn save(&self, checkpoint: &CheckPoint<T, Config>) -> Result<(), CheckPointError>;
}

// task 唯一
// pub trait TaskStore: Send + Sync + 'static {
//     async fn get(&self, task_id: &str) -> Result<Execution<T, Config>, CheckPointError>;
//     async fn save(&self, execution: &Execution<T, Config>) -> Result<(), CheckPointError>;
// }
