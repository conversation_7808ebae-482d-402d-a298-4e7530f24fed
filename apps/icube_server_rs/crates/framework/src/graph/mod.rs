pub mod builtin_action;
mod builtin_handlers;
mod compiled_graph;
mod edge;
mod error;
mod fmt;
mod handler;
mod node;
mod state_graph;
#[cfg(test)]
mod tests;

pub use compiled_graph::CompiledGraph;
pub use node::NodeRef;
pub use node::{GraphNode, GraphNodeInvokeDecision, NodeId};
pub use state_graph::{AnyType, StateGraph};
use std::borrow::Cow;
pub mod debug_type_info;
mod route_builder;
pub mod router;

pub use builtin_handlers::interrupt::InterruptHandler;

pub use error::GraphError;

pub trait RunGraph<S, C>: Send + Sync {
    fn node_ref(&self, name: &str) -> Option<NodeRef>;
    fn start_node(&self) -> NodeRef;
    fn next_node(&self, current_index: NodeId) -> Option<NodeRef>;
    fn node_name(&self, node: NodeId) -> Cow<'static, str>;
    fn end_node(&self) -> NodeRef;
    fn error_node(&self) -> NodeRef;
    fn current_node(&self, node_index: NodeId) -> Option<&GraphNode<S, C>>;
}
