use std::{fmt::Display, ops::Deref};

use serde::{Deserialize, Serialize};
use serde_json::{json, Value};

#[derive(Debug, Serialize, Deserialize, <PERSON>lone, Default, PartialEq, Eq)]
pub struct FunctionCall {
    /// The name of the function to call.
    pub name: String,
    /// The arguments to call the function with, as generated by the model in JSON format. Note that the model does not always generate valid JSON, and may hallucinate parameters not defined by your function schema. Validate the arguments in your code before calling your function.
    pub arguments: serde_json::Value,
}

#[derive(Debug, Serialize, Deserialize, Clone, Default, PartialEq, Eq)]
pub struct ToolCall {
    pub id: String,   // gemini 没有这个字段
    pub index: usize, // NOTE: 推荐用这个字段进行唯一性匹配
    pub function: FunctionCall,
}

pub struct ToolCallArgument(Value);

impl ToolCallArgument {
    pub fn remove_thought(mut self) -> Value {
        let Some(obj) = self.0.as_object_mut() else {
            return self.0;
        };

        obj.remove("thought");
        self.0
    }
}

impl ToolCall {
    pub fn new(name: impl Into<String>) -> Self {
        Self {
            id: uuid::Uuid::new_v4().to_string(),
            function: FunctionCall {
                name: name.into(),
                arguments: Value::default(),
            },
            ..Default::default()
        }
    }
    pub fn with_arguments<T: Serialize>(mut self, arguments: T) -> Self {
        self.function.arguments = serde_json::to_value(arguments).unwrap_or_default();
        self
    }

    pub fn get_arguments(&self) -> ToolCallArgument {
        ToolCallArgument(self.function.arguments.clone())
    }

    pub fn get_instruction(&self) -> String {
        self.function
            .arguments
            .get("instruction")
            .and_then(|v| v.as_str())
            .unwrap_or_default()
            .into()
    }
}

impl<T> From<T> for ToolCall
where
    T: Into<String>,
{
    fn from(name: T) -> Self {
        Self {
            id: uuid::Uuid::new_v4().to_string(),
            function: FunctionCall {
                name: name.into(),
                arguments: json!({}),
            },
            ..Default::default()
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum Message {
    HumanMessage(MessageContents),
    AIAssistant {
        content: MessageContents,
        tool_calls: Option<Vec<ToolCall>>,
    },
    SystemMessage(MessageContents),
    ToolMessage {
        toolcall: ToolCall,
        result: Value,
    },
}

impl From<MessageContents> for String {
    fn from(value: MessageContents) -> Self {
        value.content_or_default().to_string()
    }
}
impl From<&MessageContents> for String {
    fn from(value: &MessageContents) -> Self {
        value.content_or_default().to_string()
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct MessageImageSize {
    pub width: i32,
    pub height: i32,
}
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct MessageImage {
    pub url: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub size: Option<MessageImageSize>,
}
#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum MessageContent {
    Text(String),
    Image(MessageImage),
}
impl std::default::Default for MessageContent {
    fn default() -> Self {
        Self::Text(String::default())
    }
}

impl MessageContent {
    /// 兼容老的接口
    pub fn content_or_default(&self) -> &str {
        match self {
            MessageContent::Text(t) => t,
            MessageContent::Image(MessageImage { url, .. }) => url,
        }
    }
    pub fn image_url(&self) -> Option<&str> {
        match self {
            MessageContent::Image(MessageImage { url, .. }) => Some(url),
            _ => None,
        }
    }
}
impl Display for MessageContent {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.content_or_default())
    }
}
impl Deref for MessageContent {
    type Target = str;

    fn deref(&self) -> &Self::Target {
        match self {
            MessageContent::Text(t) => t,
            MessageContent::Image(MessageImage { url, .. }) => url,
        }
    }
}
#[derive(Debug, Serialize, Deserialize, Clone, Default)]
pub struct MessageContents(Vec<MessageContent>);
impl Deref for MessageContents {
    type Target = Vec<MessageContent>;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}
impl IntoIterator for MessageContents {
    type Item = MessageContent;
    type IntoIter = std::vec::IntoIter<Self::Item>;

    fn into_iter(self) -> Self::IntoIter {
        self.0.into_iter()
    }
}
impl FromIterator<MessageContent> for MessageContents {
    fn from_iter<T: IntoIterator<Item = MessageContent>>(iter: T) -> Self {
        Self(iter.into_iter().collect())
    }
}
impl MessageContents {
    /// 兼容老的接口
    pub fn content_or_default(&self) -> &str {
        self.first()
            .map(|c| c.content_or_default())
            .unwrap_or_default()
    }
}
impl Display for MessageContents {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.content_or_default())
    }
}
impl From<String> for MessageContents {
    fn from(value: String) -> Self {
        Self(vec![MessageContent::Text(value)])
    }
}
impl From<&str> for MessageContents {
    fn from(value: &str) -> Self {
        Self(vec![MessageContent::Text(value.into())])
    }
}
impl From<&String> for MessageContents {
    fn from(value: &String) -> Self {
        Self(vec![MessageContent::Text(value.into())])
    }
}
impl From<MessageContent> for MessageContents {
    fn from(value: MessageContent) -> Self {
        Self(vec![value])
    }
}
impl From<MessageImage> for MessageContents {
    fn from(value: MessageImage) -> Self {
        Self(vec![MessageContent::Image(value)])
    }
}
impl Message {
    pub fn content(&self) -> &str {
        match self {
            Message::HumanMessage(msg) => msg.content_or_default(),
            Message::AIAssistant { content, .. } => content.content_or_default(),
            Message::SystemMessage(msg) => msg.content_or_default(),
            Message::ToolMessage { result, .. } => result.as_str().unwrap(),
        }
    }
    pub fn tool_calls(&self) -> Option<&Vec<ToolCall>> {
        match self {
            Message::AIAssistant { tool_calls, .. } => tool_calls.as_ref(),
            _ => None,
        }
    }

    pub fn new_human_message(msg: impl Into<MessageContents>) -> Self {
        Self::HumanMessage(msg.into())
    }

    pub fn new_ai_message(content: impl Into<MessageContents>) -> Self {
        Self::AIAssistant {
            content: content.into(),
            tool_calls: None,
        }
    }

    pub fn with_tool_calls<T>(mut self, calls: Option<Vec<T>>) -> Self
    where
        T: Into<ToolCall>,
    {
        if let Self::AIAssistant {
            ref mut tool_calls, ..
        } = self
        {
            *tool_calls = calls.map(|c| c.into_iter().map(Into::into).collect());
        }
        self
    }

    /// tool_calls 仅保留指定 id 的项，并且将 index 设置为 0
    pub fn retain_tool_calls_by_id(&mut self, id: &str) {
        if let Self::AIAssistant { tool_calls, .. } = self {
            if let Some(tool_calls) = tool_calls.as_mut() {
                tool_calls.retain(|tc| tc.id == id);
                tool_calls.iter_mut().for_each(|tc| {
                    tc.index = 0;
                });
            }
        }
    }

    pub fn new_tool_message(toolcall: impl Into<ToolCall>, result: impl Into<Value>) -> Self {
        Self::ToolMessage {
            toolcall: toolcall.into(),
            result: result.into(),
        }
    }

    pub fn new_system_message(msg: impl Into<MessageContents>) -> Self {
        Self::SystemMessage(msg.into())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_retain_tool_calls_by_id() {
        // 不会有报错
        let mut msg = Message::new_ai_message("just for test");
        msg.retain_tool_calls_by_id("test");

        // 不会有报错
        let mut msg = Message::new_ai_message("just for test");
        msg.with_tool_calls::<ToolCall>(Some(vec![]))
            .retain_tool_calls_by_id("test");

        let mut msg = Message::new_ai_message("just for test");
        let mut msg = msg.with_tool_calls(Some(vec![
            ToolCall {
                id: "111".to_string(),
                index: 0,
                function: Default::default(),
            },
            ToolCall {
                id: "222".to_string(),
                index: 1,
                function: Default::default(),
            },
            ToolCall {
                id: "333".to_string(),
                index: 2,
                function: Default::default(),
            },
        ]));
        // 保留 222
        msg.retain_tool_calls_by_id("222");
        assert_eq!(msg.tool_calls().map_or(0, |t| t.len()), 1);
        assert_eq!(
            msg.tool_calls().unwrap()[0],
            ToolCall {
                id: "222".to_string(),
                index: 0,
                function: Default::default(),
            }
        );
        // 保留 test，因为不存在实际上全部被删除了
        msg.retain_tool_calls_by_id("test");
        assert_eq!(msg.tool_calls().map_or(0, |t| t.len()), 0);
    }

    #[test]
    fn test_tool_call_argument() {
        let tool_call = ToolCall::new("example_tool")
            .with_arguments(json!({"instruction": "do something", "thought": "thinking"}));
        let argument = tool_call.get_arguments();
        let cleaned_argument = argument.remove_thought();

        assert_eq!(cleaned_argument, json!({"instruction": "do something"}));
        assert_eq!(
            cleaned_argument.to_string(),
            "{\"instruction\":\"do something\"}"
        )
    }
}
