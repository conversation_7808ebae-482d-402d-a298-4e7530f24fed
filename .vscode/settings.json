{
  "clangd.arguments": [
    "--compile-commands-dir=lib/filesync/build/Debug",
    "--pch-storage=memory",
    "-j=16"
  ],
  "cmake.configureOnOpen": false,
  "files.associations": {
    // cpp
    "optional": "cpp",
    "*.tcc": "cpp",
    "array": "cpp",
    "atomic": "cpp",
    "bit": "cpp",
    "bitset": "cpp",
    "cctype": "cpp",
    "charconv": "cpp",
    "chrono": "cpp",
    "clocale": "cpp",
    "cmath": "cpp",
    "codecvt": "cpp",
    "compare": "cpp",
    "complex": "cpp",
    "concepts": "cpp",
    "condition_variable": "cpp",
    "cstdarg": "cpp",
    "cstddef": "cpp",
    "cstdint": "cpp",
    "cstdio": "cpp",
    "cstdlib": "cpp",
    "cstring": "cpp",
    "ctime": "cpp",
    "cwchar": "cpp",
    "cwctype": "cpp",
    "deque": "cpp",
    "map": "cpp",
    "set": "cpp",
    "unordered_map": "cpp",
    "unordered_set": "cpp",
    "vector": "cpp",
    "exception": "cpp",
    "algorithm": "cpp",
    "functional": "cpp",
    "iterator": "cpp",
    "memory": "cpp",
    "memory_resource": "cpp",
    "numeric": "cpp",
    "random": "cpp",
    "ratio": "cpp",
    "regex": "cpp",
    "string": "cpp",
    "string_view": "cpp",
    "system_error": "cpp",
    "tuple": "cpp",
    "type_traits": "cpp",
    "utility": "cpp",
    "fstream": "cpp",
    "future": "cpp",
    "initializer_list": "cpp",
    "iomanip": "cpp",
    "iosfwd": "cpp",
    "iostream": "cpp",
    "istream": "cpp",
    "limits": "cpp",
    "mutex": "cpp",
    "new": "cpp",
    "ostream": "cpp",
    "ranges": "cpp",
    "span": "cpp",
    "sstream": "cpp",
    "stdexcept": "cpp",
    "stop_token": "cpp",
    "streambuf": "cpp",
    "thread": "cpp",
    "cinttypes": "cpp",
    "typeinfo": "cpp",
    "variant": "cpp",
    // common
    ".code-workspace": "jsonc",
    ".eslintrc": "jsonc",
    ".eslintrc*.json": "jsonc",
    ".stylelintrc": "jsonc",
    "stylelintrc": "jsonc",
    "rush.json": "jsonc",
    "README": "markdown"
  },
  "search.useIgnoreFiles": true,
  "search.exclude": {
    "**/dist": true,
    "**/*.log": true,
    "**/*.pid": true,
    "**/.git": true,
    "**/node_modules": true,
    "extensions/icube-ai/**": true,
    "lib/vscode-types/**": true,
    "lib/vscode/cli/target/**": true,
    "lib/vscode/.build/**": true,
    "lib/vscode/out/**": true,
    "lib/vscode/out-build/**": true,
    "lib/vscode/out-vscode/**": true,
    "lib/vscode/i18n/**": true,
    "lib/vscode/extensions/**/dist/**": true,
    "lib/vscode/extensions/**/out/**": true,
    "lib/vscode/test/smoke/out/**": true,
    "lib/vscode/test/automation/out/**": true,
    "lib/vscode/test/integration/browser/out/**": true,
    "lib/vscode/src/vs/base/test/common/filters.perf.data.js": true,
    "lib/vscode/src/vs/base/test/node/uri.perf.data.txt": true,
    "lib/vscode/src/vs/workbench/api/test/browser/extHostDocumentData.test.perf-data.ts": true,
    "lib/vscode/src/vs/base/test/node/uri.test.data.txt": true,
    "lib/vscode/src/vs/editor/test/node/diffing/fixtures/**": true,
    "lib/vscode/build/loader.min": true,
    "lib/virtua/lib": true,
    "apps/icube_server_rs/crates/manager/modules/**": true,
    // 以下四个都是 symbol link
    "lib/vscode/extensions/icube-ai-completion/**": true,
    "lib/vscode/extensions/icube-ai-completion-win/**": true,
    "lib/vscode/extensions/icube-manager/**": true,
    "lib/vscode/extensions/icube-manager-win/**": true
  },
  "files.watcherExclude": {
    "**/.git/objects/**": true,
    "**/.git/subtree-cache/**": true,
    "**/node_modules/*/**": true,
    "**/.hg/store/**": true,
    "**/log/**": true,
    "**/target/**": true,
    "**/temp/**": true,
    "**/out*/**": true
  },
  "files.readonlyInclude": {
    "lib/vscode/extensions/theme-seti/icons/vs-seti-icon-theme.json": true,
    "lib/vscode/src/vscode-dts/vscode.d.ts": true,
    "lib/vscode-loc/i18n/**/*.i18n.json": true,
    "lib/vscode-codicons/src/icons/*.svg": true
  },
  //
  "editor.rulers": [80, 120],
  "files.eol": "\n",
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,
  "cSpell.diagnosticLevel": "Hint",
  "eslint.run": "onType",
  "eslint.probe": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue"
  ],
  "eslint.format.enable": true,
  "eslint.lintTask.enable": true,
  "javascript.validate.enable": false,
  "typescript.validate.enable": true,
  "css.validate": false,
  "scss.validate": false,
  "less.validate": false,
  "[css]": {
    "editor.formatOnType": false,
    "editor.formatOnPaste": false,
    "editor.formatOnSave": false,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[scss]": {
    "editor.formatOnType": true,
    "editor.formatOnPaste": true,
    "editor.formatOnSave": true
  },
  "[less]": {
    "editor.formatOnType": true,
    "editor.formatOnPaste": true,
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "editor.defaultFormatter": "dbaeumer.vscode-eslint",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "json.format.enable": false,
  "[json]": {
    "editor.tabSize": 2,
    "editor.formatOnType": true,
    "editor.formatOnPaste": true,
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[jsonc]": {
    "editor.tabSize": 2,
    "editor.formatOnType": true,
    "editor.formatOnPaste": true,
    "editor.formatOnSave": true,
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "emmet.triggerExpansionOnTab": true,
  "typescript.tsdk": "lib/vscode/node_modules/typescript/lib",
  "byted-ide.dev-environment-manager.showForwardPortNotification": "hidden",
  "cSpell.words": [
    "allowpopups",
    "Appkit",
    "bnpm",
    "byted",
    "Edenx",
    "iconbox",
    "icube",
    "marscode",
    "Slardar",
    "trae"
  ],
  "[typescriptreact]": {
    "editor.defaultFormatter": "vscode.typescript-language-features",
    "editor.formatOnSave": true
  },
  "editor.accessibilityPageSize": 12,
  "[rust]": {
    "editor.defaultFormatter": "rust-lang.rust-analyzer",
    "editor.formatOnSave": true
  },
  "[xml]": {
    "editor.defaultFormatter": "redhat.vscode-xml"
  },
  "css.format.spaceAroundSelectorSeparator": true,
  "[typescript]": {
    "editor.defaultFormatter": "vscode.typescript-language-features",
    "editor.formatOnSave": true
  },
  "[javascript]": {
    "editor.defaultFormatter": "vscode.typescript-language-features",
    "editor.formatOnSave": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "typescript.preferences.quoteStyle": "single",
  "typescript.tsc.autoDetect": "off",
  "typescript.enablePromptUseWorkspaceTsdk": false,
  "javascript.format.enable": true,
  "typescript.format.enable": true,
  "[postcss]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[plaintext]": {
    "files.insertFinalNewline": false
  },
  "cssVariables.lookupFiles": ["modules/color-tokens/**/*.css"],
  "[markdown]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[svg]": {
    "editor.defaultFormatter": "jock.svg"
  },
  "typescript.preferences.autoImportSpecifierExcludeRegexes": [
    "\\.\\./vscode-types"
  ],
  "javascript.preferences.autoImportSpecifierExcludeRegexes": [
    "\\.\\./vscode-types"
  ],
  "git.branchProtection": ["main", "native", "integration_*"],
  "git.branchProtectionPrompt": "alwaysCommitToNewBranch",
  "git.branchRandomName.enable": true,
  "git.pullBeforeCheckout": true,
  "rust-analyzer.cargo.extraEnv": {
    "TTNET_LIB_DIR_PATH": "${workspaceFolder}/apps/icube_server_rs/modules/ai-agent/deps/ttnet/macos/m1"
  },
  "rust-analyzer.runnables.extraEnv": {
    "TTNET_LIB_DIR_PATH": "${workspaceFolder}/apps/icube_server_rs/modules/ai-agent/deps/ttnet/macos/m1"
  },
  "rust-analyzer.rustfmt.extraArgs": ["+nightly"]
}
