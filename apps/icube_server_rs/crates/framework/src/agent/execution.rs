use std::collections::HashMap;
use std::sync::{<PERSON>, Mutex};

use crate::agent::context::SessionRef;
use crate::agent::state::RunState;
use crate::core::agent::{DynRunTaskRequest, SessionContext};
use crate::core::artifact::Artifacts;
use crate::core::checkpoint::{CheckPoint, GraphState};
use crate::core::error::AgentError;
use crate::core::message::{Message, MessageContent, MessageContents};
use crate::graph::builtin_action::interrupt::{Interrupt, UserAction};
use serde::{Deserialize, Serialize};
use serde_json::Value;
use tokio_util::sync::CancellationToken;

#[derive(Debug, Default)]
pub struct RunTaskRequest<T, Config> {
    pub(super) message: MessageContents,
    pub(super) initial_state: T,
    pub(super) config: Config,
    pub(super) history: Vec<Message>,
    pub(super) artifacts: Artifacts,
    pub(super) cancellation_token: Option<CancellationToken>,
    pub(super) session_context: SessionContext,
    pub(super) agent_run_ids: Vec<String>,
    pub(super) turns: u32,
}
impl<T: Default, Config: Default> From<String> for RunTaskRequest<T, Config> {
    fn from(value: String) -> Self {
        Self::new(value)
    }
}
impl<T: Default, Config: Default> From<&String> for RunTaskRequest<T, Config> {
    fn from(value: &String) -> Self {
        Self::new(value)
    }
}
impl<T: Default, Config: Default> From<&str> for RunTaskRequest<T, Config> {
    fn from(value: &str) -> Self {
        Self::new(value)
    }
}
impl<T: Default, Config: Default> From<MessageContent> for RunTaskRequest<T, Config> {
    fn from(value: MessageContent) -> Self {
        Self::new(value)
    }
}
impl<T: Default, Config: Default> From<MessageContents> for RunTaskRequest<T, Config> {
    fn from(value: MessageContents) -> Self {
        Self::new(value)
    }
}
impl<T, Config> RunTaskRequest<T, Config>
where
    Config: Default,
    T: Default,
{
    pub fn new(message: impl Into<MessageContents>) -> Self {
        Self {
            message: message.into(),
            ..Default::default()
        }
    }
    pub fn with_initial_state(mut self, state: T) -> Self {
        self.initial_state = state;
        self
    }
    pub fn with_config(mut self, config: Config) -> Self {
        self.config = config;
        self
    }
    pub fn with_history(mut self, history: Vec<Message>) -> Self {
        self.history = history;
        self
    }
    pub fn with_artifacts(mut self, artifacts: Artifacts) -> Self {
        self.artifacts = artifacts;
        self
    }
    pub fn with_cancellation_token(mut self, token: CancellationToken) -> Self {
        self.cancellation_token = Some(token);
        self
    }
    pub fn with_session_context(mut self, session_context: SessionContext) -> Self {
        self.session_context = session_context;
        self
    }
    pub fn with_initial_turns(mut self, turns: u32) -> Self {
        self.turns = turns;
        self
    }
}

impl<T, Config> TryFrom<DynRunTaskRequest> for RunTaskRequest<T, Config>
where
    T: Default + 'static,
    Config: Default + 'static,
{
    type Error = AgentError;
    fn try_from(value: DynRunTaskRequest) -> Result<Self, Self::Error> {
        let mut request: RunTaskRequest<T, Config> = RunTaskRequest::new(value.message);

        if let Some(initial_state_data) = value.initial_state {
            request.initial_state = initial_state_data.downcast::<T>().map_err(|e| {
                AgentError::InternalError(format!("Failed to downcast initial_state: {}", e))
            })?;
        }

        if let Some(config_data) = value.config {
            request.config = config_data.downcast::<Config>().map_err(|e| {
                AgentError::InternalError(format!("Failed to downcast config: {}", e))
            })?;
        }

        request.history = value.history;
        request.artifacts = value.artifacts;
        request.cancellation_token = value.cancellation_token;
        request.session_context = value.session_context;
        request.turns = value.turns;
        request.agent_run_ids = value.agent_run_ids;
        Ok(request)
    }
}

#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ExecutionStatus {
    Pending,
    Running,
    Completed,
    Failed,
    Interrupted,
    Canceled,
}

#[derive(Debug)]
pub struct Execution<S, Config> {
    pub id: String,
    pub session_id: String,
    pub message_id: String,
    pub user_message_id: String,
    pub agent_run_ids: Vec<String>,
    pub state: RunState<S>,
    pub status: ExecutionStatus,
    pub graph_state: GraphState,
    pub interrupt: Option<Interrupt>,
    pub config: Arc<Config>,
    pub history_pendings: Arc<Mutex<HashMap<String, Message>>>,
}

pub struct ExecutionBuilder<S, C> {
    state: Option<RunState<S>>,
    graph_state: Option<GraphState>,
    session_id: Option<String>,
    message_id: Option<String>,
    user_message_id: Option<String>,
    agent_run_ids: Option<Vec<String>>,
    config: Option<C>,
    history_pendings: Option<Arc<Mutex<HashMap<String, Message>>>>,
}

impl<S, C> Default for ExecutionBuilder<S, C> {
    fn default() -> Self {
        Self {
            state: None,
            graph_state: None,
            session_id: None,
            message_id: None,
            user_message_id: None,
            config: None,
            history_pendings: None,
            agent_run_ids: None,
        }
    }
}

impl<S: Clone, C: Default> ExecutionBuilder<S, C> {
    pub fn state(mut self, state: RunState<S>) -> Self {
        self.state = Some(state);
        self
    }

    pub fn graph_state(mut self, state: GraphState) -> Self {
        self.graph_state = Some(state);
        self
    }

    pub fn session_id(mut self, id: impl Into<String>) -> Self {
        self.session_id = Some(id.into());
        self
    }

    pub fn message_id(mut self, id: impl Into<String>) -> Self {
        self.message_id = Some(id.into());
        self
    }

    pub fn user_message_id(mut self, id: impl Into<String>) -> Self {
        self.user_message_id = Some(id.into());
        self
    }

    pub fn config(mut self, cfg: C) -> Self {
        self.config = Some(cfg);
        self
    }

    pub fn agent_run_ids(mut self, agent_run_ids: Vec<String>) -> Self {
        self.agent_run_ids = Some(agent_run_ids);
        self
    }

    pub fn with_history_pending(
        mut self,
        history_pendings: Arc<Mutex<HashMap<String, Message>>>,
    ) -> Self {
        self.history_pendings = Some(history_pendings);
        self
    }

    pub fn build(self) -> Result<Execution<S, C>, &'static str> {
        Ok(Execution {
            id: uuid::Uuid::new_v4().to_string(),
            session_id: self.session_id.ok_or("session_id missing")?,
            message_id: self.message_id.ok_or("message_id missing")?,
            user_message_id: self.user_message_id.ok_or("user_message_id missing")?,
            state: self.state.ok_or("state missing")?,
            graph_state: self.graph_state.ok_or("graph_state missing")?,
            status: ExecutionStatus::Running,
            interrupt: None,
            config: Arc::new(self.config.unwrap_or_default()),
            history_pendings: self.history_pendings.unwrap_or_default(),
            agent_run_ids: self.agent_run_ids.unwrap_or_default(),
        })
    }
}

impl<S: Clone, Config: Default> Execution<S, Config> {
    pub fn builder() -> ExecutionBuilder<S, Config> {
        ExecutionBuilder::default()
    }

    pub(crate) fn get_session_ref(&self) -> SessionRef {
        SessionRef::new(
            &self.id,
            &self.session_id,
            &self.message_id,
            &self.user_message_id,
            self.agent_run_ids.clone(),
        )
    }
    // pub fn new(state: RunState<S>, graph_state: GraphState) -> Self {
    //     let task_id = uuid::Uuid::new_v4().to_string();
    //     Self {
    //         id: task_id,
    //         interrupt: None,
    //         graph_state,
    //         state,
    //         status: ExecutionStatus::Running,
    //         session_id: "".to_string(),
    //         config: Arc::new(Config::default()),
    //     }
    // }
    // pub fn with_config(&mut self, config: Config) {
    //     self.config = Arc::new(config);
    // }

    pub fn to_cancel(&mut self) {
        if matches!(
            self.status,
            ExecutionStatus::Pending | ExecutionStatus::Running
        ) {
            self.status = ExecutionStatus::Canceled;
        } else {
            panic!("can not cancel done task by status: {:?}", self.status);
        }
    }

    pub fn resume(&mut self, user_action: UserAction) {
        self.status = ExecutionStatus::Running;
        if let Some(ref mut intr) = self.interrupt {
            intr.answer = Some(user_action);
        }
    }

    pub fn restore(checkpoint: CheckPoint<S, Config>) -> Self {
        Self {
            interrupt: checkpoint.interrupt,
            config: checkpoint.config,
            status: ExecutionStatus::Interrupted,
            graph_state: checkpoint.graph_state,
            state: checkpoint.state,
            session_id: checkpoint.session_id,
            message_id: checkpoint.message_id,
            user_message_id: checkpoint.user_message_id,
            id: checkpoint.task_id,
            history_pendings: checkpoint.history_pendings,
            agent_run_ids: checkpoint.agent_run_ids,
        }
    }

    pub fn entry_point(&self) -> &str {
        &self.graph_state.next_node_name
    }

    pub fn get_start_input(&self) -> Result<Value, AgentError> {
        let mut input = self.graph_state.next_node_input.clone();
        if let Some(interrupt) = &self.interrupt {
            input = serde_json::to_value(interrupt)?;
        }
        Ok(input)
    }

    pub fn set_interrupt(&mut self, intr: &Interrupt) {
        self.status = ExecutionStatus::Interrupted;
        self.interrupt = Some(intr.clone());
    }

    pub fn set_graph_state(&mut self, state: GraphState) {
        self.graph_state = state;
    }
    pub fn set_history_pending(&mut self, history_pendings: Arc<Mutex<HashMap<String, Message>>>) {
        self.history_pendings = history_pendings;
    }

    pub fn make_checkpoint(&self) -> CheckPoint<S, Config> {
        CheckPoint {
            session_id: self.session_id.clone(),
            message_id: self.message_id.clone(),
            user_message_id: self.user_message_id.clone(),
            task_id: self.id.clone(),
            state: self.state.clone(),
            interrupt: self.interrupt.clone(),
            config: self.config.clone(),
            graph_state: self.graph_state.clone(),
            history_pendings: self.history_pendings.clone(),
            agent_run_ids: self.agent_run_ids.clone(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::graph::builtin_action::interrupt::{Decision, InterruptReason, Question};
    use serde_json::json;

    fn make_graph_state() -> GraphState {
        GraphState {
            last_node_name: "last_node".into(),
            next_node_name: "start".into(),
            next_node_input: json!({"foo": 42}),
        }
    }

    fn make_run_state() -> RunState<()> {
        RunState::new(())
    }

    fn make_interruption() -> Interrupt {
        Interrupt::new(
            // "message_id",
            "task_id",
            InterruptReason::AskUser(Question {
                task_id: "sub_task".into(),
                agent_name: "agent_name".into(),
                tool_call: Default::default(),
                message: None,
                payload: None,
            }),
            Default::default(),
        )
    }

    fn make_execution() -> Execution<(), ()> {
        Execution::builder()
            .state(make_run_state())
            .graph_state(make_graph_state())
            .session_id("session_123")
            .message_id("message_123")
            .user_message_id("user_message_123")
            .config(())
            .build()
            .unwrap()
    }

    #[test]
    fn new_initialises_expected_fields() {
        let gs = make_graph_state();
        let state = make_run_state();
        let exec = Execution::builder()
            .state(state.clone())
            .graph_state(gs.clone())
            .session_id("session_123")
            .message_id("message_123")
            .user_message_id("user_message_123")
            .config(())
            .build()
            .unwrap();

        assert_eq!(exec.status, ExecutionStatus::Running);
        assert!(exec.interrupt.is_none());
        assert_eq!(exec.graph_state.next_node_name, gs.next_node_name);
        assert_eq!(exec.graph_state.next_node_input, gs.next_node_input);
    }

    #[test]
    fn get_start_input_prefers_interrupt_payload() {
        let mut exec = make_execution();

        assert_eq!(
            exec.get_start_input().unwrap(),
            exec.graph_state.next_node_input
        );

        let intr = make_interruption();

        exec.set_interrupt(&intr);
        let payload = exec.get_start_input().unwrap();
        assert_ne!(payload, exec.graph_state.next_node_input);
    }

    #[test]
    fn set_interrupt_switches_status() {
        let mut exec = make_execution();

        let intr = make_interruption();
        exec.set_interrupt(&intr);
        assert_eq!(exec.status, ExecutionStatus::Interrupted);
        assert!(exec.interrupt.is_some());
    }

    #[test]
    fn resume_clears_interrupt_and_sets_answer() {
        let mut exec = make_execution();
        let intr = make_interruption();
        exec.set_interrupt(&intr);

        let answer = UserAction {
            decision: Decision::Approve,
            message: Some("User approved".into()),
            payload: None,
        };
        exec.resume(answer);

        assert_eq!(exec.status, ExecutionStatus::Running);
        assert!(
            matches!(exec.interrupt, Some(ref i) if i.answer.is_some()),
            "resume must inject the answer into the stored interrupt"
        );
    }

    #[test]
    fn checkpoint_roundtrip_restores_all_fields() {
        let mut exec = make_execution();
        let intr = make_interruption();
        exec.set_interrupt(&intr);

        let cp = exec.make_checkpoint();
        let copy = Execution::restore(cp.clone());

        assert_eq!(copy.status, ExecutionStatus::Interrupted);
        assert_eq!(copy.session_id, cp.session_id);
        assert_eq!(copy.id, cp.task_id);
    }
}
