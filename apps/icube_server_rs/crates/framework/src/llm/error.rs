use async_openai::error::OpenAIError;
use reqwest::Error as ReqwestError;
use serde::{Deserialize, Serialize};
use thiserror::Error;

use crate::language_models::deepseek::DeepseekError;
use crate::language_models::openrouter::OpenRouterError;

#[derive(<PERSON><PERSON><PERSON>, Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum LLMError {
    #[error("OpenAI error: {0}")]
    OpenAIError(String),

    #[error("Deepseek error: {0}")]
    DeepseekError(#[from] DeepseekError),

    #[error("OpenRouter error: {0}")]
    OpenRouterError(#[from] OpenRouterError),

    #[error("LLMRawChat error: code: {code}, message: {message}")]
    LLMRawChatError {
        code: i32,
        message: String,
        data: Option<serde_json::Value>,
    },

    #[error("RepairJSON error: {message}, should_fix: {should_fix}")]
    RepairJSONError {
        message: String,
        should_fix: bool,
        should_retry: bool,
    },

    #[error("PromptTooLong error: {message}")]
    PromptTooLongError {
        message: String,
        should_retry: bool,
    },

    #[error("Network request failed: {0}")]
    RequestError(String),

    #[error("Parsing error: {0}")]
    ParsingError(String),

    #[error("Error: {0}")]
    OtherError(String),

    #[error("Request cancelled")]
    RequestCancelled,

    #[error("invalid run configuration")]
    InvalidRunConfig(String),
}
impl LLMError {
    pub fn is_json_invalid_retry(&self) -> bool {
        if let LLMError::RepairJSONError { should_retry, .. } = self {
            *should_retry
        } else {
            false
        }
    }

    pub fn is_json_invalid_need_fix(&self) -> bool {
        if let LLMError::RepairJSONError { should_fix, .. } = self {
            *should_fix
        } else {
            false
        }
    }
}

impl From<ReqwestError> for LLMError {
    fn from(e: ReqwestError) -> Self {
        Self::RequestError(e.to_string())
    }
}

impl From<OpenAIError> for LLMError {
    fn from(e: OpenAIError) -> Self {
        Self::OpenAIError(e.to_string())
    }
}
