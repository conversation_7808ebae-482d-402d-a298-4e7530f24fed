use builder::multi_agent;
use dotenv::dotenv;
use framework::agent::{context::CallingContext, state::RunState};
use serde::{Deserialize, Serialize};

pub mod agent;
pub mod builder;
pub mod cli;
pub mod factory;
pub mod supervisor;
pub mod tools;
pub mod utils;

#[tokio::main]
async fn main() {
    let p = dotenv().ok();
    println!("{:?}", p);
    let s = multi_agent().await;
    // resume().await;
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Default)]
pub struct UserState {
    pub foo: String,
}

/// 用户自定义state，可以在 handler 中进行修改
pub type SubAgentState = RunState<UserState>;
pub type SupervisorState = RunState<()>;
pub type Context = CallingContext<Config>;
pub type Config = ();

// Error type for compatibility
type Result<T> = std::result::Result<T, Box<dyn std::error::Error>>;
