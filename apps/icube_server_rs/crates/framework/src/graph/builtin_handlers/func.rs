use crate::core::{error::AgentError, handler::<PERSON><PERSON>and<PERSON>};
use serde::de::DeserializeOwned;
use serde::Serialize;
use std::future::Future;

pub struct FnHandler<F, In, Out> {
    name: String,
    f: F,
    _marker: std::marker::PhantomData<(In, Out)>,
}

impl<F, In, Out> FnHandler<F, In, Out> {
    #[allow(dead_code)]
    pub fn new(name: &str, f: F) -> Self {
        Self {
            name: name.to_string(),
            f,
            _marker: std::marker::PhantomData,
        }
    }
}

#[async_trait::async_trait]
impl<S, C, In, Out, F, Fut> StageHandler<S, C> for FnHandler<F, In, Out>
where
    S: Send + Sync,
    C: Send + Sync + 'static,
    In: DeserializeOwned + Send + Sync + 'static,
    Out: Serialize + Send + Sync + 'static,
    F: Fn(&mut S, In) -> Fut + Send + Sync + 'static,
    Fut: Future<Output = Result<Out, AgentError>> + Send,
{
    type Incoming = In;
    type Outgoing = Out;

    fn name(&self) -> std::borrow::Cow<'static, str> {
        self.name.clone().into()
    }

    async fn handle(
        &self,
        _ctx: C,
        state: &mut S,
        input: Self::Incoming,
    ) -> Result<Self::Outgoing, AgentError> {
        (self.f)(state, input).await
    }
}

#[cfg(test)]
mod tests {
    use crate::graph::{RunGraph, StateGraph};

    #[test]
    fn test_fn_handler() {
        use super::*;

        let handler = FnHandler::new("test", |_state: &mut (), input: String| async move {
            Ok(input.to_uppercase())
        });

        let mut graph = StateGraph::<(), String>::new();
        let first = graph.add_node(handler).expect("Failed to add handler");

        let compiled = graph.compile().expect("Failed to compile graph");
        let start = compiled.start_node();
        let start_node_name = compiled.node_name(start.index);
        assert_eq!(start_node_name, "__START__");

        let mut next = compiled.next_node(start.index);
        assert_eq!(next.unwrap().index, first.get_ref().index);
        next = compiled.next_node(next.unwrap().index);
        let end_node_name = compiled.node_name(next.unwrap().index);
        assert_eq!(end_node_name, "__END__");
    }
}
