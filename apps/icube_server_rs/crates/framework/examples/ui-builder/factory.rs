use std::borrow::Cow;

use crate::agent::resume::ResumeHandler;
use crate::agent::CallToolDecision;
use crate::supervisor::{CallAgentDecision, ResumeAction};
use crate::{Config, SubAgentState, SupervisorState, UserState};
use framework::agent::context::CallingContext;
use framework::EnumMessage;
use framework::{
    agent::{builder::GraphBuilder, state::RunState},
    enum_map,
    graph::{router::IntoRouteValue, CompiledGraph, GraphError, StateGraph},
    Enum,
};

pub struct SupervisorGraph;
pub struct AgentGraph;

impl GraphBuilder<SupervisorState, Config> for SupervisorGraph {
    fn build_graph(
        self,
        name: Option<Cow<'static, str>>,
    ) -> Result<CompiledGraph<SupervisorState, CallingContext<Config>>, GraphError> {
        let mut graph = StateGraph::new();
        if let Some(name) = name {
            graph.with_name(name);
        }
        let idle = graph.add_node(super::supervisor::idle::Idle<PERSON>tateHandler)?;
        let planning = graph.add_node(super::supervisor::planning::PlanningStateHandler {})?;
        let agent_call = graph.add_node(super::supervisor::agent::AgentCallHandler)?;
        let tool_call = graph.add_node(super::supervisor::call_tool::ToolCallHandler)?;
        let shutdown = graph.add_node(super::supervisor::shutdown::ShutdownStateHandler {})?;
        let interrupt = graph.add_interrupt_node(super::supervisor::resume::ResumeHandler)?;

        graph.connect(&idle, &planning)?;
        graph.connect(&tool_call, &planning)?;

        graph
            .switch("should_call_agent")
            .from(&planning)
            .case(
                |run_state, input| input.call_agent.as_ref().cloned(),
                &agent_call,
            )
            .case(
                |run_state, input| input.call_tool.as_ref().cloned(),
                &tool_call,
            )
            .case(
                |run_state, _| (run_state.turns < 10).then_some(()),
                &planning,
            )
            .default(&shutdown)
            .build()?;

        #[derive(Clone, Enum, EnumMessage)]
        enum Route {
            #[strum(message = "callagent")]
            AgentCall,
            #[strum(message = "toolcall")]
            ToolCall,
            #[strum(message = "planning")]
            Planning,
            #[strum(message = "shutdown")]
            Shutdown,
            #[strum(message = "intr")]
            Intr,
        }

        graph.add_conditional_edges(
            "wait_or_finish",
            &agent_call,
            move |c, run_state, input| match input {
                CallAgentDecision::Finished => (Route::Planning, ()).into_route_value(),
                CallAgentDecision::Interrupt(intr) => (Route::Intr, intr).into_route_value(),
            },
            enum_map! {
                Route::Planning => planning.get_ref(),
                Route::Intr => interrupt.get_ref(),
                _ => shutdown.get_ref(),
            },
        )?;

        graph.add_conditional_edges(
            "resume",
            &interrupt,
            move |c, run_state, input| match input {
                ResumeAction::CallAgent(call) => (Route::AgentCall, call).into_route_value(),
                ResumeAction::CallTool(call) => (Route::ToolCall, call).into_route_value(),
                ResumeAction::RePlan => (Route::Planning, ()).into_route_value(),
            },
            enum_map! {
                Route::AgentCall => agent_call.get_ref(),
                Route::ToolCall => tool_call.get_ref(),
                Route::Planning => planning.get_ref(),
                _ => shutdown.get_ref(),
            },
        )?;

        graph.compile()
    }
}

impl GraphBuilder<SubAgentState, Config> for AgentGraph {
    fn build_graph(
        self,
        name: Option<Cow<'static, str>>,
    ) -> Result<CompiledGraph<SubAgentState, CallingContext<Config>>, GraphError> {
        let mut graph = StateGraph::new();
        if let Some(name) = name {
            graph.with_name(name);
        }
        let idle = graph.add_node(super::agent::idle::IdleStateHandler)?;
        let planning = graph.add_node(super::agent::planning::PlanningStateHandler {})?;
        let tool_call = graph.add_node(super::agent::call_tool::ToolCallHandler {})?;
        let finish = graph.add_node(super::agent::finishwork::FinishStateHandler {})?;
        let interrupt = graph.add_interrupt_node(ResumeHandler)?;

        graph.connect(&idle, &planning)?;
        graph.connect(&tool_call, &planning)?;

        #[derive(Clone, Enum)]
        enum Route {
            ToolCall,
            Planning,
            Finish,
            Intr,
        }

        graph.add_conditional_edges(
            "planning_router",
            &planning,
            move |c, run_state: &RunState<UserState>, input: CallToolDecision| match input {
                CallToolDecision::CallTool(call_tool) => {
                    (Route::ToolCall, call_tool).into_route_value()
                }
                CallToolDecision::WorkComplete(work_complete) => {
                    (Route::Finish, work_complete).into_route_value()
                }
                CallToolDecision::Interrupt(intr) => (Route::Intr, intr).into_route_value(),
                CallToolDecision::RePlan => {
                    if run_state.turns < 36 {
                        (Route::Planning, ()).into_route_value()
                    } else {
                        (Route::Finish, ()).into_route_value()
                    }
                }
            },
            enum_map! {
                Route::Planning => planning.get_ref(),
                Route::ToolCall => tool_call.get_ref(),
                Route::Finish => finish.get_ref(),
                Route::Intr => interrupt.get_ref(),
            },
        )?;

        graph.add_conditional_edges(
            "resume",
            &interrupt,
            move |c, run_state, input| match input {
                ResumeAction::CallTool(call) => (Route::ToolCall, call).into_route_value(),
                ResumeAction::RePlan => (Route::Planning, ()).into_route_value(),
                _ => (Route::Finish, ()).into_route_value(),
            },
            enum_map! {
                Route::ToolCall => tool_call.get_ref(),
                Route::Planning => planning.get_ref(),
                _ => finish.get_ref(),
            },
        )?;

        graph.compile()
    }
}
