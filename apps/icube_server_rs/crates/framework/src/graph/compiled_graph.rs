use std::{borrow::Cow, collections::HashMap, ops::Deref};

use super::{
    builtin_handlers::{end::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, error::<PERSON>rap<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, start::<PERSON>rap<PERSON><PERSON><PERSON>t<PERSON>and<PERSON>},
    edge::<PERSON>rap<PERSON><PERSON><PERSON>,
    node::{GraphNode, NodeId, NodeRef},
    RunGraph, StateGraph,
};
use crate::graph::error::GraphError;
use petgraph::graph::NodeIndex as GraphNodeIndex;
use petgraph::prelude::StableDiGraph;
use petgraph::visit::EdgeRef;

type GraphResult<T> = Result<T, GraphError>;

impl<S, C> RunGraph<S, C> for CompiledGraph<S, C> {
    fn node_ref(&self, name: &str) -> Option<NodeRef> {
        self.name_2_index_mapping.get(name).and_then(|index| {
            self.graph.node_weight(**index).map(|node| NodeRef {
                index: *index,
                input_type: node.input_type(),
                output_type: node.output_type(),
            })
        })
    }

    fn start_node(&self) -> NodeRef {
        self.start_node
    }

    fn next_node(&self, current_index: NodeId) -> Option<NodeRef> {
        let next_node = self.graph.edges(*current_index).find_map(|edge| {
            let next_index = edge.target();
            let edge_type = edge.weight();
            let type_id = match edge_type {
                GraphEdge::Direct(_, ty) => ty,
                GraphEdge::Branch { ty, .. } => ty,
                GraphEdge::Virtual => return None, // Skip virtual nodes
                GraphEdge::Async(_, ty) => ty,
            };
            Some(NodeRef {
                index: next_index.into(),
                input_type: *type_id,
                output_type: *type_id,
            })
        });
        next_node
    }

    fn node_name(&self, node: NodeId) -> Cow<'static, str> {
        let node = &self.graph[*node];
        node.name()
    }

    fn end_node(&self) -> NodeRef {
        self.end_node
    }

    fn error_node(&self) -> NodeRef {
        self.error_node
    }

    fn current_node(&self, node_index: NodeId) -> Option<&GraphNode<S, C>> {
        self.graph.node_weight(*node_index)
        // &self.graph[*node_index]
    }
}

pub struct CompiledGraph<S, C> {
    pub(super) name: Cow<'static, str>,
    pub(super) graph: StableDiGraph<GraphNode<S, C>, GraphEdge>,
    start_node: NodeRef,
    end_node: NodeRef,
    error_node: NodeRef,
    name_2_index_mapping: HashMap<Cow<'static, str>, NodeId>,
}

impl<S, C> CompiledGraph<S, C> {
    pub(super) fn new(
        name: impl Into<Cow<'static, str>>,
        graph: StableDiGraph<GraphNode<S, C>, GraphEdge>,
        start_node: NodeRef,
        end_node: NodeRef,
        error_node: NodeRef,
        name_2_index_mapping: HashMap<Cow<'static, str>, NodeId>,
    ) -> Self {
        Self {
            name: name.into(),
            graph,
            start_node,
            end_node,
            error_node,
            name_2_index_mapping,
        }
    }
}

impl<S, C> Deref for CompiledGraph<S, C> {
    type Target = StableDiGraph<GraphNode<S, C>, GraphEdge>;

    fn deref(&self) -> &Self::Target {
        &self.graph
    }
}

#[derive(Debug)]
struct CategorizedNodes {
    start_nodes: Vec<GraphNodeIndex>,
    end_nodes: Vec<GraphNodeIndex>,
    all_nodes: Vec<GraphNodeIndex>,
}

impl<GS, C> StateGraph<GS, C>
where
    GS: Send + Sync + 'static,
    C: Send + Sync + 'static,
{
    pub fn compile(mut self) -> Result<CompiledGraph<GS, C>, GraphError> {
        let categorized = self.validate_and_categorize_nodes()?;

        // Inject a start node handler and connect it to all original start nodes.
        let start_node_ref = self.inject_start_handler(&categorized.start_nodes)?;

        // Inject a single end handler and connect it.
        let end_node_ref = self.inject_end_handler(&categorized.end_nodes)?;

        // Inject a global error handler and connect it to all original nodes.
        let error_node_ref = self.inject_error_handler(&categorized.all_nodes)?;

        Ok(CompiledGraph::new(
            self.name,
            self.graph,
            start_node_ref,
            end_node_ref,
            error_node_ref,
            self.name_2_index_mapping,
        ))
    }

    fn get_node_ref(&self, idx: NodeId) -> Option<NodeRef> {
        self.graph.node_weight(idx.into()).map(|handler| NodeRef {
            index: idx,
            input_type: handler.input_type(),
            output_type: handler.output_type(),
        })
    }

    fn validate_and_categorize_nodes(&self) -> GraphResult<CategorizedNodes> {
        let node_count = self.graph.node_count();
        if node_count == 0 {
            return Err(GraphError::EmptyGraph);
        }

        let mut start_nodes = Vec::new();
        let mut end_nodes = Vec::new();
        let all_nodes = self.graph.node_indices().collect::<Vec<_>>();

        for node_index in &all_nodes {
            let incoming = self
                .graph
                .edges_directed(*node_index, petgraph::Direction::Incoming)
                .count();
            let outgoing = self
                .graph
                .edges_directed(*node_index, petgraph::Direction::Outgoing)
                .count();
            match (incoming, outgoing) {
                (0, 0) => {
                    // if node count > 1 and the node has no edges, it's an isolated node.
                    if node_count > 1 {
                        return Err(GraphError::IsolatedNodes(format!("{:?}", node_index)));
                    }
                    // For a single-node graph, it's both a start and an end.
                    start_nodes.push(*node_index);
                    end_nodes.push(*node_index);
                }
                (0, _) => start_nodes.push(*node_index), // No inputs -> start node
                (_, 0) => end_nodes.push(*node_index),   // No outputs -> end node
                _ => (),                                 // Intermediate node
            }
        }

        if start_nodes.is_empty() {
            return Err(GraphError::NoStartNode);
        }

        if start_nodes.len() > 1 {
            return Err(GraphError::MultipleStartNodes);
        }

        Ok(CategorizedNodes {
            start_nodes,
            end_nodes,
            all_nodes,
        })
    }

    fn inject_start_handler(
        &mut self,
        original_start_nodes: &[GraphNodeIndex],
    ) -> Result<NodeRef, GraphError> {
        let start_node_ref = self.try_add_node(GraphStartHandler)?;
        for &start_node_index in original_start_nodes {
            let original_start_node =
                self.get_node_ref(start_node_index.into()).ok_or_else(|| {
                    GraphError::NodeNotFound(format!(
                        "Start node({:?}) not found",
                        start_node_index
                    ))
                })?;
            self.graph.add_edge(
                start_node_ref.index.into(),
                original_start_node.index.into(),
                GraphEdge::Direct(start_node_ref.output_type, original_start_node.input_type),
            );
        }
        Ok(start_node_ref)
    }

    fn inject_end_handler(
        &mut self,
        original_end_nodes: &[GraphNodeIndex],
    ) -> Result<NodeRef, GraphError> {
        let end_handler = GraphEndHandler::new(self.finish_handler.take());
        let end_node_ref = self.try_add_node(end_handler)?;
        for &end_node_index in original_end_nodes {
            let original_end_node = self.get_node_ref(end_node_index.into()).ok_or_else(|| {
                GraphError::NodeNotFound(format!("End node({:?}) not found", end_node_index))
            })?;
            self.graph.add_edge(
                original_end_node.index.into(),
                end_node_ref.index.into(),
                GraphEdge::Direct(original_end_node.output_type, end_node_ref.input_type),
            );
        }
        Ok(end_node_ref)
    }

    fn inject_error_handler(
        &mut self,
        all_original_nodes: &[GraphNodeIndex],
    ) -> Result<NodeRef, GraphError> {
        let error_handler = GraphErrorHandler::new(self.error_handler.take());
        let error_node_ref = self.try_add_node(error_handler)?;
        for &node_index in all_original_nodes {
            let node = self.get_node_ref(node_index.into()).ok_or_else(|| {
                GraphError::NodeNotFound(format!("Error node({:?}) not found", node_index))
            })?;
            self.graph.add_edge(
                node.index.into(),
                error_node_ref.index.into(),
                GraphEdge::Virtual,
            );
        }
        Ok(error_node_ref)
    }

    // pub fn compile(mut self) -> Result<CompiledGraph<GS, C>, GraphError> {
    //     let mut start_nodes = Vec::new();
    //     let mut end_nodes = Vec::new();
    //     let mut isolated_nodes = Vec::new();
    //     let mut all_nodes = Vec::new();
    //
    //     // 检查每个节点的连接情况
    //     for node_index in self.graph.node_indices() {
    //         let incoming_edges = self
    //             .graph
    //             .edges_directed(node_index, petgraph::Direction::Incoming)
    //             // .filter(|e| matches!(e.weight(), GraphEdge::Direct(_, _)))
    //             .count();
    //         let outgoing_edges = self
    //             .graph
    //             .edges_directed(node_index, petgraph::Direction::Outgoing)
    //             .count();
    //
    //         all_nodes.push(node_index);
    //         dbg!(incoming_edges, outgoing_edges, node_index);
    //         match (incoming_edges, outgoing_edges) {
    //             (0, 0) => {
    //                 // 没有任何边连接的孤立节点
    //                 isolated_nodes.push(node_index);
    //             }
    //             (0, _) => {
    //                 // 没有入边，只有出边的开始节点
    //                 start_nodes.push(node_index);
    //             }
    //             (_, 0) => {
    //                 // 没有出边，只有入边的结束节点
    //                 end_nodes.push(node_index);
    //             }
    //             _ => {
    //                 // 正常的中间节点
    //             }
    //         }
    //     }
    //
    //     dbg!(&start_nodes);
    //
    //     if self.graph.node_count() == 1 {
    //         start_nodes.push(self.graph.node_indices().next().ok_or_else(|| {
    //             GraphError::NodeNotFound("failed to find start node".to_string())
    //         })?);
    //         end_nodes.push(
    //             self.graph.node_indices().next().ok_or_else(|| {
    //                 GraphError::NodeNotFound("failed to find end node".to_string())
    //             })?,
    //         );
    //     }
    //
    //     // 如果有孤立节点，抛出异常
    //     if self.graph.node_count() > 1 && !isolated_nodes.is_empty() {
    //         return Err(GraphError::IsolatedNodes(format!("{:?}", isolated_nodes)));
    //     }
    //
    //     if start_nodes.len() == 0 {
    //         return Err(GraphError::NoStartNode);
    //     }
    //
    //     // 目前不支持多个开始节点，先校验住
    //     if start_nodes.len() > 1 {
    //         return Err(GraphError::MultipleStartNodes);
    //     }
    //
    //
    //     // 添加开始节点
    //     let start_node_ref = self.try_add_node(GraphStartHandler)?;
    //
    //     // 将开始处理器连接到所有原始开始节点
    //     for start_node_index in start_nodes {
    //         let original_start_node =
    //             self.get_node_ref(start_node_index.into()).ok_or_else(|| {
    //                 GraphError::NodeNotFound(format!(
    //                     "Start node({:?}) not found ",
    //                     start_node_index
    //                 ))
    //             })?;
    //         // 直接添加，不校验
    //         self.graph.add_edge(
    //             start_node_ref.index.into(),
    //             original_start_node.index.into(),
    //             GraphEdge::Direct(start_node_ref.output_type, original_start_node.input_type),
    //         );
    //     }
    //
    //     let end_handler = GraphEndHandler::new(self.finish_handler.take());
    //     let end_node_ref = self.try_add_node(end_handler)?;
    //
    //     // 将所有原始结束节点连接到结束处理器
    //     for end_node_index in end_nodes {
    //         let original_end_node = self.get_node_ref(end_node_index.into()).ok_or_else(|| {
    //             GraphError::NodeNotFound(format!("End node({:?}) not found ", end_node_index))
    //         })?;
    //         // 创建一个通用的连接，因为结束处理器接受 AnyType
    //         self.graph.add_edge(
    //             original_end_node.index.into(),
    //             end_node_ref.index.into(),
    //             GraphEdge::Direct(original_end_node.output_type, end_node_ref.input_type),
    //         );
    //     }
    //
    //     // 添加错误节点
    //     let error_handler = GraphErrorHandler::new(self.error_handler.take());
    //     let error_node_ref = self.try_add_node(error_handler)?;
    //     for node_index in all_nodes {
    //         let node = self.get_node_ref(node_index.into()).ok_or_else(|| {
    //             GraphError::NodeNotFound(format!("Error node({:?}) not found ", node_index))
    //         })?;
    //         self.graph.add_edge(
    //             node.index.into(),
    //             error_node_ref.index.into(),
    //             GraphEdge::Virtual,
    //         );
    //     }
    //
    //     Ok(CompiledGraph::new(
    //         self.name,
    //         self.graph,
    //         start_node_ref,
    //         end_node_ref,
    //         error_node_ref,
    //         self.name_2_index_mapping,
    //     ))
    // }
}

#[cfg(test)]
mod tests {
    use crate::graph::builtin_handlers::func::FnHandler;
    use crate::graph::{CompiledGraph, GraphError, NodeRef, RunGraph};

    fn assert_graph(graph: Result<CompiledGraph<(), ()>, GraphError>, expected: NodeRef) {
        if let Ok(compiled_graph) = graph {
            let start_node = compiled_graph.start_node();
            assert_eq!(compiled_graph.node_name(start_node.index), "__START__");
            assert_eq!(
                compiled_graph.next_node(start_node.index).unwrap().index,
                expected.index
            );
        }
    }

    #[test]
    fn test_should_return_error_if_cannot_find_start_node() {
        let mut graph = super::StateGraph::<(), ()>::new();
        let handler_a = FnHandler::new("a", |_: &mut (), _: ()| async move { Ok(()) });
        let handler_b = FnHandler::new("b", |_: &mut (), _: ()| async move { Ok(()) });
        let handler_c = FnHandler::new("c", |_: &mut (), _: ()| async move { Ok(()) });

        let a = graph.add_node(handler_a).unwrap();
        let b = graph.add_node(handler_b).unwrap();
        let c = graph.add_node(handler_c).unwrap();

        graph.connect(&a, &b).unwrap();
        graph.connect(&b, &c).unwrap();
        graph.connect(&c, &a).unwrap();

        let compiled = graph.compile();
        assert!(compiled.is_err());
        if let Err(e) = compiled {
            assert!(matches!(e, GraphError::NoStartNode))
        }
    }

    #[test]
    fn test_should_add_start_node() {
        let mut graph = super::StateGraph::<(), ()>::new();
        let handler_a = FnHandler::new("a", |_: &mut (), _: ()| async move { Ok(()) });
        let handler_b = FnHandler::new("b", |_: &mut (), _: ()| async move { Ok(()) });
        let handler_c = FnHandler::new("c", |_: &mut (), _: ()| async move { Ok(()) });

        let a = graph.add_node(handler_a).unwrap();
        let b = graph.add_node(handler_b).unwrap();
        let c = graph.add_node(handler_c).unwrap();

        graph.connect(&a, &b).unwrap();
        graph.connect(&b, &c).unwrap();

        let compiled = graph.compile();
        assert!(compiled.is_ok());
        assert_graph(compiled, a.get_ref());
    }

    #[test]
    fn test_should_allow_single_node() {
        let mut graph = super::StateGraph::<(), ()>::new();
        let handler_a = FnHandler::new("a", |_: &mut (), _: ()| async move { Ok(()) });

        let a = graph.add_node(handler_a).unwrap();

        let compiled = graph.compile();
        assert!(compiled.is_ok());
        assert_graph(compiled, a.get_ref());
    }

    #[test]
    fn test_should_error_if_exists_multi_start_node() {
        let mut graph = super::StateGraph::<(), ()>::new();
        let handler_a = FnHandler::new("a", |_: &mut (), _: ()| async move { Ok(()) });
        let handler_b = FnHandler::new("b", |_: &mut (), _: ()| async move { Ok(()) });

        graph.add_node(handler_a).unwrap();
        graph.add_node(handler_b).unwrap();

        let compiled = graph.compile();
        assert!(compiled.is_err());
    }

    #[test]
    fn test_should_error_if_duplicate_node() {
        let mut graph = super::StateGraph::<(), ()>::new();
        let handler_a = FnHandler::new("a", |_: &mut (), _: ()| async move { Ok(()) });
        let handler_b = FnHandler::new("a", |_: &mut (), _: ()| async move { Ok(()) });
        graph.add_node(handler_a).unwrap();
        let b = graph.add_node(handler_b);
        assert!(b.is_err());
    }
}
