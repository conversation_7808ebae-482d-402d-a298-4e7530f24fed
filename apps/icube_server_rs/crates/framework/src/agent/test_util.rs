use crate::core::error::{Agent<PERSON>rror, ToolError};
use crate::core::provider::model::MockModelProvider;
use crate::prompt::{PromptError, PromptFormatter, PromptProvider};

use std::borrow::Cow;

use crate::core::event::Event;
use crate::core::message::{Message, MessageContents};
use crate::core::task::TaskControl;
use crate::core::tool::{CallToolResponse, DynTool, ToolCallingContext, ToolResult};
use crate::ioc::IOCContainer;
use crate::tool::tool::{IntoDynTool, PinBoxToolStreamParam};
use crate::tool::tool_box::{ToolDefinitionTag, Toolbox};
use crate::FunctionDefinition;

use async_trait::async_trait;
use indexmap::IndexMap;
use serde_json::{json, Value};
use tokio::sync::mpsc::UnboundedSender;

use super::context::{Calling<PERSON>ontext, Create<PERSON>ontextParams, ReActAgentContext, SessionRef};
use crate::core::llm::{LLMRunConfig, PinBoxLLMStream, LLM};
use crate::llm::error::LLMError;
use crate::llm::GenerateResult;
use std::sync::Arc;

impl<C: Default + 'static> Default for CallingContext<C> {
    fn default() -> Self {
        Self::new(
            SessionRef::default(),
            Arc::new(ReActAgentContext::default()),
            None,
            Arc::new(IOCContainer::new()),
            Arc::new(C::default()),
            None,
        )
    }
}

struct MockLLM;

impl MockLLM {
    pub fn new() -> Self {
        Self {}
    }
}

#[async_trait]
impl LLM for MockLLM {
    type LLMConfig = LLMRunConfig;

    fn name(&self) -> &'static str {
        "MockLLM"
    }

    async fn generate(&self, _messages: &[Message]) -> Result<GenerateResult, LLMError> {
        todo!()
    }

    async fn stream(&self, _messages: &[Message]) -> Result<PinBoxLLMStream, LLMError> {
        todo!()
    }
}

impl<C> Default for ReActAgentContext<C> {
    fn default() -> Self {
        struct TmpPromptProvider;
        #[async_trait]
        impl PromptProvider for TmpPromptProvider {
            async fn get_default_prompt_template(
                &self,
            ) -> Result<Arc<dyn PromptFormatter>, PromptError> {
                todo!()
            }

            async fn get_prompt_template(
                &self,
                _agent_name: &str,
            ) -> Result<Arc<dyn PromptFormatter>, PromptError> {
                todo!()
            }

            fn as_any(&self) -> &dyn std::any::Any {
                todo!()
            }
        }
        Self {
            name: Cow::from("default"),
            llm: Arc::new(MockLLM::new()),
            llm_provider: Arc::new(MockModelProvider::new()),
            prompt_provider: Arc::new(TmpPromptProvider),
            toolbox: None,
            agents: IndexMap::new(),
            service_registry: Arc::new(IOCContainer::new()),
            history_recorder: None,
        }
    }
}
/// Builder for creating test contexts with sensible defaults
/// 用于创建测试上下文的构建器，提供合理的默认值
pub struct TestContextBuilder<Config> {
    pub(crate) session_ref: Option<SessionRef>,
    pub(crate) agent_ref: Option<Arc<ReActAgentContext<Config>>>,
    pub(crate) event_bus: Option<UnboundedSender<Event>>,
    pub(crate) container: Option<Arc<IOCContainer>>,
    pub(crate) config: Option<Arc<Config>>,
    pub(crate) toolbox: Option<Arc<Toolbox>>,
    pub(crate) ctrl: Option<TaskControl>,
    pub(crate) initial_message: Option<MessageContents>,
}

impl<Config: Default> Default for TestContextBuilder<Config> {
    fn default() -> Self {
        Self {
            session_ref: None,
            agent_ref: None,
            event_bus: None,
            container: None,
            config: None,
            toolbox: None,
            ctrl: None,
            initial_message: None,
        }
    }
}

impl<Config: Default + 'static> TestContextBuilder<Config> {
    /// 创建新的测试上下文构建器
    pub fn new() -> Self {
        Self::default()
    }

    /// 设置会话引用
    pub fn with_session_ref(mut self, session_ref: SessionRef) -> Self {
        self.session_ref = Some(session_ref);
        self
    }

    /// 设置任务ID（简化的会话引用设置）
    pub fn with_task_id(mut self, task_id: impl Into<String>) -> Self {
        let task_id = task_id.into();
        self.session_ref = Some(SessionRef::new(
            task_id.clone(),
            format!("{}_session", task_id),
            format!("{}_message", task_id),
            format!("{}_user_message", task_id),
            vec![],
        ));
        self
    }

    /// 设置代理上下文
    pub fn with_agent_ref(mut self, agent_ref: Arc<ReActAgentContext<Config>>) -> Self {
        self.agent_ref = Some(agent_ref);
        self
    }

    /// 设置代理名称（简化的代理上下文设置）
    pub fn with_agent_name(mut self, name: impl Into<Cow<'static, str>>) -> Self {
        let agent_context = ReActAgentContext {
            name: name.into(),
            ..Default::default()
        };
        self.agent_ref = Some(Arc::new(agent_context));
        self
    }

    /// 设置事件总线
    pub fn with_event_bus(mut self, event_bus: UnboundedSender<Event>) -> Self {
        self.event_bus = Some(event_bus);
        self
    }

    /// 设置IOC容器
    pub fn with_container(mut self, container: Arc<IOCContainer>) -> Self {
        self.container = Some(container);
        self
    }

    /// 设置配置
    pub fn with_config(mut self, config: Arc<Config>) -> Self {
        self.config = Some(config);
        self
    }

    /// 设置工具箱
    pub fn with_toolbox(mut self, toolbox: Arc<Toolbox>) -> Self {
        self.toolbox = Some(toolbox);
        self
    }

    /// 设置任务控制
    pub fn with_task_control(mut self, ctrl: TaskControl) -> Self {
        self.ctrl = Some(ctrl);
        self
    }

    /// 设置初始消息
    pub fn with_initial_message(mut self, message: MessageContents) -> Self {
        self.initial_message = Some(message);
        self
    }

    /// 构建测试上下文（同步版本，使用默认工具定义）
    pub fn build(self) -> CallingContext<Config> {
        CallingContext::new(
            self.session_ref.unwrap_or_default(),
            self.agent_ref.unwrap_or_else(|| {
                let c = ReActAgentContext {
                    toolbox: self.toolbox.clone(),
                    ..Default::default()
                };
                Arc::new(c)
            }),
            self.event_bus,
            self.container
                .unwrap_or_else(|| Arc::new(IOCContainer::new())),
            self.config.unwrap_or_else(|| Arc::new(Config::default())),
            self.toolbox,
        )
    }

    /// 构建测试上下文（异步版本，初始化工具定义）
    pub async fn build_async(self) -> Result<CallingContext<Config>, AgentError> {
        CallingContext::create(CreateContextParams {
            session_ref: self.session_ref.unwrap_or_default(),
            agent_ref: self.agent_ref.unwrap_or_else(|| {
                let c = ReActAgentContext {
                    toolbox: self.toolbox.clone(),
                    ..Default::default()
                };
                Arc::new(c)
            }),
            event_bus: self.event_bus,
            container: self
                .container
                .unwrap_or_else(|| Arc::new(IOCContainer::new())),
            config: self.config.unwrap_or_else(|| Arc::new(Config::default())),
            toolbox: self.toolbox,
            ctrl: self.ctrl.unwrap_or_default(),
            initial_message: self.initial_message,
        })
        .await
    }
}

/// 创建测试上下文的便捷函数
/// 为最常见的测试场景提供快速创建方法
pub fn create_test_context<Config: Default + 'static>() -> CallingContext<Config> {
    TestContextBuilder::new().build()
}

pub fn create_test_container() -> IOCContainer {
    IOCContainer::new()
}

/// 创建带有任务ID的测试上下文
pub fn create_test_context_with_task_id<Config: Default + 'static>(
    task_id: impl Into<String>,
) -> CallingContext<Config> {
    TestContextBuilder::new().with_task_id(task_id).build()
}

/// 创建带有工具箱的测试上下文（异步版本）
pub async fn create_test_context_with_toolbox<Config: Default + 'static>(
    toolbox: Arc<Toolbox>,
) -> Result<CallingContext<Config>, AgentError> {
    TestContextBuilder::new()
        .with_toolbox(toolbox)
        .build_async()
        .await
}

/// 简单的测试工具实现
/// 用于测试场景的基础工具结构体
#[derive(Debug, Clone, Default)]
pub struct TestTool {
    pub name: String,
    pub description: String,
    pub response: Value,
    pub should_handle_error: bool,
}

impl TestTool {
    /// 创建新的测试工具
    pub fn new(name: impl Into<String>, description: impl Into<String>) -> Self {
        Self {
            name: name.into(),
            description: description.into(),
            should_handle_error: false,
            response: json!({"success": true, "message": "Test tool executed successfully"}),
        }
    }

    pub fn with_handle_error(mut self, should_handle_error: bool) -> Self {
        self.should_handle_error = should_handle_error;
        self
    }

    pub fn with_response(mut self, response: Value) -> Self {
        self.response = response;
        self
    }
}

impl IntoDynTool for TestTool {
    fn into_dyn(self) -> Arc<dyn DynTool> {
        Arc::new(self)
    }
}

#[async_trait]
impl DynTool for TestTool {
    fn name(&self) -> Cow<str> {
        Cow::Borrowed(&self.name)
    }

    fn tag(&self) -> ToolDefinitionTag {
        ToolDefinitionTag::Normal
    }

    fn description(&self) -> Cow<str> {
        Cow::Borrowed(&self.description)
    }

    async fn handle_error(
        &self,
        _ctx: ToolCallingContext,
        _param: Option<Value>,
        err: LLMError,
    ) -> ToolResult<CallToolResponse> {
        if self.should_handle_error {
            Ok(CallToolResponse::success(
                self.response.clone(),
                self.response.clone(),
            ))
        } else {
            Err(ToolError::from(err))
        }
    }

    async fn call_tool(
        &self,
        _ctx: ToolCallingContext,
        _param: Option<Value>,
    ) -> ToolResult<CallToolResponse> {
        Ok(CallToolResponse::success(
            self.response.clone(),
            self.response.clone(),
        ))
    }

    async fn call_tool_stream(
        &self,
        _ctx: ToolCallingContext,
        _stream: PinBoxToolStreamParam,
    ) -> ToolResult<()> {
        Ok(())
    }

    fn schema(&self) -> Value {
        json!({
            "type": "object",
            "properties": {},
            "required": []
        })
    }

    async fn definition(&self, _ctx: ToolCallingContext) -> Vec<FunctionDefinition> {
        vec![FunctionDefinition {
            name: self.name.clone(),
            description: self.description.clone(),
            parameters: self.schema(),
        }]
    }
}

/// 快速创建测试工具的辅助函数
pub fn create_test_tool(name: impl Into<String>, description: impl Into<String>) -> TestTool {
    TestTool::new(name, description)
}

/// 创建带有自定义响应的测试工具
pub fn create_test_tool_with_response(
    name: impl Into<String>,
    description: impl Into<String>,
    response: Value,
) -> TestTool {
    TestTool::new(name, description).with_response(response)
}

/// 创建包含单个测试工具的工具箱
pub fn create_toolbox_with_test_tool(
    name: impl Into<String>,
    description: impl Into<String>,
) -> Result<Arc<Toolbox>, crate::core::error::ToolError> {
    let mut toolbox = Toolbox::new();
    let tool = create_test_tool(name, description);
    toolbox.add_tool(tool)?;
    Ok(Arc::new(toolbox))
}

/// 创建包含多个测试工具的工具箱
pub fn create_toolbox_with_tools(
    tools: Vec<TestTool>,
) -> Result<Arc<Toolbox>, crate::core::error::ToolError> {
    let mut toolbox = Toolbox::new();
    for tool in tools {
        toolbox.add_tool(tool)?;
    }
    Ok(Arc::new(toolbox))
}

/// 创建带有测试工具的测试上下文（异步版本）
pub async fn create_test_context_with_test_tool<Config: Default + 'static>(
    tool_name: impl Into<String>,
    tool_description: impl Into<String>,
) -> Result<CallingContext<Config>, AgentError> {
    let toolbox = create_toolbox_with_test_tool(tool_name, tool_description).unwrap();
    create_test_context_with_toolbox(toolbox).await
}
