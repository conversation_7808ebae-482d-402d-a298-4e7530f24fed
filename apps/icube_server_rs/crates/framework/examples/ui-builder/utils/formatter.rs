use framework::core::event::Event;
use framework::core::message::ToolCall;
use framework::core::tool::Tool;
use framework::graph::builtin_action::interrupt::InterruptReason;
use serde_json::Value;

use crate::tools::filesystem::{
    CreateFile, CreateRequirement, ListFolder, ReadFile, RunTerminalCommand,
};

pub fn format_tool_call(call: &ToolCall) -> String {
    let tool_name = &call.function.name;
    let arguments = &call.function.arguments;

    let thought = if let Some(thought) = extract_string_arg(arguments, "thought") {
        thought
    } else {
        "".to_string()
    };

    match tool_name.as_str() {
        CreateFile::NAME => {
            if let Some(file_path) = extract_string_arg(arguments, "filePath") {
                let filename = std::path::Path::new(&file_path)
                    .file_name()
                    .and_then(|name| name.to_str())
                    .unwrap_or(&file_path);
                format!("✨ Creating → {} [{}]", filename, thought)
            } else {
                "✨ Creating new file".to_string()
            }
        }
        ReadFile::NAME => {
            if let Some(file_path) = extract_string_arg(arguments, "filePath") {
                let filename = std::path::Path::new(&file_path)
                    .file_name()
                    .and_then(|name| name.to_str())
                    .unwrap_or(&file_path);
                format!("👁  Reading → {} [{}]", filename, thought)
            } else {
                "👁  Reading file".to_string()
            }
        }
        ListFolder::NAME => {
            if let Some(path) = extract_string_arg(arguments, "path") {
                let dir_name = std::path::Path::new(&path)
                    .file_name()
                    .and_then(|name| name.to_str())
                    .unwrap_or(&path);
                if let Some(max_depth) = extract_number_arg(arguments, "maxDepth") {
                    format!(
                        "🗂  Exploring → {} (depth: {}) [{}]",
                        dir_name, max_depth, thought
                    )
                } else {
                    format!("🗂  Exploring → {} [{}]", dir_name, thought)
                }
            } else {
                "🗂  Exploring directory".to_string()
            }
        }
        RunTerminalCommand::NAME => {
            if let Some(command) = extract_string_arg(arguments, "command") {
                if let Some(args) = extract_array_arg(arguments, "args") {
                    if args.is_empty() {
                        format!("⚡ Running → {} [{}]", command, thought)
                    } else {
                        format!("⚡ Running → {} {} [{}]", command, args.join(" "), thought)
                    }
                } else {
                    format!("⚡ Running → {} [{}]", command, thought)
                }
            } else {
                "⚡ Running command".to_string()
            }
        }
        CreateRequirement::NAME => {
            if let Some(file_path) = extract_string_arg(arguments, "filePath") {
                let filename = std::path::Path::new(&file_path)
                    .file_name()
                    .and_then(|name| name.to_str())
                    .unwrap_or(&file_path);
                format!("📋 Documenting → {} [{}]", filename, thought)
            } else {
                "📋 Documenting requirements".to_string()
            }
        }
        _ => {
            format!("🛠  Invoking → {} [{}]", tool_name, thought)
        }
    }
}

/// Format event output with Claude Code CLI-inspired styling
pub fn format_event(event: &Event) -> String {
    match event {
        Event::NewQueryReceived { query } => {
            format!("🎯 Query → {}", query)
        }
        Event::AskUser { question } => {
            format!("💭 Question → {}", question)
        }
        Event::HumanInputReceived {
            response,
            original_context_id,
        } => {
            format!("💬 Input [{}] → {}", original_context_id, response)
        }
        Event::WaitingForUserReply => "⏸  Awaiting response...".to_string(),
        Event::ProcessingComplete { output: _ } => "✨ Complete".to_string(),
        Event::ResponseGenerated {
            agent_name,
            agent_run_id,
            response_text,
        } => {
            let avatar = {
                if agent_name == "product_manager" {
                    "👩‍💼"
                } else if agent_name == "front_end" {
                    "👨‍💻"
                } else if agent_name == "supervisor" {
                    "🧑‍💼"
                } else {
                    "🤖"
                }
            };
            format!("{} {} {} → {}", avatar, agent_name, agent_run_id, response_text)
        }
        Event::ResponseSentSuccessfully {
            original_context_id,
        } => {
            format!("📡 Delivered [{}]", original_context_id)
        }
        Event::ErrorOccurred { error, turns } => {
            format!("💥 Error → {:?} [{}]", error, turns)
        }
        Event::ShutdownAgent => "🔴 Shutdown".to_string(),
        Event::Interrupt(interrupt) => match &interrupt.reason {
            InterruptReason::AskUser(question) => {
                let msg = question
                    .message
                    .clone()
                    .unwrap_or_else(|| "continue? (y/n)".to_string());
                format!("⚠️ interrupt: [{}] {}", question.agent_name, msg)
            }
            _ => "⚠️ unknown interrupt".to_string(),
        },
        _ => "⚠️ unknown event".to_string(),
    }
}

fn extract_string_arg(arguments: &Value, key: &str) -> Option<String> {
    arguments
        .as_object()?
        .get(key)?
        .as_str()
        .map(|s| s.to_string())
}

fn extract_number_arg(arguments: &Value, key: &str) -> Option<u64> {
    arguments.as_object()?.get(key)?.as_u64()
}

fn extract_array_arg(arguments: &Value, key: &str) -> Option<Vec<String>> {
    arguments
        .as_object()?
        .get(key)?
        .as_array()?
        .iter()
        .map(|v| v.as_str().map(|s| s.to_string()))
        .collect()
}
